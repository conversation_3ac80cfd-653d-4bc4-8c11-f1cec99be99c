import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from 'react-query';
import { toast } from 'react-hot-toast';
import { apiService } from '../services/apiService';
import {
  CogIcon,
  MapPinIcon,
  TruckIcon,
  EnvelopeIcon,
  BuildingOfficeIcon,
} from '@heroicons/react/24/outline';

const settingIcons = {
  restaurant_search_radius: MapPinIcon,
  max_delivery_distance: TruckIcon,
  app_name: BuildingOfficeIcon,
  support_email: EnvelopeIcon,
};

const settingLabels = {
  restaurant_search_radius: 'Restaurant Search Radius (km)',
  max_delivery_distance: 'Maximum Delivery Distance (km)',
  app_name: 'Application Name',
  support_email: 'Support Email',
};

function SettingCard({ settingKey, setting, onUpdate }) {
  const [isEditing, setIsEditing] = useState(false);
  const [value, setValue] = useState(setting.value);
  const Icon = settingIcons[settingKey] || CogIcon;

  const updateMutation = useMutation(
    (newValue) => apiService.updateSetting(settingKey, newValue),
    {
      onSuccess: () => {
        toast.success('Setting updated successfully!');
        setIsEditing(false);
        onUpdate();
      },
      onError: (error) => {
        toast.error(error.error || 'Failed to update setting');
        setValue(setting.value); // Reset to original value
      },
    }
  );

  const handleSave = () => {
    if (value.trim() === '') {
      toast.error('Setting value cannot be empty');
      return;
    }
    updateMutation.mutate(value.trim());
  };

  const handleCancel = () => {
    setValue(setting.value);
    setIsEditing(false);
  };

  return (
    <div className="card">
      <div className="flex items-start space-x-4">
        <div className="flex-shrink-0">
          <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
            <Icon className="h-6 w-6 text-blue-600" />
          </div>
        </div>
        
        <div className="flex-1 min-w-0">
          <h3 className="text-lg font-medium text-gray-900 mb-1">
            {settingLabels[settingKey] || settingKey}
          </h3>
          <p className="text-sm text-gray-500 mb-3">{setting.description}</p>
          
          {isEditing ? (
            <div className="space-y-3">
              <input
                type="text"
                value={value}
                onChange={(e) => setValue(e.target.value)}
                className="input w-full"
                placeholder="Enter value..."
              />
              <div className="flex space-x-2">
                <button
                  onClick={handleSave}
                  disabled={updateMutation.isLoading}
                  className="btn-primary text-sm"
                >
                  {updateMutation.isLoading ? 'Saving...' : 'Save'}
                </button>
                <button
                  onClick={handleCancel}
                  className="btn-secondary text-sm"
                >
                  Cancel
                </button>
              </div>
            </div>
          ) : (
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <span className="text-lg font-semibold text-gray-900">{setting.value}</span>
                {settingKey.includes('radius') || settingKey.includes('distance') ? (
                  <span className="text-sm text-gray-500">km</span>
                ) : null}
              </div>
              <button
                onClick={() => setIsEditing(true)}
                className="btn-secondary text-sm"
              >
                Edit
              </button>
            </div>
          )}
          
          <p className="text-xs text-gray-400 mt-2">
            Last updated: {new Date(setting.updatedAt).toLocaleString()}
          </p>
        </div>
      </div>
    </div>
  );
}

function Settings() {
  const queryClient = useQueryClient();
  
  const { data: settingsData, isLoading, error } = useQuery(
    'admin-settings',
    apiService.getSettings
  );

  const handleSettingUpdate = () => {
    queryClient.invalidateQueries('admin-settings');
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <p className="text-red-600">Failed to load settings: {error.message}</p>
      </div>
    );
  }

  const settings = settingsData?.settings || {};

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">System Settings</h1>
          <p className="text-gray-600">Manage application configuration and preferences</p>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {Object.entries(settings).map(([key, setting]) => (
          <SettingCard
            key={key}
            settingKey={key}
            setting={setting}
            onUpdate={handleSettingUpdate}
          />
        ))}
      </div>

      <div className="card bg-blue-50 border-blue-200">
        <div className="flex items-start space-x-3">
          <div className="flex-shrink-0">
            <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
              <CogIcon className="h-5 w-5 text-blue-600" />
            </div>
          </div>
          <div>
            <h3 className="text-sm font-medium text-blue-900 mb-1">Important Notes</h3>
            <ul className="text-sm text-blue-800 space-y-1">
              <li>• Restaurant Search Radius controls how far customers can see restaurants</li>
              <li>• Changes take effect immediately for new requests</li>
              <li>• Existing cached data may take a few minutes to update</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}

export default Settings;
