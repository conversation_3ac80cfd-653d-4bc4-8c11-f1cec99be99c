import 'package:flutter/material.dart';
import '../models/user.dart';
import '../services/api_service.dart';

class AuthProvider with ChangeNotifier {
  User? _user;
  RestaurantProfile? _profile;
  bool _isLoading = false;
  String? _error;
  bool _isAuthenticated = false;

  User? get user => _user;
  RestaurantProfile? get profile => _profile;
  bool get isLoading => _isLoading;
  String? get error => _error;
  bool get isAuthenticated => _isAuthenticated;
  bool get hasProfile => _profile != null;

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String? error) {
    _error = error;
    notifyListeners();
  }

  Future<void> checkAuthStatus() async {
    _setLoading(true);

    try {
      final token = await ApiService.getToken();

      if (token == null || token.isEmpty) {
        print('🔍 No token found, user not authenticated');
        _isAuthenticated = false;
        _user = null;
        _profile = null;
        _setLoading(false);
        return;
      }

      print('🔍 Token found, checking user status...');
      final result = await ApiService.getCurrentUser();

      if (result['success'] == true && result['data'] != null) {
        print('✅ User data retrieved successfully');
        try {
          _user = User.fromJson(result['data']['user']);
          _isAuthenticated = true;

          // Handle profile data
          if (result['data']['profile'] != null) {
            _profile = RestaurantProfile.fromJson(result['data']['profile']);
            print('✅ Profile data loaded');
          } else {
            _profile = null;
            print('ℹ️ No profile data found');
          }
        } catch (parseError) {
          print('❌ Error parsing user data: $parseError');
          _isAuthenticated = false;
          _user = null;
          _profile = null;
          await ApiService.removeToken();
        }
      } else {
        print('❌ Failed to get user data: ${result['error']}');
        _isAuthenticated = false;
        _user = null;
        _profile = null;
        await ApiService.removeToken();
      }
    } catch (e) {
      print('❌ Auth check error: $e');
      _isAuthenticated = false;
      _user = null;
      _profile = null;
      await ApiService.removeToken();
    } finally {
      _setLoading(false);
      notifyListeners();
    }
  }

  Future<void> loadCurrentUser() async {
    try {
      _setLoading(true);
      _setError(null);

      final result = await ApiService.getCurrentUser();
      if (result['success']) {
        _user = User.fromJson(result['data']['user']);
        _isAuthenticated = true;

        // Handle profile data
        if (result['data']['profile'] != null) {
          _profile = RestaurantProfile.fromJson(result['data']['profile']);
        } else {
          _profile = null;
        }
      } else {
        _setError(result['error']);
        // If token is invalid, logout
        await logout();
      }
    } catch (e) {
      _setError('Failed to load user: $e');
      // If token is invalid, logout
      await logout();
    } finally {
      _setLoading(false);
    }
  }

  Future<bool> register({
    required String email,
    required String password,
  }) async {
    _setLoading(true);
    _setError(null);

    try {
      final result = await ApiService.register(
        email: email,
        password: password,
      );

      if (result['success']) {
        _user = User.fromJson(result['data']['user']);
        _isAuthenticated = true;
        _profile = null; // No profile yet after registration

        // Notify listeners immediately after setting authentication state
        notifyListeners();
        _setLoading(false);
        return true;
      } else {
        _setError(result['error']);
        _setLoading(false);
        return false;
      }
    } catch (e) {
      _setError('Registration failed: $e');
      _setLoading(false);
      return false;
    }
  }

  Future<bool> login({required String email, required String password}) async {
    _setLoading(true);
    _setError(null);

    try {
      final result = await ApiService.login(email: email, password: password);

      if (result['success']) {
        _user = User.fromJson(result['data']['user']);
        _isAuthenticated = true;

        // Handle profile data from login response
        if (result['data']['profile'] != null) {
          _profile = RestaurantProfile.fromJson(result['data']['profile']);
        } else {
          _profile = null;
        }

        // Notify listeners immediately after setting authentication state
        notifyListeners();
        _setLoading(false);
        return true;
      } else {
        _setError(result['error']);
        _setLoading(false);
        return false;
      }
    } catch (e) {
      _setError('Login failed: $e');
      _setLoading(false);
      return false;
    }
  }

  Future<void> _loadProfile() async {
    try {
      final result = await ApiService.getProfile();
      if (result['success']) {
        _profile = RestaurantProfile.fromJson(result['data']['profile']);
      }
    } catch (e) {
      print('Load profile error: $e');
      // Profile might not exist yet, which is fine
    }
  }

  Future<bool> completeProfile({
    required String restaurantName,
    required String description,
    required String phone,
    required String address,
    required double latitude,
    required double longitude,
    required String cuisineType,
  }) async {
    _setLoading(true);
    _setError(null);

    try {
      final result = await ApiService.completeProfile(
        restaurantName: restaurantName,
        description: description,
        phone: phone,
        address: address,
        latitude: latitude,
        longitude: longitude,
        cuisineType: cuisineType,
      );

      if (result['success']) {
        _profile = RestaurantProfile.fromJson(result['data']['profile']);

        // Notify listeners immediately after setting profile
        notifyListeners();
        _setLoading(false);
        return true;
      } else {
        _setError(result['error']);
        _setLoading(false);
        return false;
      }
    } catch (e) {
      _setError('Profile completion failed: $e');
      _setLoading(false);
      return false;
    }
  }

  Future<bool> toggleRestaurantStatus({required bool isOpen}) async {
    _setLoading(true);
    _setError(null);

    try {
      final result = await ApiService.toggleRestaurantStatus(isOpen: isOpen);

      if (result['success']) {
        if (_profile != null) {
          _profile = _profile!.copyWith(isOpen: result['data']['isOpen']);
        }
        _setLoading(false);
        return true;
      } else {
        _setError(result['error']);
        _setLoading(false);
        return false;
      }
    } catch (e) {
      _setError('Status update failed: $e');
      _setLoading(false);
      return false;
    }
  }

  Future<void> logout() async {
    _setLoading(true);

    await ApiService.logout();

    _user = null;
    _profile = null;
    _isAuthenticated = false;
    _error = null;

    _setLoading(false);
  }

  void clearError() {
    _setError(null);
  }
}
