import 'dart:io';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'firebase_service.dart';
import '../config/firebase_config.dart';

class ImageManagementService {
  static const String baseUrl = 'http://********:3000/api'; // Android emulator
  // static const String baseUrl = 'http://localhost:3000/api'; // iOS simulator
  
  /// Upload restaurant profile image
  static Future<String> uploadRestaurantImage({
    required File imageFile,
    required String restaurantId,
    required String authToken,
    String imageType = 'profile',
  }) async {
    try {
      // First upload to Firebase Storage
      final storagePath = FirebaseStoragePaths.restaurantProfileImage(restaurantId);
      final imageUrl = await FirebaseService.uploadImage(
        imageFile: imageFile,
        path: storagePath,
      );

      // Then update the backend database
      await _updateRestaurantImageInBackend(
        restaurantId: restaurantId,
        imageUrl: imageUrl,
        imageType: imageType,
        authToken: authToken,
      );

      return imageUrl;
    } catch (e) {
      throw Exception('Failed to upload restaurant image: $e');
    }
  }

  /// Upload menu item image
  static Future<String> uploadMenuItemImage({
    required File imageFile,
    required String restaurantId,
    required String menuItemId,
    required String authToken,
  }) async {
    try {
      // First upload to Firebase Storage
      final storagePath = FirebaseStoragePaths.menuItemImage(restaurantId);
      final imageUrl = await FirebaseService.uploadImage(
        imageFile: imageFile,
        path: storagePath,
      );

      // Then update the backend database
      await _updateMenuItemImageInBackend(
        menuItemId: menuItemId,
        imageUrl: imageUrl,
        authToken: authToken,
      );

      return imageUrl;
    } catch (e) {
      throw Exception('Failed to upload menu item image: $e');
    }
  }

  /// Upload image using backend API (alternative method)
  static Future<String> uploadImageViaBackend({
    required File imageFile,
    required String uploadType, // 'restaurant' or 'menu-item'
    required String entityId,
    required String authToken,
    String? imageType,
  }) async {
    try {
      var request = http.MultipartRequest(
        'POST',
        Uri.parse('$baseUrl/upload/$uploadType/$entityId/image'),
      );

      // Add headers
      request.headers['Authorization'] = 'Bearer $authToken';
      request.headers['Content-Type'] = 'multipart/form-data';

      // Add image file
      request.files.add(
        await http.MultipartFile.fromPath('image', imageFile.path),
      );

      // Add image type if provided
      if (imageType != null) {
        request.fields['type'] = imageType;
      }

      // Send request
      final response = await request.send();
      final responseBody = await response.stream.bytesToString();

      if (response.statusCode == 200) {
        final data = json.decode(responseBody);
        return data['imageUrl'];
      } else {
        final error = json.decode(responseBody);
        throw Exception(error['error'] ?? 'Upload failed');
      }
    } catch (e) {
      throw Exception('Failed to upload image via backend: $e');
    }
  }

  /// Delete image
  static Future<void> deleteImage({
    required String imageUrl,
    required String authToken,
  }) async {
    try {
      // Delete from Firebase Storage
      await FirebaseService.deleteImage(imageUrl);

      // Delete from backend (optional - backend can handle cleanup)
      await _deleteImageFromBackend(imageUrl: imageUrl, authToken: authToken);
    } catch (e) {
      throw Exception('Failed to delete image: $e');
    }
  }

  /// Get all restaurant images
  static Future<Map<String, dynamic>> getRestaurantImages({
    required String restaurantId,
    required String authToken,
  }) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/restaurant/$restaurantId/images'),
        headers: {
          'Authorization': 'Bearer $authToken',
          'Content-Type': 'application/json',
        },
      );

      if (response.statusCode == 200) {
        return json.decode(response.body);
      } else {
        final error = json.decode(response.body);
        throw Exception(error['error'] ?? 'Failed to get images');
      }
    } catch (e) {
      throw Exception('Failed to get restaurant images: $e');
    }
  }

  /// Private method to update restaurant image in backend
  static Future<void> _updateRestaurantImageInBackend({
    required String restaurantId,
    required String imageUrl,
    required String imageType,
    required String authToken,
  }) async {
    try {
      final response = await http.patch(
        Uri.parse('$baseUrl/restaurant/$restaurantId/image'),
        headers: {
          'Authorization': 'Bearer $authToken',
          'Content-Type': 'application/json',
        },
        body: json.encode({
          'imageUrl': imageUrl,
          'imageType': imageType,
        }),
      );

      if (response.statusCode != 200) {
        final error = json.decode(response.body);
        throw Exception(error['error'] ?? 'Failed to update restaurant image');
      }
    } catch (e) {
      throw Exception('Failed to update restaurant image in backend: $e');
    }
  }

  /// Private method to update menu item image in backend
  static Future<void> _updateMenuItemImageInBackend({
    required String menuItemId,
    required String imageUrl,
    required String authToken,
  }) async {
    try {
      final response = await http.patch(
        Uri.parse('$baseUrl/menu-items/$menuItemId'),
        headers: {
          'Authorization': 'Bearer $authToken',
          'Content-Type': 'application/json',
        },
        body: json.encode({
          'imageUrl': imageUrl,
        }),
      );

      if (response.statusCode != 200) {
        final error = json.decode(response.body);
        throw Exception(error['error'] ?? 'Failed to update menu item image');
      }
    } catch (e) {
      throw Exception('Failed to update menu item image in backend: $e');
    }
  }

  /// Private method to delete image from backend
  static Future<void> _deleteImageFromBackend({
    required String imageUrl,
    required String authToken,
  }) async {
    try {
      // Extract upload path from Firebase URL for backend deletion
      final uploadPath = _extractUploadPathFromUrl(imageUrl);
      if (uploadPath == null) return;

      final response = await http.delete(
        Uri.parse('$baseUrl/upload/image'),
        headers: {
          'Authorization': 'Bearer $authToken',
          'Content-Type': 'application/json',
        },
        body: json.encode({
          'uploadPath': uploadPath,
        }),
      );

      if (response.statusCode != 200) {
        final error = json.decode(response.body);
        print('Warning: Failed to delete image from backend: ${error['error']}');
      }
    } catch (e) {
      print('Warning: Failed to delete image from backend: $e');
    }
  }

  /// Extract Firebase Storage path from URL
  static String? _extractUploadPathFromUrl(String imageUrl) {
    try {
      final uri = Uri.parse(imageUrl);
      final pathSegments = uri.pathSegments;
      
      // Find the path after '/o/'
      final oIndex = pathSegments.indexOf('o');
      if (oIndex == -1 || oIndex + 1 >= pathSegments.length) {
        return null;
      }
      
      final encodedPath = pathSegments[oIndex + 1];
      return Uri.decodeComponent(encodedPath);
    } catch (e) {
      return null;
    }
  }

  /// Validate image file
  static bool isValidImageFile(File file) {
    final allowedExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp'];
    final fileName = file.path.toLowerCase();
    
    return allowedExtensions.any((ext) => fileName.endsWith(ext));
  }

  /// Get image file size in MB
  static double getImageSizeInMB(File file) {
    final bytes = file.lengthSync();
    return bytes / (1024 * 1024);
  }

  /// Check if image size is within limits
  static bool isImageSizeValid(File file, {double maxSizeMB = 5.0}) {
    return getImageSizeInMB(file) <= maxSizeMB;
  }
}
