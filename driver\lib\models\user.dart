class User {
  final int id;
  final String email;
  final String userType;
  final bool isVerified;
  final bool isActive;

  User({
    required this.id,
    required this.email,
    required this.userType,
    required this.isVerified,
    required this.isActive,
  });

  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      id: json['id'],
      email: json['email'],
      userType: json['userType'] ?? json['user_type'],
      isVerified: json['isVerified'] ?? json['is_verified'] ?? false,
      isActive: json['isActive'] ?? json['is_active'] ?? true,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'email': email,
      'userType': userType,
      'isVerified': isVerified,
      'isActive': isActive,
    };
  }
}

class DriverProfile {
  final int id;
  final int userId;
  final String? fullName;
  final String? phone;
  final String? licenseNumber;
  final String? vehicleType;
  final String? vehiclePlate;
  final bool isAvailable;
  final double? currentLatitude;
  final double? currentLongitude;
  final double rating;
  final int totalDeliveries;
  final DateTime createdAt;
  final DateTime updatedAt;

  DriverProfile({
    required this.id,
    required this.userId,
    this.fullName,
    this.phone,
    this.licenseNumber,
    this.vehicleType,
    this.vehiclePlate,
    required this.isAvailable,
    this.currentLatitude,
    this.currentLongitude,
    required this.rating,
    required this.totalDeliveries,
    required this.createdAt,
    required this.updatedAt,
  });

  factory DriverProfile.fromJson(Map<String, dynamic> json) {
    return DriverProfile(
      id: json['id'],
      userId: json['user_id'],
      fullName: json['full_name'],
      phone: json['phone'],
      licenseNumber: json['license_number'],
      vehicleType: json['vehicle_type'],
      vehiclePlate: json['vehicle_plate'],
      isAvailable: json['is_available'] ?? false,
      currentLatitude: _parseDouble(json['current_latitude']),
      currentLongitude: _parseDouble(json['current_longitude']),
      rating: _parseDouble(json['rating']) ?? 0.0,
      totalDeliveries: _parseInt(json['total_deliveries']) ?? 0,
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: DateTime.parse(json['updated_at']),
    );
  }

  static double? _parseDouble(dynamic value) {
    if (value == null) return null;
    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) {
      return double.tryParse(value);
    }
    return null;
  }

  static int? _parseInt(dynamic value) {
    if (value == null) return null;
    if (value is int) return value;
    if (value is double) return value.toInt();
    if (value is String) {
      return int.tryParse(value);
    }
    return null;
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'full_name': fullName,
      'phone': phone,
      'license_number': licenseNumber,
      'vehicle_type': vehicleType,
      'vehicle_plate': vehiclePlate,
      'is_available': isAvailable,
      'current_latitude': currentLatitude,
      'current_longitude': currentLongitude,
      'rating': rating,
      'total_deliveries': totalDeliveries,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  bool get isProfileCompleted {
    return fullName != null && 
           phone != null && 
           licenseNumber != null && 
           vehicleType != null && 
           vehiclePlate != null;
  }
}
