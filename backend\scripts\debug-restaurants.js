const { pool } = require('../config/database');

async function debugRestaurants() {
  const client = await pool.connect();
  try {
    console.log('🔍 Debugging restaurant visibility...\n');
    
    // Check all restaurants in database
    const allRestaurants = await client.query(`
      SELECT 
        rp.id, rp.restaurant_name, rp.is_open, rp.latitude, rp.longitude,
        u.user_type, u.is_active, u.email
      FROM restaurant_profiles rp
      JOIN users u ON rp.user_id = u.id
      ORDER BY rp.created_at DESC
    `);
    
    console.log('📊 ALL RESTAURANTS IN DATABASE:');
    allRestaurants.rows.forEach(r => {
      const status = r.is_open ? '🟢 OPEN' : '🔴 CLOSED';
      const active = r.is_active ? '✅ ACTIVE' : '❌ INACTIVE';
      const coords = r.latitude && r.longitude ? `(${r.latitude}, ${r.longitude})` : '(no coords)';
      console.log(`  ${r.id}: ${r.restaurant_name} ${status} ${active} ${coords}`);
    });
    
    // Check what customer API would return (public endpoint logic)
    const customerVisible = await client.query(`
      SELECT
        rp.id, rp.restaurant_name, rp.description, rp.cuisine_type,
        rp.address, rp.latitude, rp.longitude, rp.rating, rp.total_orders,
        rp.is_open, rp.created_at,
        u.email
      FROM restaurant_profiles rp
      JOIN users u ON rp.user_id = u.id
      WHERE u.user_type = 'restaurant' AND u.is_active = true AND rp.is_open = true
      ORDER BY rp.rating DESC, rp.total_orders DESC
    `);
    
    console.log(`\n🎯 RESTAURANTS VISIBLE TO CUSTOMERS: ${customerVisible.rows.length}`);
    customerVisible.rows.forEach(r => {
      console.log(`  ${r.id}: ${r.restaurant_name} (rating: ${r.rating}, orders: ${r.total_orders})`);
    });
    
    // Check for any restaurants that should be visible but aren't
    const shouldBeVisible = allRestaurants.rows.filter(r => 
      r.user_type === 'restaurant' && r.is_active && r.is_open
    );
    
    console.log(`\n📈 SHOULD BE VISIBLE: ${shouldBeVisible.length}`);
    console.log(`📱 ACTUALLY VISIBLE: ${customerVisible.rows.length}`);
    
    if (shouldBeVisible.length !== customerVisible.rows.length) {
      console.log('\n⚠️  MISMATCH DETECTED!');
      console.log('Restaurants that should be visible but might not be:');
      shouldBeVisible.forEach(r => {
        const found = customerVisible.rows.find(cv => cv.id === r.id);
        if (!found) {
          console.log(`  ❌ Missing: ${r.restaurant_name} (ID: ${r.id})`);
        }
      });
    } else {
      console.log('\n✅ All restaurants are properly visible!');
    }
    
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    client.release();
    process.exit(0);
  }
}

debugRestaurants();
