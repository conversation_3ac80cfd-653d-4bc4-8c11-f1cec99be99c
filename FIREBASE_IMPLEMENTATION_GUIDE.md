# Firebase Implementation Guide for BRSIMA

This guide explains how Firebase has been integrated into your BRSIMA project and how to use the implemented features.

## 🏗️ Architecture Overview

### Firebase Services Used:
- **Firebase Storage**: For storing restaurant images, menu item images, and other media
- **Firebase Auth**: For secure authentication and authorization
- **Firebase Admin SDK**: For server-side operations in the Node.js backend

### Integration Points:
1. **Backend (Node.js)**: Firebase Admin SDK for server-side operations
2. **Customer App (Flutter)**: Firebase client SDK for image viewing and basic operations
3. **Driver App (Flutter)**: Firebase client SDK for profile images
4. **Restaurant App (Flutter)**: Full Firebase integration for image management
5. **Admin Panel (React)**: Web Firebase SDK for administrative operations

## 📁 Project Structure Changes

### New Files Added:

#### Root Level:
- `firebase.json` - Firebase project configuration
- `storage.rules` - Firebase Storage security rules
- `FIREBASE_SETUP.md` - Manual setup instructions

#### Backend:
- `backend/config/firebase.js` - Firebase Admin SDK configuration
- `backend/routes/upload.js` - Image upload API endpoints
- `backend/services/imageService.js` - Image management service

#### Flutter Apps (Customer/Driver/Restaurant):
- `lib/config/firebase_config.dart` - Firebase client configuration
- `lib/services/firebase_service.dart` - Firebase operations service
- `lib/widgets/image_upload_widget.dart` - Reusable image upload component
- `lib/services/image_management_service.dart` - (Restaurant app only)

## 🔧 Implementation Details

### 1. Image Storage Organization

Firebase Storage is organized as follows:
```
/restaurants/{restaurantId}/
  ├── profile/          # Restaurant profile images
  ├── banner/           # Restaurant banner images
  └── gallery/          # Restaurant gallery images

/menu-items/{restaurantId}/
  └── {filename}        # Menu item images

/drivers/{driverId}/
  ├── profile/          # Driver profile images
  ├── vehicle/          # Vehicle images
  └── license/          # License images

/admin/{category}/
  └── {filename}        # Admin uploaded images

/temp/{userId}/
  └── {filename}        # Temporary uploads
```

### 2. Security Rules

The Firebase Storage security rules implement:
- **Public read access** for all restaurant and menu images
- **Restricted write access** based on user roles and ownership
- **File validation** (image types only, 5MB max size)
- **Role-based permissions** (admin, restaurant, driver, customer)

### 3. Backend API Endpoints

#### Image Upload Endpoints:
- `POST /api/upload/restaurant/:restaurantId/image` - Upload restaurant image
- `POST /api/upload/menu-item/:restaurantId/image` - Upload menu item image
- `POST /api/upload/admin/image` - Admin image upload
- `DELETE /api/upload/image` - Delete image

#### Restaurant Image Management:
- `PATCH /api/restaurant/:restaurantId/image` - Update restaurant image URL
- `GET /api/restaurant/:restaurantId/images` - Get all restaurant images

### 4. Flutter Integration

#### Image Upload Widget Usage:
```dart
ImageUploadWidget(
  initialImageUrl: currentImageUrl,
  uploadPath: FirebaseStoragePaths.restaurantProfileImage(restaurantId),
  onImageUploaded: (imageUrl) {
    // Handle uploaded image URL
    setState(() {
      this.imageUrl = imageUrl;
    });
  },
  width: 200,
  height: 200,
  placeholder: 'Tap to upload restaurant image',
)
```

#### Direct Firebase Service Usage:
```dart
// Upload image
final imageUrl = await FirebaseService.uploadImage(
  imageFile: selectedFile,
  path: 'restaurants/123/profile',
);

// Delete image
await FirebaseService.deleteImage(imageUrl);
```

## 🚀 Usage Examples

### Restaurant App - Upload Menu Item Image

```dart
// In your menu item form
class MenuItemForm extends StatefulWidget {
  @override
  _MenuItemFormState createState() => _MenuItemFormState();
}

class _MenuItemFormState extends State<MenuItemForm> {
  String? menuItemImageUrl;
  
  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Other form fields...
        
        ImageUploadWidget(
          initialImageUrl: menuItemImageUrl,
          uploadPath: FirebaseStoragePaths.menuItemImage(restaurantId),
          onImageUploaded: (imageUrl) {
            setState(() {
              menuItemImageUrl = imageUrl;
            });
          },
          placeholder: 'Upload menu item image',
        ),
        
        // Save button that includes imageUrl in API call
        ElevatedButton(
          onPressed: () => saveMenuItem(),
          child: Text('Save Menu Item'),
        ),
      ],
    );
  }
  
  void saveMenuItem() async {
    // Include menuItemImageUrl in your API call
    final response = await http.post(
      Uri.parse('$baseUrl/menu-items'),
      headers: {'Authorization': 'Bearer $token'},
      body: json.encode({
        'name': nameController.text,
        'description': descriptionController.text,
        'price': priceController.text,
        'imageUrl': menuItemImageUrl, // Firebase Storage URL
      }),
    );
  }
}
```

### Backend - Handle Image Upload

```javascript
// In your menu item creation endpoint
router.post('/menu-items', authenticateToken, async (req, res) => {
  try {
    const { name, description, price, imageUrl } = req.body;
    
    const result = await query(
      `INSERT INTO menu_items (name, description, price, image_url) 
       VALUES ($1, $2, $3, $4) RETURNING *`,
      [name, description, price, imageUrl] // Firebase Storage URL
    );
    
    res.json({ menuItem: result.rows[0] });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});
```

## 🔒 Security Considerations

### 1. Authentication Tokens
- All Firebase operations require proper authentication
- Custom tokens are generated by the backend with user role claims
- Tokens include `userType` and relevant IDs for authorization

### 2. File Validation
- Only image files are allowed (jpg, png, gif, webp)
- Maximum file size is 5MB
- Files are validated both client-side and in security rules

### 3. Access Control
- Restaurant owners can only upload to their own restaurant folders
- Admins have full access to all folders
- Public read access for customer-facing images

## 📊 Database Integration

### Image URL Storage
Images are stored as URLs in your PostgreSQL database:

```sql
-- Restaurant profiles
ALTER TABLE restaurant_profiles 
ADD COLUMN profile_image_url TEXT,
ADD COLUMN banner_image_url TEXT;

-- Menu items (already exists)
-- image_url column in menu_items table
```

### Migration Considerations
- Existing image URLs will continue to work
- New uploads will use Firebase Storage URLs
- You can migrate existing images gradually

## 🧪 Testing

### 1. Unit Testing
Test Firebase services with mock data:
```dart
// Test image upload
test('should upload image successfully', () async {
  final mockFile = File('test_image.jpg');
  final result = await FirebaseService.uploadImage(
    imageFile: mockFile,
    path: 'test/path',
  );
  expect(result, isA<String>());
});
```

### 2. Integration Testing
- Test complete image upload flow
- Verify database updates
- Test security rule enforcement

### 3. Manual Testing Checklist
- [ ] Restaurant can upload profile image
- [ ] Restaurant can upload menu item images
- [ ] Images are publicly accessible
- [ ] Unauthorized users cannot upload
- [ ] File size limits are enforced
- [ ] Invalid file types are rejected

## 🔧 Maintenance

### Regular Tasks:
1. **Monitor Storage Usage**: Check Firebase console for storage quotas
2. **Review Security Rules**: Audit access patterns and update rules as needed
3. **Clean Up Orphaned Files**: Remove unused images from storage
4. **Update Dependencies**: Keep Firebase SDKs updated

### Backup Strategy:
- Firebase Storage has built-in redundancy
- Consider periodic exports for critical images
- Maintain database backups with image URL references

## 📈 Scaling Considerations

### Performance:
- Images are served from Firebase CDN for fast loading
- Implement image resizing for different screen sizes
- Use appropriate image formats (WebP when supported)

### Cost Optimization:
- Monitor Firebase Storage usage and costs
- Implement image compression
- Set up lifecycle rules for temporary files

### Future Enhancements:
- Add image resizing/optimization
- Implement progressive image loading
- Add support for video uploads
- Integrate with Firebase ML for image analysis
