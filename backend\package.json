{"name": "brsima-backend", "version": "1.0.0", "description": "Backend server for BRSIMA system", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "migrate": "node migrations/migrate.js", "migrate:reset": "node migrations/reset.js", "create-admin": "node scripts/create-admin.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["nodejs", "express", "postgresql", "api"], "author": "", "license": "ISC", "dependencies": {"bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "firebase-admin": "^13.4.0", "helmet": "^7.0.0", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "multer": "^2.0.2", "pg": "^8.11.3"}, "devDependencies": {"nodemon": "^3.0.1"}}