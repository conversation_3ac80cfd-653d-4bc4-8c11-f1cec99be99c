import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from 'react-query';
import { useForm } from 'react-hook-form';
import { apiService } from '../services/apiService';
import toast from 'react-hot-toast';
import {
  PlusIcon,
  BuildingStorefrontIcon,
  PhoneIcon,
  MapPinIcon,
  StarIcon,
} from '@heroicons/react/24/outline';

function CreateRestaurantModal({ isOpen, onClose }) {
  const { register, handleSubmit, formState: { errors }, reset } = useForm();
  const queryClient = useQueryClient();

  const createMutation = useMutation(apiService.createRestaurant, {
    onSuccess: () => {
      queryClient.invalidateQueries('restaurants');
      toast.success('Restaurant created successfully!');
      reset();
      onClose();
    },
    onError: (error) => {
      toast.error(error.error || 'Failed to create restaurant');
    },
  });

  const onSubmit = (data) => {
    createMutation.mutate({
      ...data,
      latitude: parseFloat(data.latitude),
      longitude: parseFloat(data.longitude),
    });
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div className="relative top-20 mx-auto p-5 border w-full max-w-2xl shadow-lg rounded-md bg-white">
        <div className="mt-3">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-medium text-gray-900">Create New Restaurant</h3>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600"
            >
              <span className="sr-only">Close</span>
              ×
            </button>
          </div>

          <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700">Email</label>
                <input
                  {...register('email', { 
                    required: 'Email is required',
                    pattern: {
                      value: /^\S+@\S+$/i,
                      message: 'Invalid email address'
                    }
                  })}
                  type="email"
                  className="input-field"
                />
                {errors.email && <p className="text-red-600 text-sm">{errors.email.message}</p>}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700">Password</label>
                <input
                  {...register('password', { 
                    required: 'Password is required',
                    minLength: { value: 6, message: 'Password must be at least 6 characters' }
                  })}
                  type="password"
                  className="input-field"
                />
                {errors.password && <p className="text-red-600 text-sm">{errors.password.message}</p>}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700">Restaurant Name</label>
                <input
                  {...register('restaurantName', { required: 'Restaurant name is required' })}
                  type="text"
                  className="input-field"
                />
                {errors.restaurantName && <p className="text-red-600 text-sm">{errors.restaurantName.message}</p>}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700">Phone</label>
                <input
                  {...register('phone', { required: 'Phone is required' })}
                  type="tel"
                  className="input-field"
                />
                {errors.phone && <p className="text-red-600 text-sm">{errors.phone.message}</p>}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700">Cuisine Type</label>
                <select
                  {...register('cuisineType', { required: 'Cuisine type is required' })}
                  className="input-field"
                >
                  <option value="">Select cuisine type</option>
                  <option value="italian">Italian</option>
                  <option value="chinese">Chinese</option>
                  <option value="indian">Indian</option>
                  <option value="mexican">Mexican</option>
                  <option value="american">American</option>
                  <option value="mediterranean">Mediterranean</option>
                  <option value="japanese">Japanese</option>
                  <option value="thai">Thai</option>
                  <option value="other">Other</option>
                </select>
                {errors.cuisineType && <p className="text-red-600 text-sm">{errors.cuisineType.message}</p>}
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700">Address</label>
              <input
                {...register('address', { required: 'Address is required' })}
                type="text"
                className="input-field"
              />
              {errors.address && <p className="text-red-600 text-sm">{errors.address.message}</p>}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700">Description</label>
              <textarea
                {...register('description')}
                rows={3}
                className="input-field"
                placeholder="Brief description of the restaurant"
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700">Latitude</label>
                <input
                  {...register('latitude', { 
                    required: 'Latitude is required',
                    pattern: {
                      value: /^-?([1-8]?[1-9]|[1-9]0)\.{1}\d{1,6}$/,
                      message: 'Invalid latitude format'
                    }
                  })}
                  type="number"
                  step="any"
                  className="input-field"
                  placeholder="e.g., 40.7128"
                />
                {errors.latitude && <p className="text-red-600 text-sm">{errors.latitude.message}</p>}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700">Longitude</label>
                <input
                  {...register('longitude', { 
                    required: 'Longitude is required',
                    pattern: {
                      value: /^-?([1]?[1-7][1-9]|[1]?[1-8][0]|[1-9]?[0-9])\.{1}\d{1,6}$/,
                      message: 'Invalid longitude format'
                    }
                  })}
                  type="number"
                  step="any"
                  className="input-field"
                  placeholder="e.g., -74.0060"
                />
                {errors.longitude && <p className="text-red-600 text-sm">{errors.longitude.message}</p>}
              </div>
            </div>

            <div className="flex justify-end space-x-3 pt-4">
              <button
                type="button"
                onClick={onClose}
                className="btn-secondary"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={createMutation.isLoading}
                className="btn-primary"
              >
                {createMutation.isLoading ? 'Creating...' : 'Create Restaurant'}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}

function RestaurantCard({ restaurant, onToggleStatus }) {
  const { user, profile } = restaurant;

  return (
    <div className="card">
      <div className="flex items-start justify-between">
        <div className="flex items-start space-x-3">
          <div className="flex-shrink-0">
            <BuildingStorefrontIcon className="h-8 w-8 text-gray-400" />
          </div>
          <div className="flex-1">
            <h3 className="text-lg font-medium text-gray-900">{profile.restaurantName}</h3>
            <p className="text-sm text-gray-600">{user.email}</p>
            {profile.description && (
              <p className="text-sm text-gray-500 mt-1">{profile.description}</p>
            )}
            
            <div className="mt-2 space-y-1">
              <div className="flex items-center text-sm text-gray-600">
                <PhoneIcon className="h-4 w-4 mr-1" />
                {profile.phone}
              </div>
              <div className="flex items-center text-sm text-gray-600">
                <MapPinIcon className="h-4 w-4 mr-1" />
                {profile.address}
              </div>
              <div className="flex items-center text-sm text-gray-600">
                <StarIcon className="h-4 w-4 mr-1" />
                {profile.rating.toFixed(1)} rating • {profile.totalOrders} orders
              </div>
            </div>

            <div className="mt-3 flex items-center space-x-2">
              <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                profile.isOpen ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
              }`}>
                {profile.isOpen ? 'Open' : 'Closed'}
              </span>
              <span className="text-xs text-gray-500 capitalize">
                {profile.cuisineType}
              </span>
            </div>
          </div>
        </div>

        <div className="flex flex-col items-end space-y-2">
          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
            user.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
          }`}>
            {user.isActive ? 'Active' : 'Inactive'}
          </span>
          <button
            onClick={() => onToggleStatus(user.id, !user.isActive)}
            className={`text-xs px-3 py-1 rounded ${
              user.isActive ? 'btn-danger' : 'btn-primary'
            }`}
          >
            {user.isActive ? 'Deactivate' : 'Activate'}
          </button>
        </div>
      </div>
    </div>
  );
}

function Restaurants() {
  const [showCreateModal, setShowCreateModal] = useState(false);
  const queryClient = useQueryClient();

  const { data: restaurants, isLoading, error } = useQuery('restaurants', apiService.getRestaurants);

  const toggleStatusMutation = useMutation(
    ({ userId, isActive }) => apiService.toggleUserStatus(userId, isActive),
    {
      onSuccess: () => {
        queryClient.invalidateQueries('restaurants');
        toast.success('Restaurant status updated successfully!');
      },
      onError: (error) => {
        toast.error(error.error || 'Failed to update restaurant status');
      },
    }
  );

  const handleToggleStatus = (userId, isActive) => {
    toggleStatusMutation.mutate({ userId, isActive });
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-8">
        <p className="text-red-600">Error loading restaurants</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Restaurants</h1>
          <p className="text-gray-600">Manage restaurant accounts and profiles</p>
        </div>
        <button
          onClick={() => setShowCreateModal(true)}
          className="btn-primary flex items-center"
        >
          <PlusIcon className="h-5 w-5 mr-2" />
          Add Restaurant
        </button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {restaurants?.restaurants?.map((restaurant) => (
          <RestaurantCard
            key={restaurant.user.id}
            restaurant={restaurant}
            onToggleStatus={handleToggleStatus}
          />
        ))}
      </div>

      {restaurants?.restaurants?.length === 0 && (
        <div className="text-center py-12">
          <BuildingStorefrontIcon className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">No restaurants</h3>
          <p className="mt-1 text-sm text-gray-500">Get started by creating a new restaurant.</p>
        </div>
      )}

      <CreateRestaurantModal
        isOpen={showCreateModal}
        onClose={() => setShowCreateModal(false)}
      />
    </div>
  );
}

export default Restaurants;
