const express = require('express');
const { query } = require('../config/database');
const { authenticateToken, requireRole } = require('../middleware/auth');
const { validateRequest, schemas } = require('../middleware/validation');

const router = express.Router();

// ============================================================================
// PUBLIC ENDPOINTS (No Authentication Required)
// ============================================================================

// Get nearby restaurants (public access for guest browsing)
router.get('/public/restaurants', async (req, res) => {
  try {
    // Get default radius from admin settings
    const defaultRadiusResult = await query(
      "SELECT setting_value FROM admin_settings WHERE setting_key = 'restaurant_search_radius'"
    );
    const defaultRadius = defaultRadiusResult.rows.length > 0
      ? parseFloat(defaultRadiusResult.rows[0].setting_value)
      : 10;

    const { latitude, longitude, radius = defaultRadius } = req.query; // radius in km

    let sqlQuery;
    let queryParams;

    if (latitude && longitude) {
      // With location - calculate distance and sort by proximity
      sqlQuery = `
        SELECT
          rp.id, rp.restaurant_name, rp.description, rp.cuisine_type,
          rp.address, rp.latitude, rp.longitude, rp.rating, rp.total_orders,
          rp.is_open, rp.created_at,
          u.email,
          (
            6371 * acos(
              cos(radians($1)) * cos(radians(rp.latitude)) *
              cos(radians(rp.longitude) - radians($2)) +
              sin(radians($1)) * sin(radians(rp.latitude))
            )
          ) AS distance
        FROM restaurant_profiles rp
        JOIN users u ON rp.user_id = u.id
        WHERE u.user_type = 'restaurant' AND u.is_active = true AND rp.is_open = true
        AND (
          6371 * acos(
            cos(radians($1)) * cos(radians(rp.latitude)) *
            cos(radians(rp.longitude) - radians($2)) +
            sin(radians($1)) * sin(radians(rp.latitude))
          )
        ) <= $3
        ORDER BY distance ASC, rp.rating DESC, rp.total_orders DESC
      `;
      queryParams = [parseFloat(latitude), parseFloat(longitude), parseFloat(radius)];
    } else {
      // Without location - just get all restaurants
      sqlQuery = `
        SELECT
          rp.id, rp.restaurant_name, rp.description, rp.cuisine_type,
          rp.address, rp.latitude, rp.longitude, rp.rating, rp.total_orders,
          rp.is_open, rp.created_at,
          u.email,
          NULL as distance
        FROM restaurant_profiles rp
        JOIN users u ON rp.user_id = u.id
        WHERE u.user_type = 'restaurant' AND u.is_active = true AND rp.is_open = true
        ORDER BY rp.rating DESC, rp.total_orders DESC
      `;
      queryParams = [];
    }

    const result = await query(sqlQuery, queryParams);

    const restaurants = result.rows.map(row => ({
      id: row.id,
      name: row.restaurant_name,
      description: row.description,
      cuisineType: row.cuisine_type,
      address: row.address,
      location: {
        latitude: parseFloat(row.latitude),
        longitude: parseFloat(row.longitude)
      },
      rating: parseFloat(row.rating),
      totalOrders: row.total_orders,
      isOpen: row.is_open,
      distance: row.distance ? parseFloat(row.distance).toFixed(2) : null,
      createdAt: row.created_at
    }));

    res.json({ restaurants });

  } catch (error) {
    console.error('Get public restaurants error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Get restaurant details with menu (public access for guest browsing)
router.get('/public/restaurants/:restaurantId', async (req, res) => {
  try {
    const { restaurantId } = req.params;

    // Get restaurant details
    const restaurantResult = await query(`
      SELECT
        rp.id, rp.restaurant_name, rp.description, rp.cuisine_type,
        rp.address, rp.latitude, rp.longitude, rp.rating, rp.total_orders,
        rp.is_open, rp.created_at,
        u.email
      FROM restaurant_profiles rp
      JOIN users u ON rp.user_id = u.id
      WHERE rp.id = $1 AND u.user_type = 'restaurant' AND u.is_active = true
    `, [restaurantId]);

    if (restaurantResult.rows.length === 0) {
      return res.status(404).json({ error: 'Restaurant not found' });
    }

    const restaurant = restaurantResult.rows[0];

    // Get categories and menu items
    const categoriesResult = await query(
      'SELECT * FROM categories WHERE restaurant_id = $1 AND is_active = true ORDER BY name',
      [restaurantId]
    );

    const menuItemsResult = await query(
      'SELECT * FROM menu_items WHERE restaurant_id = $1 AND is_available = true ORDER BY category_id, name',
      [restaurantId]
    );

    const categories = categoriesResult.rows;
    const menuItems = menuItemsResult.rows;

    // Group menu items by category
    const categoriesWithItems = categories.map(category => ({
      id: category.id,
      name: category.name,
      description: category.description,
      items: menuItems.filter(item => item.category_id === category.id).map(item => ({
        id: item.id,
        name: item.name,
        description: item.description,
        price: parseFloat(item.price),
        imageUrl: item.image_url,
        preparationTime: item.preparation_time,
        isAvailable: item.is_available,
        createdAt: item.created_at
      }))
    }));

    res.json({
      restaurant: {
        id: restaurant.id,
        name: restaurant.restaurant_name,
        description: restaurant.description,
        cuisineType: restaurant.cuisine_type,
        address: restaurant.address,
        location: {
          latitude: parseFloat(restaurant.latitude),
          longitude: parseFloat(restaurant.longitude)
        },
        rating: parseFloat(restaurant.rating),
        totalOrders: restaurant.total_orders,
        isOpen: restaurant.is_open,
        createdAt: restaurant.created_at
      },
      menu: categoriesWithItems
    });

  } catch (error) {
    console.error('Get public restaurant details error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Search restaurants and menu items (public access for guest browsing)
router.get('/public/search', async (req, res) => {
  try {
    const { q, latitude, longitude, cuisine } = req.query;

    if (!q || q.trim().length < 2) {
      return res.status(400).json({ error: 'Search query must be at least 2 characters' });
    }

    const searchTerm = `%${q.trim().toLowerCase()}%`;
    let queryParams = [searchTerm, searchTerm, searchTerm];
    let whereClause = `WHERE u.user_type = 'restaurant' AND u.is_active = true AND rp.is_open = true
      AND (LOWER(rp.restaurant_name) LIKE $1 OR LOWER(rp.description) LIKE $2 OR LOWER(rp.cuisine_type) LIKE $3)`;

    if (cuisine) {
      whereClause += ` AND LOWER(rp.cuisine_type) = $${queryParams.length + 1}`;
      queryParams.push(cuisine.toLowerCase());
    }

    let sqlQuery;
    if (latitude && longitude) {
      // With location - calculate distance
      sqlQuery = `
        SELECT
          rp.id, rp.restaurant_name, rp.description, rp.cuisine_type,
          rp.address, rp.latitude, rp.longitude, rp.rating, rp.total_orders,
          rp.is_open, rp.created_at,
          u.email,
          (
            6371 * acos(
              cos(radians($${queryParams.length + 1})) * cos(radians(rp.latitude)) *
              cos(radians(rp.longitude) - radians($${queryParams.length + 2})) +
              sin(radians($${queryParams.length + 1})) * sin(radians(rp.latitude))
            )
          ) AS distance
        FROM restaurant_profiles rp
        JOIN users u ON rp.user_id = u.id
        ${whereClause}
        ORDER BY distance ASC, rp.rating DESC
        LIMIT 20
      `;
      queryParams.push(parseFloat(latitude), parseFloat(longitude));
    } else {
      // Without location
      sqlQuery = `
        SELECT
          rp.id, rp.restaurant_name, rp.description, rp.cuisine_type,
          rp.address, rp.latitude, rp.longitude, rp.rating, rp.total_orders,
          rp.is_open, rp.created_at,
          u.email,
          NULL as distance
        FROM restaurant_profiles rp
        JOIN users u ON rp.user_id = u.id
        ${whereClause}
        ORDER BY rp.rating DESC, rp.total_orders DESC
        LIMIT 20
      `;
    }

    const result = await query(sqlQuery, queryParams);

    const restaurants = result.rows.map(row => ({
      id: row.id,
      name: row.restaurant_name,
      description: row.description,
      cuisineType: row.cuisine_type,
      address: row.address,
      location: {
        latitude: parseFloat(row.latitude),
        longitude: parseFloat(row.longitude)
      },
      rating: parseFloat(row.rating),
      totalOrders: row.total_orders,
      isOpen: row.is_open,
      distance: row.distance ? parseFloat(row.distance).toFixed(2) : null,
      createdAt: row.created_at
    }));

    res.json({ restaurants });

  } catch (error) {
    console.error('Public search error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// ============================================================================
// AUTHENTICATED ENDPOINTS (Require Authentication)
// ============================================================================

// Complete customer profile
router.post('/profile', 
  authenticateToken, 
  requireRole(['customer']), 
  validateRequest(schemas.customerProfile), 
  async (req, res) => {
    try {
      const { fullName, phone, address, latitude, longitude } = req.body;
      const userId = req.user.id;

      // Check if profile already exists
      const existingProfile = await query(
        'SELECT id FROM customer_profiles WHERE user_id = $1',
        [userId]
      );

      let result;
      if (existingProfile.rows.length > 0) {
        // Update existing profile
        result = await query(
          `UPDATE customer_profiles 
           SET full_name = $1, phone = $2, address = $3, latitude = $4, longitude = $5, 
               profile_completed = true, updated_at = CURRENT_TIMESTAMP
           WHERE user_id = $6 
           RETURNING *`,
          [fullName, phone, address, latitude, longitude, userId]
        );
      } else {
        // Create new profile
        result = await query(
          `INSERT INTO customer_profiles 
           (user_id, full_name, phone, address, latitude, longitude, profile_completed) 
           VALUES ($1, $2, $3, $4, $5, $6, true) 
           RETURNING *`,
          [userId, fullName, phone, address, latitude, longitude]
        );
      }

      const profile = result.rows[0];

      res.json({
        message: 'Profile completed successfully',
        profile: {
          id: profile.id,
          fullName: profile.full_name,
          phone: profile.phone,
          address: profile.address,
          latitude: profile.latitude,
          longitude: profile.longitude,
          profileCompleted: profile.profile_completed
        }
      });

    } catch (error) {
      console.error('Profile completion error:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }
);

// Get customer profile
router.get('/profile', authenticateToken, requireRole(['customer']), async (req, res) => {
  try {
    const userId = req.user.id;

    const result = await query(
      'SELECT * FROM customer_profiles WHERE user_id = $1',
      [userId]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Profile not found' });
    }

    const profile = result.rows[0];

    res.json({
      profile: {
        id: profile.id,
        fullName: profile.full_name,
        phone: profile.phone,
        address: profile.address,
        latitude: profile.latitude,
        longitude: profile.longitude,
        profileCompleted: profile.profile_completed,
        createdAt: profile.created_at,
        updatedAt: profile.updated_at
      }
    });

  } catch (error) {
    console.error('Get profile error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Update customer location
router.patch('/location', authenticateToken, requireRole(['customer']), async (req, res) => {
  try {
    const { latitude, longitude, address } = req.body;
    const userId = req.user.id;

    if (!latitude || !longitude) {
      return res.status(400).json({ error: 'Latitude and longitude are required' });
    }

    const result = await query(
      `UPDATE customer_profiles 
       SET latitude = $1, longitude = $2, address = COALESCE($3, address), updated_at = CURRENT_TIMESTAMP
       WHERE user_id = $4 
       RETURNING latitude, longitude, address`,
      [latitude, longitude, address, userId]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Profile not found' });
    }

    res.json({
      message: 'Location updated successfully',
      location: result.rows[0]
    });

  } catch (error) {
    console.error('Update location error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Get nearby restaurants
router.get('/restaurants', authenticateToken, requireRole(['customer']), async (req, res) => {
  try {
    // Get default radius from admin settings
    const defaultRadiusResult = await query(
      "SELECT setting_value FROM admin_settings WHERE setting_key = 'restaurant_search_radius'"
    );
    const defaultRadius = defaultRadiusResult.rows.length > 0
      ? parseFloat(defaultRadiusResult.rows[0].setting_value)
      : 10;

    const { latitude, longitude, radius = defaultRadius } = req.query; // radius in km
    
    let sqlQuery;
    let queryParams;
    
    if (latitude && longitude) {
      // With location - calculate distance and filter by radius
      sqlQuery = `
        SELECT 
          rp.id, rp.restaurant_name, rp.description, rp.cuisine_type,
          rp.address, rp.latitude, rp.longitude, rp.rating, rp.total_orders,
          rp.is_open, rp.created_at,
          u.email,
          CAST(
            6371 * acos(
              LEAST(1.0, GREATEST(-1.0,
                cos(radians($1)) * cos(radians(rp.latitude)) *
                cos(radians(rp.longitude) - radians($2)) +
                sin(radians($1)) * sin(radians(rp.latitude))
              ))
            ) AS NUMERIC(10,2)
          ) as distance
        FROM restaurant_profiles rp
        JOIN users u ON rp.user_id = u.id
        WHERE u.user_type = 'restaurant' AND u.is_active = true AND rp.is_open = true
        AND rp.latitude IS NOT NULL AND rp.longitude IS NOT NULL
        AND (
          6371 * acos(
            LEAST(1.0, GREATEST(-1.0,
              cos(radians($1)) * cos(radians(rp.latitude)) *
              cos(radians(rp.longitude) - radians($2)) +
              sin(radians($1)) * sin(radians(rp.latitude))
            ))
          )
        ) <= $3
        ORDER BY distance ASC, rp.rating DESC, rp.total_orders DESC
      `;
      queryParams = [parseFloat(latitude), parseFloat(longitude), parseFloat(radius)];
    } else {
      // Without location - just get all restaurants
      sqlQuery = `
        SELECT 
          rp.id, rp.restaurant_name, rp.description, rp.cuisine_type,
          rp.address, rp.latitude, rp.longitude, rp.rating, rp.total_orders,
          rp.is_open, rp.created_at,
          u.email,
          NULL as distance
        FROM restaurant_profiles rp
        JOIN users u ON rp.user_id = u.id
        WHERE u.user_type = 'restaurant' AND u.is_active = true AND rp.is_open = true
        ORDER BY rp.rating DESC, rp.total_orders DESC
      `;
      queryParams = [];
    }

    const result = await query(sqlQuery, queryParams);

    const restaurants = result.rows.map(row => ({
      id: row.id,
      name: row.restaurant_name,
      description: row.description,
      cuisineType: row.cuisine_type,
      address: row.address,
      location: {
        latitude: parseFloat(row.latitude),
        longitude: parseFloat(row.longitude)
      },
      rating: parseFloat(row.rating),
      totalOrders: row.total_orders,
      isOpen: row.is_open,
      distance: row.distance ? parseFloat(row.distance).toFixed(2) : null,
      createdAt: row.created_at
    }));

    res.json({ restaurants });

  } catch (error) {
    console.error('Get restaurants error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Get restaurant details with menu
router.get('/restaurants/:restaurantId', authenticateToken, requireRole(['customer']), async (req, res) => {
  try {
    const { restaurantId } = req.params;

    // Get restaurant details
    const restaurantResult = await query(`
      SELECT 
        rp.id, rp.restaurant_name, rp.description, rp.cuisine_type,
        rp.address, rp.latitude, rp.longitude, rp.rating, rp.total_orders,
        rp.is_open, rp.created_at,
        u.email
      FROM restaurant_profiles rp
      JOIN users u ON rp.user_id = u.id
      WHERE rp.id = $1 AND u.user_type = 'restaurant' AND u.is_active = true
    `, [restaurantId]);

    if (restaurantResult.rows.length === 0) {
      return res.status(404).json({ error: 'Restaurant not found' });
    }

    const restaurant = restaurantResult.rows[0];

    // Get categories and menu items
    const categoriesResult = await query(
      'SELECT * FROM categories WHERE restaurant_id = $1 AND is_active = true ORDER BY name',
      [restaurantId]
    );

    const menuItemsResult = await query(
      `SELECT mi.*, mi.average_rating, mi.total_ratings
       FROM menu_items mi
       WHERE mi.restaurant_id = $1 AND mi.is_available = true
       ORDER BY mi.category_id, mi.name`,
      [restaurantId]
    );

    const categories = categoriesResult.rows;
    const menuItems = menuItemsResult.rows;

    // Group menu items by category
    const categoriesWithItems = categories.map(category => ({
      id: category.id,
      name: category.name,
      description: category.description,
      items: menuItems.filter(item => item.category_id === category.id).map(item => ({
        id: item.id,
        name: item.name,
        description: item.description,
        price: parseFloat(item.price),
        imageUrl: item.image_url,
        preparationTime: item.preparation_time,
        isAvailable: item.is_available,
        averageRating: parseFloat(item.average_rating) || 0,
        totalRatings: item.total_ratings || 0,
        createdAt: item.created_at
      }))
    }));

    // Add uncategorized items
    const uncategorizedItems = menuItems.filter(item => !item.category_id);
    if (uncategorizedItems.length > 0) {
      categoriesWithItems.push({
        id: null,
        name: 'Popular Items',
        description: 'Featured menu items',
        items: uncategorizedItems.map(item => ({
          id: item.id,
          name: item.name,
          description: item.description,
          price: parseFloat(item.price),
          imageUrl: item.image_url,
          preparationTime: item.preparation_time,
          isAvailable: item.is_available,
          averageRating: parseFloat(item.average_rating) || 0,
          totalRatings: item.total_ratings || 0,
          createdAt: item.created_at
        }))
      });
    }

    res.json({
      restaurant: {
        id: restaurant.id,
        name: restaurant.restaurant_name,
        description: restaurant.description,
        cuisineType: restaurant.cuisine_type,
        address: restaurant.address,
        location: {
          latitude: parseFloat(restaurant.latitude),
          longitude: parseFloat(restaurant.longitude)
        },
        rating: parseFloat(restaurant.rating),
        totalOrders: restaurant.total_orders,
        isOpen: restaurant.is_open,
        createdAt: restaurant.created_at
      },
      menu: categoriesWithItems
    });

  } catch (error) {
    console.error('Get restaurant details error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Search restaurants and menu items
router.get('/search', authenticateToken, requireRole(['customer']), async (req, res) => {
  try {
    const { q, latitude, longitude, cuisine } = req.query;
    
    if (!q || q.trim().length < 2) {
      return res.status(400).json({ error: 'Search query must be at least 2 characters' });
    }

    const searchTerm = `%${q.trim().toLowerCase()}%`;
    let queryParams = [searchTerm, searchTerm, searchTerm];
    let whereClause = `WHERE u.user_type = 'restaurant' AND u.is_active = true AND rp.is_open = true
      AND (LOWER(rp.restaurant_name) LIKE $1 OR LOWER(rp.description) LIKE $2 OR LOWER(rp.cuisine_type) LIKE $3)`;
    
    if (cuisine) {
      whereClause += ` AND LOWER(rp.cuisine_type) = $${queryParams.length + 1}`;
      queryParams.push(cuisine.toLowerCase());
    }

    const result = await query(`
      SELECT DISTINCT
        rp.id, rp.restaurant_name, rp.description, rp.cuisine_type,
        rp.address, rp.latitude, rp.longitude, rp.rating, rp.total_orders,
        rp.is_open, rp.created_at
      FROM restaurant_profiles rp
      JOIN users u ON rp.user_id = u.id
      LEFT JOIN menu_items mi ON rp.id = mi.restaurant_id AND mi.is_available = true
      ${whereClause}
      OR (mi.id IS NOT NULL AND (LOWER(mi.name) LIKE $1 OR LOWER(mi.description) LIKE $2))
      ORDER BY rp.rating DESC, rp.total_orders DESC
      LIMIT 20
    `, queryParams);

    const restaurants = result.rows.map(row => ({
      id: row.id,
      name: row.restaurant_name,
      description: row.description,
      cuisineType: row.cuisine_type,
      address: row.address,
      location: {
        latitude: parseFloat(row.latitude),
        longitude: parseFloat(row.longitude)
      },
      rating: parseFloat(row.rating),
      totalOrders: row.total_orders,
      isOpen: row.is_open,
      createdAt: row.created_at
    }));

    res.json({ restaurants, query: q });

  } catch (error) {
    console.error('Search error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Create order from cart
router.post('/orders', 
  authenticateToken, 
  requireRole(['customer']), 
  async (req, res) => {
    try {
      const { 
        restaurantId, 
        items, 
        deliveryAddress, 
        deliveryLatitude, 
        deliveryLongitude, 
        specialInstructions,
        deliveryFee = 0.00 
      } = req.body;
      
      const userId = req.user.id;

      // Get customer profile
      const customerProfile = await query(
        'SELECT id FROM customer_profiles WHERE user_id = $1',
        [userId]
      );

      if (customerProfile.rows.length === 0) {
        return res.status(400).json({ error: 'Customer profile not found' });
      }

      const customerId = customerProfile.rows[0].id;

      // Validate restaurant exists
      const restaurant = await query(
        'SELECT id FROM restaurant_profiles WHERE id = $1',
        [restaurantId]
      );

      if (restaurant.rows.length === 0) {
        return res.status(400).json({ error: 'Restaurant not found' });
      }

      // Calculate total amount
      let totalAmount = 0;
      const validatedItems = [];

      for (const item of items) {
        const menuItem = await query(
          'SELECT id, name, price, is_available FROM menu_items WHERE id = $1 AND restaurant_id = $2',
          [item.menuItemId, restaurantId]
        );

        if (menuItem.rows.length === 0) {
          return res.status(400).json({ error: `Menu item ${item.menuItemId} not found` });
        }

        const menuItemData = menuItem.rows[0];
        if (!menuItemData.is_available) {
          return res.status(400).json({ error: `Menu item ${menuItemData.name} is not available` });
        }

        const itemTotal = parseFloat(menuItemData.price) * item.quantity;
        totalAmount += itemTotal;

        validatedItems.push({
          menuItemId: item.menuItemId,
          quantity: item.quantity,
          unitPrice: parseFloat(menuItemData.price),
          totalPrice: itemTotal,
          specialInstructions: item.specialInstructions || null
        });
      }

      totalAmount += parseFloat(deliveryFee);

      // Generate order number
      const orderNumber = 'ORD' + Date.now() + Math.floor(Math.random() * 1000);

      // Create order
      const orderResult = await query(
        `INSERT INTO orders 
         (customer_id, restaurant_id, order_number, status, total_amount, delivery_fee, 
          delivery_address, delivery_latitude, delivery_longitude, special_instructions,
          estimated_delivery_time) 
         VALUES ($1, $2, $3, 'pending', $4, $5, $6, $7, $8, $9, $10) 
         RETURNING *`,
        [
          customerId, 
          restaurantId, 
          orderNumber, 
          totalAmount, 
          deliveryFee,
          deliveryAddress, 
          deliveryLatitude, 
          deliveryLongitude, 
          specialInstructions,
          new Date(Date.now() + 45 * 60 * 1000) // 45 minutes from now
        ]
      );

      const order = orderResult.rows[0];

      // Create order items
      for (const item of validatedItems) {
        await query(
          `INSERT INTO order_items 
           (order_id, menu_item_id, quantity, unit_price, total_price, special_instructions) 
           VALUES ($1, $2, $3, $4, $5, $6)`,
          [
            order.id, 
            item.menuItemId, 
            item.quantity, 
            item.unitPrice, 
            item.totalPrice, 
            item.specialInstructions
          ]
        );
      }

      res.status(201).json({
        message: 'Order created successfully',
        order: {
          id: order.id,
          orderNumber: order.order_number,
          status: order.status,
          totalAmount: parseFloat(order.total_amount),
          deliveryFee: parseFloat(order.delivery_fee),
          estimatedDeliveryTime: order.estimated_delivery_time,
          createdAt: order.created_at
        }
      });

    } catch (error) {
      console.error('Create order error:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }
);

// Get customer orders
router.get('/orders', 
  authenticateToken, 
  requireRole(['customer']), 
  async (req, res) => {
    try {
      const userId = req.user.id;
      const { status, limit = 20, offset = 0 } = req.query;

      // Get customer profile
      const customerProfile = await query(
        'SELECT id FROM customer_profiles WHERE user_id = $1',
        [userId]
      );

      if (customerProfile.rows.length === 0) {
        return res.status(400).json({ error: 'Customer profile not found' });
      }

      const customerId = customerProfile.rows[0].id;

      let whereClause = 'WHERE o.customer_id = $1';
      let queryParams = [customerId];
      let paramCount = 1;

      if (status) {
        paramCount++;
        whereClause += ` AND o.status = $${paramCount}`;
        queryParams.push(status);
      }

      const ordersResult = await query(
        `SELECT
           o.id, o.order_number, o.status, o.total_amount, o.delivery_fee,
           o.delivery_address, o.special_instructions, o.estimated_delivery_time,
           o.created_at, o.updated_at,
           r.restaurant_name, r.cuisine_type, r.phone as restaurant_phone,
           d.full_name as driver_name, d.phone as driver_phone,
           CASE WHEN o.status = 'delivered' THEN
             CASE WHEN EXISTS (
               SELECT 1 FROM food_ratings fr
               JOIN order_items oi ON fr.menu_item_id = oi.menu_item_id AND fr.order_id = oi.order_id
               WHERE oi.order_id = o.id AND fr.customer_id = o.customer_id
             ) THEN true ELSE false END
           ELSE false END as has_food_ratings,
           CASE WHEN o.status = 'delivered' AND o.driver_id IS NOT NULL THEN
             CASE WHEN EXISTS (
               SELECT 1 FROM driver_ratings dr
               WHERE dr.order_id = o.id AND dr.customer_id = o.customer_id AND dr.driver_id = o.driver_id
             ) THEN true ELSE false END
           ELSE false END as has_driver_rating
         FROM orders o
         LEFT JOIN restaurant_profiles r ON o.restaurant_id = r.id
         LEFT JOIN driver_profiles d ON o.driver_id = d.id
         ${whereClause}
         ORDER BY o.created_at DESC
         LIMIT $${paramCount + 1} OFFSET $${paramCount + 2}`,
        [...queryParams, limit, offset]
      );

      const orders = [];
      for (const order of ordersResult.rows) {
        // Get order items
        const itemsResult = await query(
          `SELECT 
             oi.quantity, oi.unit_price, oi.total_price, oi.special_instructions,
             mi.name, mi.description, mi.image_url
           FROM order_items oi
           JOIN menu_items mi ON oi.menu_item_id = mi.id
           WHERE oi.order_id = $1`,
          [order.id]
        );

        orders.push({
          id: order.id,
          orderNumber: order.order_number,
          status: order.status,
          totalAmount: parseFloat(order.total_amount),
          deliveryFee: parseFloat(order.delivery_fee),
          deliveryAddress: order.delivery_address,
          specialInstructions: order.special_instructions,
          estimatedDeliveryTime: order.estimated_delivery_time,
          createdAt: order.created_at,
          updatedAt: order.updated_at,
          restaurant: {
            name: order.restaurant_name,
            cuisineType: order.cuisine_type,
            phone: order.restaurant_phone
          },
          driver: order.driver_name ? {
            name: order.driver_name,
            phone: order.driver_phone
          } : null,
          items: itemsResult.rows.map(item => ({
            name: item.name,
            description: item.description,
            imageUrl: item.image_url,
            quantity: item.quantity,
            unitPrice: parseFloat(item.unit_price),
            totalPrice: parseFloat(item.total_price),
            specialInstructions: item.special_instructions
          })),
          ratings: {
            hasFoodRatings: order.has_food_ratings,
            hasDriverRating: order.has_driver_rating,
            canRate: order.status === 'delivered'
          }
        });
      }

      res.json({ orders });

    } catch (error) {
      console.error('Get orders error:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }
);

// Get specific order details
router.get('/orders/:orderId', 
  authenticateToken, 
  requireRole(['customer']), 
  async (req, res) => {
    try {
      const { orderId } = req.params;
      const userId = req.user.id;

      // Get customer profile
      const customerProfile = await query(
        'SELECT id FROM customer_profiles WHERE user_id = $1',
        [userId]
      );

      if (customerProfile.rows.length === 0) {
        return res.status(400).json({ error: 'Customer profile not found' });
      }

      const customerId = customerProfile.rows[0].id;

      // Get order details
      const orderResult = await query(
        `SELECT 
           o.id, o.order_number, o.status, o.total_amount, o.delivery_fee,
           o.delivery_address, o.delivery_latitude, o.delivery_longitude,
           o.special_instructions, o.estimated_delivery_time,
           o.created_at, o.updated_at,
           r.restaurant_name, r.cuisine_type, r.phone as restaurant_phone,
           r.address as restaurant_address, r.latitude as restaurant_latitude,
           r.longitude as restaurant_longitude,
           d.full_name as driver_name, d.phone as driver_phone,
           d.current_latitude as driver_latitude, d.current_longitude as driver_longitude
         FROM orders o
         LEFT JOIN restaurant_profiles r ON o.restaurant_id = r.id
         LEFT JOIN driver_profiles d ON o.driver_id = d.id
         WHERE o.id = $1 AND o.customer_id = $2`,
        [orderId, customerId]
      );

      if (orderResult.rows.length === 0) {
        return res.status(404).json({ error: 'Order not found' });
      }

      const order = orderResult.rows[0];

      // Get order items
      const itemsResult = await query(
        `SELECT 
           oi.quantity, oi.unit_price, oi.total_price, oi.special_instructions,
           mi.name, mi.description, mi.image_url
         FROM order_items oi
         JOIN menu_items mi ON oi.menu_item_id = mi.id
         WHERE oi.order_id = $1`,
        [orderId]
      );

      const orderDetails = {
        id: order.id,
        orderNumber: order.order_number,
        status: order.status,
        totalAmount: parseFloat(order.total_amount),
        deliveryFee: parseFloat(order.delivery_fee),
        deliveryAddress: order.delivery_address,
        deliveryLocation: {
          latitude: parseFloat(order.delivery_latitude),
          longitude: parseFloat(order.delivery_longitude)
        },
        specialInstructions: order.special_instructions,
        estimatedDeliveryTime: order.estimated_delivery_time,
        createdAt: order.created_at,
        updatedAt: order.updated_at,
        restaurant: {
          name: order.restaurant_name,
          cuisineType: order.cuisine_type,
          phone: order.restaurant_phone,
          address: order.restaurant_address,
          location: {
            latitude: parseFloat(order.restaurant_latitude),
            longitude: parseFloat(order.restaurant_longitude)
          }
        },
        driver: order.driver_name ? {
          name: order.driver_name,
          phone: order.driver_phone,
          currentLocation: {
            latitude: parseFloat(order.driver_latitude),
            longitude: parseFloat(order.driver_longitude)
          }
        } : null,
        items: itemsResult.rows.map(item => ({
          name: item.name,
          description: item.description,
          imageUrl: item.image_url,
          quantity: item.quantity,
          unitPrice: parseFloat(item.unit_price),
          totalPrice: parseFloat(item.total_price),
          specialInstructions: item.special_instructions
        }))
      };

      res.json({ order: orderDetails });

    } catch (error) {
      console.error('Get order details error:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }
);

// Cancel order (only if status is pending or confirmed)
router.put('/orders/:orderId/cancel', 
  authenticateToken, 
  requireRole(['customer']), 
  async (req, res) => {
    try {
      const { orderId } = req.params;
      const userId = req.user.id;

      // Get customer profile
      const customerProfile = await query(
        'SELECT id FROM customer_profiles WHERE user_id = $1',
        [userId]
      );

      if (customerProfile.rows.length === 0) {
        return res.status(400).json({ error: 'Customer profile not found' });
      }

      const customerId = customerProfile.rows[0].id;

      // Check if order exists and belongs to customer
      const orderResult = await query(
        'SELECT id, status FROM orders WHERE id = $1 AND customer_id = $2',
        [orderId, customerId]
      );

      if (orderResult.rows.length === 0) {
        return res.status(404).json({ error: 'Order not found' });
      }

      const order = orderResult.rows[0];

      // Check if order can be cancelled
      if (!['pending', 'confirmed'].includes(order.status)) {
        return res.status(400).json({ 
          error: 'Order cannot be cancelled. Current status: ' + order.status 
        });
      }

      // Update order status to cancelled
      await query(
        'UPDATE orders SET status = $1, updated_at = CURRENT_TIMESTAMP WHERE id = $2',
        ['cancelled', orderId]
      );

      res.json({ message: 'Order cancelled successfully' });

    } catch (error) {
      console.error('Cancel order error:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }
);

// Get customer addresses
router.get('/addresses', authenticateToken, requireRole(['customer']), async (req, res) => {
  try {
    const userId = req.user.id;

    // Get customer profile
    const customerProfile = await query(
      'SELECT id FROM customer_profiles WHERE user_id = $1',
      [userId]
    );

    if (customerProfile.rows.length === 0) {
      return res.status(404).json({ error: 'Customer profile not found' });
    }

    const customerId = customerProfile.rows[0].id;

    // Get all addresses for this customer
    const addresses = await query(
      `SELECT id, label, address, latitude, longitude, is_default, created_at, updated_at
       FROM customer_addresses
       WHERE customer_id = $1
       ORDER BY is_default DESC, created_at ASC`,
      [customerId]
    );

    res.json({
      success: true,
      addresses: addresses.rows
    });
  } catch (error) {
    console.error('Get addresses error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Add new customer address
router.post('/addresses', authenticateToken, requireRole(['customer']), async (req, res) => {
  try {
    const { label, address, latitude, longitude, isDefault } = req.body;
    const userId = req.user.id;

    if (!label || !address || !latitude || !longitude) {
      return res.status(400).json({ error: 'Label, address, latitude, and longitude are required' });
    }

    // Get customer profile
    const customerProfile = await query(
      'SELECT id FROM customer_profiles WHERE user_id = $1',
      [userId]
    );

    if (customerProfile.rows.length === 0) {
      return res.status(404).json({ error: 'Customer profile not found' });
    }

    const customerId = customerProfile.rows[0].id;

    // If this is set as default, unset other defaults
    if (isDefault) {
      await query(
        'UPDATE customer_addresses SET is_default = FALSE WHERE customer_id = $1',
        [customerId]
      );
    }

    // Insert new address
    const result = await query(
      `INSERT INTO customer_addresses (customer_id, label, address, latitude, longitude, is_default)
       VALUES ($1, $2, $3, $4, $5, $6)
       RETURNING *`,
      [customerId, label, address, latitude, longitude, isDefault || false]
    );

    res.status(201).json({
      success: true,
      address: result.rows[0]
    });
  } catch (error) {
    console.error('Add address error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Update customer address
router.put('/addresses/:addressId', authenticateToken, requireRole(['customer']), async (req, res) => {
  try {
    const { addressId } = req.params;
    const { label, address, latitude, longitude, isDefault } = req.body;
    const userId = req.user.id;

    // Get customer profile
    const customerProfile = await query(
      'SELECT id FROM customer_profiles WHERE user_id = $1',
      [userId]
    );

    if (customerProfile.rows.length === 0) {
      return res.status(404).json({ error: 'Customer profile not found' });
    }

    const customerId = customerProfile.rows[0].id;

    // Verify address belongs to this customer
    const addressCheck = await query(
      'SELECT id FROM customer_addresses WHERE id = $1 AND customer_id = $2',
      [addressId, customerId]
    );

    if (addressCheck.rows.length === 0) {
      return res.status(404).json({ error: 'Address not found' });
    }

    // If this is set as default, unset other defaults
    if (isDefault) {
      await query(
        'UPDATE customer_addresses SET is_default = FALSE WHERE customer_id = $1 AND id != $2',
        [customerId, addressId]
      );
    }

    // Update address
    const result = await query(
      `UPDATE customer_addresses
       SET label = COALESCE($1, label),
           address = COALESCE($2, address),
           latitude = COALESCE($3, latitude),
           longitude = COALESCE($4, longitude),
           is_default = COALESCE($5, is_default),
           updated_at = CURRENT_TIMESTAMP
       WHERE id = $6 AND customer_id = $7
       RETURNING *`,
      [label, address, latitude, longitude, isDefault, addressId, customerId]
    );

    res.json({
      success: true,
      address: result.rows[0]
    });
  } catch (error) {
    console.error('Update address error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Delete customer address
router.delete('/addresses/:addressId', authenticateToken, requireRole(['customer']), async (req, res) => {
  try {
    const { addressId } = req.params;
    const userId = req.user.id;

    // Get customer profile
    const customerProfile = await query(
      'SELECT id FROM customer_profiles WHERE user_id = $1',
      [userId]
    );

    if (customerProfile.rows.length === 0) {
      return res.status(404).json({ error: 'Customer profile not found' });
    }

    const customerId = customerProfile.rows[0].id;

    // Verify address belongs to this customer
    const addressCheck = await query(
      'SELECT id, is_default FROM customer_addresses WHERE id = $1 AND customer_id = $2',
      [addressId, customerId]
    );

    if (addressCheck.rows.length === 0) {
      return res.status(404).json({ error: 'Address not found' });
    }

    const wasDefault = addressCheck.rows[0].is_default;

    // Delete address
    await query(
      'DELETE FROM customer_addresses WHERE id = $1 AND customer_id = $2',
      [addressId, customerId]
    );

    // If deleted address was default, set another address as default
    if (wasDefault) {
      await query(
        `UPDATE customer_addresses
         SET is_default = TRUE
         WHERE customer_id = $1
         AND id = (SELECT id FROM customer_addresses WHERE customer_id = $1 LIMIT 1)`,
        [customerId]
      );
    }

    res.json({
      success: true,
      message: 'Address deleted successfully'
    });
  } catch (error) {
    console.error('Delete address error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

module.exports = router;
