import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from 'react-query';
import { useForm } from 'react-hook-form';
import { apiService } from '../services/apiService';
import toast from 'react-hot-toast';
import {
  PlusIcon,
  TruckIcon,
  PhoneIcon,
  StarIcon,
  CurrencyDollarIcon,
} from '@heroicons/react/24/outline';

function CreateDriverModal({ isOpen, onClose }) {
  const { register, handleSubmit, formState: { errors }, reset } = useForm();
  const queryClient = useQueryClient();

  const createMutation = useMutation(apiService.createDriver, {
    onSuccess: () => {
      queryClient.invalidateQueries('drivers');
      toast.success('Driver created successfully!');
      reset();
      onClose();
    },
    onError: (error) => {
      toast.error(error.error || 'Failed to create driver');
    },
  });

  const onSubmit = (data) => {
    createMutation.mutate(data);
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div className="relative top-20 mx-auto p-5 border w-full max-w-2xl shadow-lg rounded-md bg-white">
        <div className="mt-3">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-medium text-gray-900">Create New Driver</h3>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600"
            >
              <span className="sr-only">Close</span>
              ×
            </button>
          </div>

          <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700">Email</label>
                <input
                  {...register('email', { 
                    required: 'Email is required',
                    pattern: {
                      value: /^\S+@\S+$/i,
                      message: 'Invalid email address'
                    }
                  })}
                  type="email"
                  className="input-field"
                />
                {errors.email && <p className="text-red-600 text-sm">{errors.email.message}</p>}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700">Password</label>
                <input
                  {...register('password', { 
                    required: 'Password is required',
                    minLength: { value: 6, message: 'Password must be at least 6 characters' }
                  })}
                  type="password"
                  className="input-field"
                />
                {errors.password && <p className="text-red-600 text-sm">{errors.password.message}</p>}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700">Full Name</label>
                <input
                  {...register('fullName', { required: 'Full name is required' })}
                  type="text"
                  className="input-field"
                />
                {errors.fullName && <p className="text-red-600 text-sm">{errors.fullName.message}</p>}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700">Phone</label>
                <input
                  {...register('phone', { required: 'Phone is required' })}
                  type="tel"
                  className="input-field"
                />
                {errors.phone && <p className="text-red-600 text-sm">{errors.phone.message}</p>}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700">License Number</label>
                <input
                  {...register('licenseNumber', { required: 'License number is required' })}
                  type="text"
                  className="input-field"
                />
                {errors.licenseNumber && <p className="text-red-600 text-sm">{errors.licenseNumber.message}</p>}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700">Vehicle Type</label>
                <select
                  {...register('vehicleType', { required: 'Vehicle type is required' })}
                  className="input-field"
                >
                  <option value="">Select vehicle type</option>
                  <option value="motorcycle">Motorcycle</option>
                  <option value="car">Car</option>
                  <option value="bicycle">Bicycle</option>
                  <option value="scooter">Scooter</option>
                </select>
                {errors.vehicleType && <p className="text-red-600 text-sm">{errors.vehicleType.message}</p>}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700">Vehicle Plate</label>
                <input
                  {...register('vehiclePlate', { required: 'Vehicle plate is required' })}
                  type="text"
                  className="input-field"
                  placeholder="e.g., ABC-1234"
                />
                {errors.vehiclePlate && <p className="text-red-600 text-sm">{errors.vehiclePlate.message}</p>}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700">Vehicle Color</label>
                <input
                  {...register('vehicleColor')}
                  type="text"
                  className="input-field"
                  placeholder="e.g., Red, Blue, White"
                />
              </div>
            </div>

            <div className="flex justify-end space-x-3 pt-4">
              <button
                type="button"
                onClick={onClose}
                className="btn-secondary"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={createMutation.isLoading}
                className="btn-primary"
              >
                {createMutation.isLoading ? 'Creating...' : 'Create Driver'}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}

function DriverCard({ driver, onToggleStatus }) {
  const { user, profile } = driver;

  return (
    <div className="card">
      <div className="flex items-start justify-between">
        <div className="flex items-start space-x-3">
          <div className="flex-shrink-0">
            <TruckIcon className="h-8 w-8 text-gray-400" />
          </div>
          <div className="flex-1">
            <h3 className="text-lg font-medium text-gray-900">{profile.fullName}</h3>
            <p className="text-sm text-gray-600">{user.email}</p>
            
            <div className="mt-2 space-y-1">
              <div className="flex items-center text-sm text-gray-600">
                <PhoneIcon className="h-4 w-4 mr-1" />
                {profile.phone}
              </div>
              <div className="flex items-center text-sm text-gray-600">
                <span className="font-medium">License:</span>
                <span className="ml-1">{profile.licenseNumber}</span>
              </div>
              <div className="flex items-center text-sm text-gray-600">
                <span className="font-medium">Vehicle:</span>
                <span className="ml-1 capitalize">
                  {profile.vehicleType} - {profile.vehiclePlate}
                  {profile.vehicleColor && ` (${profile.vehicleColor})`}
                </span>
              </div>
            </div>

            <div className="mt-3 flex items-center space-x-4">
              <div className="flex items-center text-sm text-gray-600">
                <StarIcon className="h-4 w-4 mr-1" />
                {(profile.rating || 0).toFixed(1)} rating
              </div>
              <div className="flex items-center text-sm text-gray-600">
                <span>{profile.totalDeliveries || 0} deliveries</span>
              </div>
              <div className="flex items-center text-sm text-gray-600">
                <CurrencyDollarIcon className="h-4 w-4 mr-1" />
                ${(profile.totalEarnings || 0).toFixed(2)}
              </div>
            </div>

            <div className="mt-3 flex items-center space-x-2">
              <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                profile.isAvailable ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
              }`}>
                {profile.isAvailable ? 'Available' : 'Unavailable'}
              </span>
              {profile.currentLatitude && profile.currentLongitude && (
                <span className="text-xs text-gray-500">
                  Location: {Number(profile.currentLatitude).toFixed(4)}, {Number(profile.currentLongitude).toFixed(4)}
                </span>
              )}
            </div>
          </div>
        </div>

        <div className="flex flex-col items-end space-y-2">
          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
            user.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
          }`}>
            {user.isActive ? 'Active' : 'Inactive'}
          </span>
          <button
            onClick={() => onToggleStatus(user.id, !user.isActive)}
            className={`text-xs px-3 py-1 rounded ${
              user.isActive ? 'btn-danger' : 'btn-primary'
            }`}
          >
            {user.isActive ? 'Deactivate' : 'Activate'}
          </button>
        </div>
      </div>
    </div>
  );
}

function Drivers() {
  const [showCreateModal, setShowCreateModal] = useState(false);
  const queryClient = useQueryClient();

  const { data: drivers, isLoading, error } = useQuery('drivers', apiService.getDrivers);

  const toggleStatusMutation = useMutation(
    ({ userId, isActive }) => apiService.toggleUserStatus(userId, isActive),
    {
      onSuccess: () => {
        queryClient.invalidateQueries('drivers');
        toast.success('Driver status updated successfully!');
      },
      onError: (error) => {
        toast.error(error.error || 'Failed to update driver status');
      },
    }
  );

  const handleToggleStatus = (userId, isActive) => {
    toggleStatusMutation.mutate({ userId, isActive });
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-8">
        <p className="text-red-600">Error loading drivers</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Drivers</h1>
          <p className="text-gray-600">Manage driver accounts and profiles</p>
        </div>
        <button
          onClick={() => setShowCreateModal(true)}
          className="btn-primary flex items-center"
        >
          <PlusIcon className="h-5 w-5 mr-2" />
          Add Driver
        </button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {drivers?.drivers?.map((driver) => (
          <DriverCard
            key={driver.user.id}
            driver={driver}
            onToggleStatus={handleToggleStatus}
          />
        ))}
      </div>

      {drivers?.drivers?.length === 0 && (
        <div className="text-center py-12">
          <TruckIcon className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">No drivers</h3>
          <p className="mt-1 text-sm text-gray-500">Get started by creating a new driver.</p>
        </div>
      )}

      <CreateDriverModal
        isOpen={showCreateModal}
        onClose={() => setShowCreateModal(false)}
      />
    </div>
  );
}

export default Drivers;
