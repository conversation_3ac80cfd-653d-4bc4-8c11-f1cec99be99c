import 'package:flutter/material.dart';
import '../models/user.dart';
import '../services/api_service.dart';

class OrdersProvider with ChangeNotifier {
  List<Order> _orders = [];
  bool _isLoading = false;
  String? _error;
  String? _selectedStatus;

  List<Order> get orders => _orders;
  bool get isLoading => _isLoading;
  String? get error => _error;
  String? get selectedStatus => _selectedStatus;

  List<Order> get pendingOrders =>
      _orders.where((order) => order.status == 'pending').toList();

  List<Order> get confirmedOrders =>
      _orders.where((order) => order.status == 'confirmed').toList();

  List<Order> get preparingOrders =>
      _orders.where((order) => order.status == 'preparing').toList();

  List<Order> get readyOrders =>
      _orders.where((order) => order.status == 'ready').toList();

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String? error) {
    _error = error;
    notifyListeners();
  }

  Future<void> loadOrders({String? status}) async {
    _setLoading(true);
    _setError(null);
    _selectedStatus = status;

    try {
      final result = await ApiService.getOrders(status: status);

      if (result['success']) {
        final ordersData = result['data']['orders'] as List;
        _orders = ordersData
            .map((orderJson) => Order.fromJson(orderJson))
            .toList();
      } else {
        _setError(result['error']);
      }
    } catch (e) {
      _setError('Failed to load orders: $e');
    }

    _setLoading(false);
  }

  Future<bool> cancelOrder({required int orderId, String? reason}) async {
    _setLoading(true);
    _setError(null);

    try {
      final result = await ApiService.cancelOrder(
        orderId: orderId,
        reason: reason,
      );

      if (result['success']) {
        // Update the order in the local list
        final orderIndex = _orders.indexWhere((order) => order.id == orderId);
        if (orderIndex != -1) {
          // Create a new order with cancelled status
          final updatedOrder = Order(
            id: _orders[orderIndex].id,
            orderNumber: _orders[orderIndex].orderNumber,
            status: 'cancelled',
            totalAmount: _orders[orderIndex].totalAmount,
            deliveryFee: _orders[orderIndex].deliveryFee,
            deliveryAddress: _orders[orderIndex].deliveryAddress,
            specialInstructions: _orders[orderIndex].specialInstructions,
            estimatedDeliveryTime: _orders[orderIndex].estimatedDeliveryTime,
            customer: _orders[orderIndex].customer,
            driver: _orders[orderIndex].driver,
            items: _orders[orderIndex].items,
            createdAt: _orders[orderIndex].createdAt,
            updatedAt: DateTime.now(),
          );

          _orders[orderIndex] = updatedOrder;
        }

        _setLoading(false);
        return true;
      } else {
        _setError(result['error']);
        _setLoading(false);
        return false;
      }
    } catch (e) {
      _setError('Failed to cancel order: $e');
      _setLoading(false);
      return false;
    }
  }

  Future<bool> updateOrderStatus({
    required int orderId,
    required String status,
  }) async {
    _setLoading(true);
    _setError(null);

    try {
      final result = await ApiService.updateOrderStatus(
        orderId: orderId,
        status: status,
      );

      if (result['success']) {
        // Update the order in the local list
        final orderIndex = _orders.indexWhere((order) => order.id == orderId);
        if (orderIndex != -1) {
          // Create a new order with updated status
          final updatedOrder = Order(
            id: _orders[orderIndex].id,
            orderNumber: _orders[orderIndex].orderNumber,
            status: status,
            totalAmount: _orders[orderIndex].totalAmount,
            deliveryFee: _orders[orderIndex].deliveryFee,
            deliveryAddress: _orders[orderIndex].deliveryAddress,
            specialInstructions: _orders[orderIndex].specialInstructions,
            estimatedDeliveryTime: _orders[orderIndex].estimatedDeliveryTime,
            customer: _orders[orderIndex].customer,
            createdAt: _orders[orderIndex].createdAt,
            updatedAt: DateTime.now(),
          );

          _orders[orderIndex] = updatedOrder;
        }

        _setLoading(false);
        return true;
      } else {
        _setError(result['error']);
        _setLoading(false);
        return false;
      }
    } catch (e) {
      _setError('Failed to update order status: $e');
      _setLoading(false);
      return false;
    }
  }

  void clearError() {
    _setError(null);
  }

  void filterByStatus(String? status) {
    _selectedStatus = status;
    loadOrders(status: status);
  }

  int getOrderCountByStatus(String status) {
    return _orders.where((order) => order.status == status).length;
  }

  double getTotalRevenue() {
    return _orders
        .where((order) => order.status == 'delivered')
        .fold(0.0, (sum, order) => sum + order.totalAmount);
  }

  int getTotalOrdersToday() {
    final today = DateTime.now();
    return _orders.where((order) {
      return order.createdAt.year == today.year &&
          order.createdAt.month == today.month &&
          order.createdAt.day == today.day;
    }).length;
  }
}
