import 'dart:io';
import 'dart:typed_data';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../config/firebase_config.dart';

class FirebaseService {
  static final FirebaseStorage _storage = FirebaseConfig.storage;
  static final FirebaseAuth _auth = FirebaseConfig.auth;

  /// Upload image to Firebase Storage
  /// Returns the download URL of the uploaded image
  static Future<String> uploadImage({
    required File imageFile,
    required String path,
    String? customFileName,
  }) async {
    try {
      // Generate unique filename if not provided
      final fileName = customFileName ?? 
          '${DateTime.now().millisecondsSinceEpoch}_${imageFile.path.split('/').last}';
      
      final fullPath = '$path/$fileName';
      
      // Create reference to Firebase Storage
      final ref = _storage.ref().child(fullPath);
      
      // Upload file
      final uploadTask = ref.putFile(imageFile);
      
      // Wait for upload to complete
      final snapshot = await uploadTask;
      
      // Get download URL
      final downloadUrl = await snapshot.ref.getDownloadURL();
      
      print('✅ Image uploaded successfully: $downloadUrl');
      return downloadUrl;
      
    } catch (e) {
      print('❌ Error uploading image: $e');
      throw Exception('Failed to upload image: $e');
    }
  }

  /// Upload image from bytes (useful for web)
  static Future<String> uploadImageFromBytes({
    required Uint8List imageBytes,
    required String path,
    required String fileName,
    String contentType = 'image/jpeg',
  }) async {
    try {
      final fullPath = '$path/$fileName';
      
      // Create reference to Firebase Storage
      final ref = _storage.ref().child(fullPath);
      
      // Upload bytes with metadata
      final uploadTask = ref.putData(
        imageBytes,
        SettableMetadata(contentType: contentType),
      );
      
      // Wait for upload to complete
      final snapshot = await uploadTask;
      
      // Get download URL
      final downloadUrl = await snapshot.ref.getDownloadURL();
      
      print('✅ Image uploaded successfully from bytes: $downloadUrl');
      return downloadUrl;
      
    } catch (e) {
      print('❌ Error uploading image from bytes: $e');
      throw Exception('Failed to upload image: $e');
    }
  }

  /// Delete image from Firebase Storage
  static Future<void> deleteImage(String imageUrl) async {
    try {
      // Extract path from URL
      final uri = Uri.parse(imageUrl);
      final pathSegments = uri.pathSegments;
      
      // Find the path after '/o/'
      final oIndex = pathSegments.indexOf('o');
      if (oIndex == -1 || oIndex + 1 >= pathSegments.length) {
        throw Exception('Invalid Firebase Storage URL');
      }
      
      final encodedPath = pathSegments[oIndex + 1];
      final decodedPath = Uri.decodeComponent(encodedPath);
      
      // Create reference and delete
      final ref = _storage.ref().child(decodedPath);
      await ref.delete();
      
      print('✅ Image deleted successfully: $imageUrl');
      
    } catch (e) {
      print('❌ Error deleting image: $e');
      throw Exception('Failed to delete image: $e');
    }
  }

  /// Get download URL for a file path
  static Future<String> getDownloadUrl(String path) async {
    try {
      final ref = _storage.ref().child(path);
      final url = await ref.getDownloadURL();
      return url;
    } catch (e) {
      print('❌ Error getting download URL: $e');
      throw Exception('Failed to get download URL: $e');
    }
  }

  /// Check if image exists in Firebase Storage
  static Future<bool> imageExists(String path) async {
    try {
      final ref = _storage.ref().child(path);
      await ref.getMetadata();
      return true;
    } catch (e) {
      return false;
    }
  }

  /// Create a custom token for authentication (if needed)
  static Future<void> signInWithCustomToken(String customToken) async {
    try {
      await _auth.signInWithCustomToken(customToken);
      print('✅ Signed in with custom token');
    } catch (e) {
      print('❌ Error signing in with custom token: $e');
      throw Exception('Failed to sign in: $e');
    }
  }

  /// Sign out from Firebase Auth
  static Future<void> signOut() async {
    try {
      await _auth.signOut();
      print('✅ Signed out from Firebase');
    } catch (e) {
      print('❌ Error signing out: $e');
    }
  }

  /// Get current Firebase user
  static User? get currentUser => _auth.currentUser;

  /// Check if user is signed in
  static bool get isSignedIn => _auth.currentUser != null;

  /// Listen to auth state changes
  static Stream<User?> get authStateChanges => _auth.authStateChanges();
}

/// Helper class for image upload progress tracking
class ImageUploadProgress {
  final int bytesTransferred;
  final int totalBytes;
  final double percentage;
  final TaskState state;

  ImageUploadProgress({
    required this.bytesTransferred,
    required this.totalBytes,
    required this.percentage,
    required this.state,
  });

  factory ImageUploadProgress.fromSnapshot(TaskSnapshot snapshot) {
    final percentage = snapshot.totalBytes > 0 
        ? (snapshot.bytesTransferred / snapshot.totalBytes) * 100
        : 0.0;
    
    return ImageUploadProgress(
      bytesTransferred: snapshot.bytesTransferred,
      totalBytes: snapshot.totalBytes,
      percentage: percentage,
      state: snapshot.state,
    );
  }

  bool get isComplete => state == TaskState.success;
  bool get isError => state == TaskState.error;
  bool get isRunning => state == TaskState.running;
  bool get isPaused => state == TaskState.paused;
}
