import React, { useState } from 'react';
import { useQuery } from 'react-query';
import { apiService } from '../services/apiService';
import {
  ChartBarIcon,
  CurrencyDollarIcon,
  ShoppingBagIcon,
  CheckCircleIcon,
  XCircleIcon,
  StarIcon,
  TruckIcon,
} from '@heroicons/react/24/outline';

function StatCard({ title, value, icon: Icon, color, subtitle }) {
  return (
    <div className="card">
      <div className="flex items-center">
        <div className={`p-3 rounded-lg ${color}`}>
          <Icon className="h-6 w-6 text-white" />
        </div>
        <div className="ml-4">
          <p className="text-sm font-medium text-gray-600">{title}</p>
          <p className="text-2xl font-bold text-gray-900">{value}</p>
          {subtitle && <p className="text-sm text-gray-500">{subtitle}</p>}
        </div>
      </div>
    </div>
  );
}

function TrendChart({ data, title }) {
  if (!data || data.length === 0) {
    return (
      <div className="text-center py-8 text-gray-500">
        No data available for {title}
      </div>
    );
  }

  const maxValue = Math.max(...data.map(item => item.orders || item.revenue || 0));

  return (
    <div className="space-y-4">
      <h3 className="text-lg font-medium text-gray-900">{title}</h3>
      <div className="space-y-2">
        {data.slice(0, 10).map((item, index) => (
          <div key={index} className="flex items-center justify-between">
            <div className="flex-1">
              <div className="flex justify-between items-center mb-1">
                <span className="text-sm font-medium text-gray-700">
                  {item.date ? new Date(item.date).toLocaleDateString() : item.restaurant_name}
                </span>
                <span className="text-sm text-gray-500">
                  {item.orders ? `${item.orders} orders` : `$${parseFloat(item.revenue || 0).toFixed(2)}`}
                </span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className="bg-primary-600 h-2 rounded-full"
                  style={{
                    width: `${((item.orders || item.revenue || 0) / maxValue) * 100}%`
                  }}
                ></div>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}

function StatusChart({ data }) {
  if (!data || data.length === 0) {
    return (
      <div className="text-center py-8 text-gray-500">
        No status data available
      </div>
    );
  }

  const total = data.reduce((sum, item) => sum + parseInt(item.count), 0);
  
  const statusColors = {
    pending: 'bg-yellow-500',
    confirmed: 'bg-blue-500',
    preparing: 'bg-orange-500',
    ready: 'bg-green-500',
    picked_up: 'bg-purple-500',
    delivered: 'bg-green-600',
    cancelled: 'bg-red-500',
  };

  return (
    <div className="space-y-4">
      <h3 className="text-lg font-medium text-gray-900">Orders by Status</h3>
      <div className="space-y-3">
        {data.map((item, index) => {
          const percentage = total > 0 ? (parseInt(item.count) / total) * 100 : 0;
          return (
            <div key={index} className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className={`w-4 h-4 rounded ${statusColors[item.status] || 'bg-gray-400'}`}></div>
                <span className="text-sm font-medium text-gray-700 capitalize">
                  {item.status.replace('_', ' ')}
                </span>
              </div>
              <div className="flex items-center space-x-2">
                <span className="text-sm text-gray-500">{item.count}</span>
                <span className="text-xs text-gray-400">({percentage.toFixed(1)}%)</span>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
}

function Analytics() {
  const [selectedPeriod, setSelectedPeriod] = useState('7d');

  const { data: analytics, isLoading, error } = useQuery(
    ['order-analytics', selectedPeriod],
    () => apiService.getOrderAnalytics(selectedPeriod),
    {
      refetchInterval: 60000, // Refresh every minute
    }
  );

  const { data: ratingAnalytics, isLoading: ratingLoading } = useQuery(
    ['rating-analytics'],
    () => apiService.getRatingAnalytics(),
    {
      refetchInterval: 300000, // Refresh every 5 minutes
    }
  );

  const periods = [
    { value: '7d', label: 'Last 7 Days' },
    { value: '30d', label: 'Last 30 Days' },
    { value: '90d', label: 'Last 90 Days' },
  ];

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-8">
        <p className="text-red-600">Error loading analytics: {error.message}</p>
      </div>
    );
  }

  const stats = analytics?.totalStats || {};

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Analytics</h1>
          <p className="text-gray-600">Order and revenue insights</p>
        </div>
        <div>
          <select
            value={selectedPeriod}
            onChange={(e) => setSelectedPeriod(e.target.value)}
            className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
          >
            {periods.map((period) => (
              <option key={period.value} value={period.value}>
                {period.label}
              </option>
            ))}
          </select>
        </div>
      </div>

      {/* Overview Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatCard
          title="Total Orders"
          value={stats.total_orders || 0}
          icon={ShoppingBagIcon}
          color="bg-blue-500"
        />
        <StatCard
          title="Total Revenue"
          value={`$${parseFloat(stats.total_revenue || 0).toFixed(2)}`}
          icon={CurrencyDollarIcon}
          color="bg-green-500"
        />
        <StatCard
          title="Completed Orders"
          value={stats.completed_orders || 0}
          icon={CheckCircleIcon}
          color="bg-green-600"
          subtitle={`${stats.total_orders > 0 ? ((stats.completed_orders / stats.total_orders) * 100).toFixed(1) : 0}% completion rate`}
        />
        <StatCard
          title="Cancelled Orders"
          value={stats.cancelled_orders || 0}
          icon={XCircleIcon}
          color="bg-red-500"
          subtitle={`${stats.total_orders > 0 ? ((stats.cancelled_orders / stats.total_orders) * 100).toFixed(1) : 0}% cancellation rate`}
        />
      </div>

      {/* Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Order Status Distribution */}
        <div className="card">
          <StatusChart data={analytics?.statusStats} />
        </div>

        {/* Daily Trends */}
        <div className="card">
          <TrendChart 
            data={analytics?.trendStats} 
            title="Daily Order Trends" 
          />
        </div>
      </div>

      {/* Top Restaurants */}
      <div className="card">
        <TrendChart 
          data={analytics?.topRestaurants} 
          title="Top Performing Restaurants" 
        />
      </div>

      {/* Detailed Stats Table */}
      <div className="card">
        <div className="mb-4">
          <h3 className="text-lg font-medium text-gray-900">Detailed Statistics</h3>
        </div>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Metric
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Value
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Percentage
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              <tr>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                  Average Order Value
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  ${stats.total_orders > 0 ? (stats.total_revenue / stats.total_orders).toFixed(2) : '0.00'}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  -
                </td>
              </tr>
              <tr>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                  Successful Delivery Rate
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {stats.completed_orders || 0} / {stats.total_orders || 0}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {stats.total_orders > 0 ? ((stats.completed_orders / stats.total_orders) * 100).toFixed(1) : 0}%
                </td>
              </tr>
              <tr>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                  Cancellation Rate
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {stats.cancelled_orders || 0} / {stats.total_orders || 0}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {stats.total_orders > 0 ? ((stats.cancelled_orders / stats.total_orders) * 100).toFixed(1) : 0}%
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      {/* Rating Analytics Section */}
      {ratingAnalytics && (
        <div className="space-y-6">
          <h2 className="text-xl font-bold text-gray-900">Rating Analytics</h2>

          {/* Rating Overview Stats */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <StatCard
              title="Average Food Rating"
              value={ratingAnalytics.averageFoodRating?.toFixed(1) || '0.0'}
              icon={StarIcon}
              color="bg-yellow-500"
              subtitle={`${ratingAnalytics.totalFoodRatings || 0} total ratings`}
            />
            <StatCard
              title="Average Driver Rating"
              value={ratingAnalytics.averageDriverRating?.toFixed(1) || '0.0'}
              icon={TruckIcon}
              color="bg-blue-500"
              subtitle={`${ratingAnalytics.totalDriverRatings || 0} total ratings`}
            />
            <StatCard
              title="Overall Rating"
              value={ratingAnalytics.overallRating?.toFixed(1) || '0.0'}
              icon={StarIcon}
              color="bg-green-500"
              subtitle="Combined average"
            />
          </div>

          {/* Top and Low Rated Items */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Top Rated Items */}
            <div className="card">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Top Rated Food Items</h3>
              <div className="space-y-3">
                {ratingAnalytics.topRatedItems?.slice(0, 5).map((item, index) => (
                  <div key={item.id} className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                    <div className="flex-1">
                      <p className="font-medium text-gray-900">{item.name}</p>
                      <p className="text-sm text-gray-600">{item.restaurant_name}</p>
                    </div>
                    <div className="flex items-center space-x-2">
                      <div className="flex items-center">
                        <StarIcon className="h-4 w-4 text-yellow-400 fill-current" />
                        <span className="ml-1 text-sm font-medium">{item.average_rating?.toFixed(1)}</span>
                      </div>
                      <span className="text-xs text-gray-500">({item.total_ratings})</span>
                    </div>
                  </div>
                )) || (
                  <p className="text-gray-500 text-center py-4">No rated items yet</p>
                )}
              </div>
            </div>

            {/* Low Rated Items */}
            <div className="card">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Items Needing Improvement</h3>
              <div className="space-y-3">
                {ratingAnalytics.lowRatedItems?.slice(0, 5).map((item, index) => (
                  <div key={item.id} className="flex items-center justify-between p-3 bg-red-50 rounded-lg">
                    <div className="flex-1">
                      <p className="font-medium text-gray-900">{item.name}</p>
                      <p className="text-sm text-gray-600">{item.restaurant_name}</p>
                    </div>
                    <div className="flex items-center space-x-2">
                      <div className="flex items-center">
                        <StarIcon className="h-4 w-4 text-yellow-400 fill-current" />
                        <span className="ml-1 text-sm font-medium">{item.average_rating?.toFixed(1)}</span>
                      </div>
                      <span className="text-xs text-gray-500">({item.total_ratings})</span>
                    </div>
                  </div>
                )) || (
                  <p className="text-gray-500 text-center py-4">No rated items yet</p>
                )}
              </div>
            </div>
          </div>

          {/* Top Rated Drivers */}
          <div className="card">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Top Rated Drivers</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {ratingAnalytics.topRatedDrivers?.slice(0, 6).map((driver, index) => (
                <div key={driver.id} className="p-4 bg-blue-50 rounded-lg">
                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center">
                      <span className="text-white font-medium">
                        {driver.full_name?.charAt(0).toUpperCase() || 'D'}
                      </span>
                    </div>
                    <div className="flex-1">
                      <p className="font-medium text-gray-900">{driver.full_name}</p>
                      <div className="flex items-center space-x-1">
                        <StarIcon className="h-4 w-4 text-yellow-400 fill-current" />
                        <span className="text-sm font-medium">{driver.rating?.toFixed(1)}</span>
                        <span className="text-xs text-gray-500">({driver.total_ratings})</span>
                      </div>
                    </div>
                  </div>
                </div>
              )) || (
                <p className="text-gray-500 text-center py-4 col-span-full">No rated drivers yet</p>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

export default Analytics;
