import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:firebase_auth/firebase_auth.dart';

class FirebaseConfig {
  static FirebaseApp? _app;
  static FirebaseStorage? _storage;
  static FirebaseAuth? _auth;

  // Firebase configuration options
  static const FirebaseOptions _firebaseOptions = FirebaseOptions(
    apiKey: 'AIzaSyDdYQ-PNUxbgiyg-6wqnBGxVRJZ1AfTPp4',
    authDomain: 'brsima-30138.firebaseapp.com',
    projectId: 'brsima-30138',
    storageBucket: 'brsima-30138.firebasestorage.app',
    messagingSenderId: '120480442939',
    appId: '1:120480442939:android:724b7d367228916520ca1e',
  );

  /// Initialize Firebase
  static Future<void> initialize() async {
    try {
      _app = await Firebase.initializeApp(
        name: 'brsima-driver',
        options: _firebaseOptions,
      );

      _storage = FirebaseStorage.instanceFor(app: _app!);
      _auth = FirebaseAuth.instanceFor(app: _app!);

      print('✅ Firebase initialized successfully for Driver app');
    } catch (e) {
      print('❌ Firebase initialization failed: $e');
      rethrow;
    }
  }

  /// Get Firebase Storage instance
  static FirebaseStorage get storage {
    if (_storage == null) {
      throw Exception(
        'Firebase not initialized. Call FirebaseConfig.initialize() first.',
      );
    }
    return _storage!;
  }

  /// Get Firebase Auth instance
  static FirebaseAuth get auth {
    if (_auth == null) {
      throw Exception(
        'Firebase not initialized. Call FirebaseConfig.initialize() first.',
      );
    }
    return _auth!;
  }

  /// Get Firebase App instance
  static FirebaseApp get app {
    if (_app == null) {
      throw Exception(
        'Firebase not initialized. Call FirebaseConfig.initialize() first.',
      );
    }
    return _app!;
  }

  /// Check if Firebase is initialized
  static bool get isInitialized => _app != null;
}

/// Firebase Storage paths for driver-related images
class FirebaseStoragePaths {
  static const String drivers = 'drivers';
  static const String temp = 'temp';

  /// Get driver profile image path
  static String driverProfileImage(String driverId) {
    return '$drivers/$driverId/profile';
  }

  /// Get driver vehicle image path
  static String driverVehicleImage(String driverId) {
    return '$drivers/$driverId/vehicle';
  }

  /// Get driver license image path
  static String driverLicenseImage(String driverId) {
    return '$drivers/$driverId/license';
  }

  /// Get temporary upload path
  static String tempUpload(String userId) {
    return '$temp/$userId';
  }
}
