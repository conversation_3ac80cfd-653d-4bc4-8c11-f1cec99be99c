import 'package:flutter/foundation.dart';
import 'package:geolocator/geolocator.dart';
import 'package:geocoding/geocoding.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

class LocationProvider extends ChangeNotifier {
  Position? _currentPosition;
  String? _currentAddress;
  bool _isLoading = false;
  String? _error;
  GoogleMapController? _mapController;
  
  // Getters
  Position? get currentPosition => _currentPosition;
  String? get currentAddress => _currentAddress;
  bool get isLoading => _isLoading;
  String? get error => _error;
  GoogleMapController? get mapController => _mapController;
  
  LatLng? get currentLatLng {
    if (_currentPosition != null) {
      return LatLng(_currentPosition!.latitude, _currentPosition!.longitude);
    }
    return null;
  }

  void setMapController(GoogleMapController controller) {
    _mapController = controller;
    notifyListeners();
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String? error) {
    _error = error;
    notifyListeners();
  }

  void _setPosition(Position position) {
    _currentPosition = position;
    notifyListeners();
  }

  void _setAddress(String address) {
    _currentAddress = address;
    notifyListeners();
  }

  /// Check and request location permissions
  Future<bool> checkLocationPermissions() async {
    try {
      LocationPermission permission = await Geolocator.checkPermission();
      
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          _setError('Location permissions are denied');
          return false;
        }
      }

      if (permission == LocationPermission.deniedForever) {
        _setError('Location permissions are permanently denied. Please enable them in settings.');
        return false;
      }

      return true;
    } catch (e) {
      _setError('Failed to check location permissions: $e');
      return false;
    }
  }

  /// Get current location
  Future<bool> getCurrentLocation() async {
    try {
      _setLoading(true);
      _setError(null);

      // Check permissions first
      if (!await checkLocationPermissions()) {
        return false;
      }

      // Check if location services are enabled
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        _setError('Location services are disabled. Please enable them in settings.');
        return false;
      }

      // Get current position
      Position position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
        timeLimit: const Duration(seconds: 10),
      );

      _setPosition(position);

      // Get address from coordinates
      await getAddressFromCoordinates(position.latitude, position.longitude);

      return true;
    } catch (e) {
      _setError('Failed to get current location: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// Get address from coordinates
  Future<String?> getAddressFromCoordinates(double latitude, double longitude) async {
    try {
      List<Placemark> placemarks = await placemarkFromCoordinates(latitude, longitude);
      
      if (placemarks.isNotEmpty) {
        Placemark place = placemarks[0];
        String address = '';
        
        if (place.street != null && place.street!.isNotEmpty) {
          address += '${place.street}, ';
        }
        if (place.locality != null && place.locality!.isNotEmpty) {
          address += '${place.locality}, ';
        }
        if (place.administrativeArea != null && place.administrativeArea!.isNotEmpty) {
          address += '${place.administrativeArea}, ';
        }
        if (place.country != null && place.country!.isNotEmpty) {
          address += place.country!;
        }
        
        // Remove trailing comma and space
        address = address.replaceAll(RegExp(r', $'), '');
        
        _setAddress(address);
        return address;
      }
      return null;
    } catch (e) {
      print('Error getting address from coordinates: $e');
      return null;
    }
  }

  /// Set location from map tap
  Future<void> setLocationFromMap(LatLng position) async {
    try {
      _setLoading(true);
      _setError(null);

      // Create Position object from LatLng
      Position newPosition = Position(
        latitude: position.latitude,
        longitude: position.longitude,
        timestamp: DateTime.now(),
        accuracy: 0,
        altitude: 0,
        altitudeAccuracy: 0,
        heading: 0,
        headingAccuracy: 0,
        speed: 0,
        speedAccuracy: 0,
      );

      _setPosition(newPosition);

      // Get address for the selected location
      await getAddressFromCoordinates(position.latitude, position.longitude);

      // Move map camera to selected location
      if (_mapController != null) {
        await _mapController!.animateCamera(
          CameraUpdate.newLatLng(position),
        );
      }
    } catch (e) {
      _setError('Failed to set location: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Move map camera to specific location
  Future<void> moveToLocation(LatLng location, {double zoom = 16.0}) async {
    if (_mapController != null) {
      await _mapController!.animateCamera(
        CameraUpdate.newCameraPosition(
          CameraPosition(
            target: location,
            zoom: zoom,
          ),
        ),
      );
    }
  }

  /// Clear current location data
  void clearLocation() {
    _currentPosition = null;
    _currentAddress = null;
    _error = null;
    notifyListeners();
  }

  /// Dispose resources
  @override
  void dispose() {
    _mapController?.dispose();
    super.dispose();
  }
}
