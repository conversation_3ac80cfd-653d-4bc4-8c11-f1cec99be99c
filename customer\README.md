# BRSIMA Customer App 

Flutter application for customers to register, complete their profile, and order food from restaurants.

##  **Implemented Features**

###  **Authentication System**
-  **Email/Password Registration** - Create new customer accounts
-  **Login System** - Secure JWT-based authentication
-  **Profile Setup** - Complete customer profile with name, phone, and location
-  **Location Services** - Get current location automatically
-  **Form Validation** - Comprehensive input validation
-  **Error Handling** - User-friendly error messages
-  **State Management** - Provider pattern for auth state
-  **Persistent Login** - Stay logged in between app sessions

###  **User Interface**
-  **Modern Design** - Clean, professional UI with Material Design 3
-  **Custom Theme** - Orange brand colors with Google Fonts
-  **Responsive Layout** - Works on different screen sizes
-  **Loading States** - Smooth loading indicators
-  **Navigation Flow** - Seamless navigation between screens

###  **Home Dashboard**
-  **Welcome Screen** - Personalized greeting with user info
-  **Quick Actions** - Easy access to main features
-  **Profile Management** - View and manage user profile
-  **Logout Functionality** - Secure logout with confirmation

##  **Getting Started**

### Prerequisites
- Flutter SDK (3.8.1 or higher)
- Android Studio / VS Code
- BRSIMA Backend Server running on `http://localhost:3000`

### Installation

1. **Install dependencies:**
   ```bash
   flutter pub get
   ```

2. **Run the app:**
   ```bash
   flutter run
   ```

###  **Configuration**

The app is configured to connect to the backend at `http://localhost:3000`. Make sure the backend server is running before testing the app.

##  **Project Structure**

```bash
customer/
├── lib/
│   ├── main.dart                    # App entry point with routing
│   ├── models/
│   │   └── user.dart               # User and CustomerProfile models
│   ├── providers/
│   │   └── auth_provider.dart      # Authentication state management
│   ├── services/
│   │   └── api_service.dart        # HTTP API service
│   ├── screens/
│   │   ├── auth/
│   │   │   ├── login_screen.dart   # Login UI
│   │   │   └── register_screen.dart # Registration UI
│   │   ├── profile/
│   │   │   └── profile_setup_screen.dart # Profile completion
│   │   ├── home/
│   │   │   └── home_screen.dart    # Main dashboard
│   │   └── splash/
│   │       └── splash_screen.dart  # App splash screen
│   └── ...
├── android/
│   └── app/src/main/AndroidManifest.xml # Location permissions
└── pubspec.yaml                    # Dependencies
```

##  **Backend Integration**

### API Endpoints Used:
- `POST /api/auth/register` - User registration
- `POST /api/auth/login` - User login
- `GET /api/auth/me` - Get current user
- `POST /api/customer/profile` - Complete customer profile
- `PATCH /api/customer/location` - Update location

### Authentication Flow:
1. **Registration** → Email/Password → JWT Token
2. **Profile Setup** → Name, Phone, Address, Location
3. **Home Dashboard** → Full app access

##  **Dependencies**

- `provider: ^6.1.1` - State management
- `http: ^1.1.0` - HTTP requests
- `shared_preferences: ^2.2.2` - Local storage
- `geolocator: ^10.1.0` - Location services
- `geocoding: ^2.1.1` - Address from coordinates
- `google_fonts: ^6.1.0` - Custom fonts

##  **Next Steps (To be implemented)**

-  Restaurant browsing and search
-  Shopping cart and checkout
-  Real-time order tracking
-  Payment integration
-  Reviews and ratings
-  Push notifications
-  Order history

##  **Testing the App**

1. **Start the backend server:**
   ```bash
   cd ../backend
   npm start
   ```

2. **Run the Flutter app:**
   ```bash
   flutter run
   ```

3. **Test the flow:**
   - Register with email/password
   - Complete profile setup
   - Access home dashboard
   - Test logout functionality

##  **Troubleshooting**

- **Location not working**: Ensure location permissions are granted
- **API errors**: Check if backend server is running on port 3000
- **Build errors**: Run `flutter clean && flutter pub get`

---

**Status**:  **Authentication system fully implemented and ready for testing!**
