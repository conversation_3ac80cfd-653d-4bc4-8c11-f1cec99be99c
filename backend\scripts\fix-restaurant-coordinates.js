const { pool } = require('../config/database');

async function fixRestaurantCoordinates() {
  const client = await pool.connect();
  try {
    console.log('🔧 Fixing restaurant coordinates...\n');
    
    // Fix "hellooo" restaurant coordinates (ID: 2)
    // Move it to be near the other restaurants in Halabja area
    await client.query(`
      UPDATE restaurant_profiles 
      SET latitude = 35.17450000, longitude = 45.97900000, updated_at = CURRENT_TIMESTAMP
      WHERE id = 2
    `);
    
    // Fix "kaka" restaurant coordinates (ID: 11)  
    // Move it to be near the other restaurants in Halabja area
    await client.query(`
      UPDATE restaurant_profiles 
      SET latitude = 35.17460000, longitude = 45.97910000, updated_at = CURRENT_TIMESTAMP
      WHERE id = 11
    `);
    
    console.log('✅ Updated coordinates for "hellooo" restaurant');
    console.log('✅ Updated coordinates for "kaka" restaurant');
    
    // Verify the changes
    const updatedRestaurants = await client.query(`
      SELECT id, restaurant_name, latitude, longitude
      FROM restaurant_profiles 
      WHERE id IN (2, 11)
      ORDER BY id
    `);
    
    console.log('\n📍 Updated coordinates:');
    updatedRestaurants.rows.forEach(r => {
      console.log(`  ${r.id}: ${r.restaurant_name} -> (${r.latitude}, ${r.longitude})`);
    });
    
    // Test distance calculation again
    console.log('\n🧪 Testing distance calculation with updated coordinates...');
    const testQuery = await client.query(`
      SELECT 
        id, restaurant_name, latitude, longitude,
        (
          6371 * acos(
            cos(radians(35.174)) * cos(radians(latitude)) *
            cos(radians(longitude) - radians(45.978)) +
            sin(radians(35.174)) * sin(radians(latitude))
          )
        ) AS distance
      FROM restaurant_profiles 
      WHERE id IN (2, 11)
      ORDER BY distance
    `);
    
    console.log('📏 New distance calculations:');
    testQuery.rows.forEach(r => {
      const distance = parseFloat(r.distance);
      const withinRadius = distance <= 10;
      const status = withinRadius ? '✅ NOW WITHIN RADIUS' : '❌ STILL TOO FAR';
      console.log(`${status} ${r.restaurant_name}: ${distance.toFixed(2)} km`);
    });
    
    console.log('\n🎉 Restaurant coordinates fixed! All restaurants should now be visible to customers.');
    
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    client.release();
    process.exit(0);
  }
}

fixRestaurantCoordinates();
