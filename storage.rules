rules_version = '2';

// Firebase Storage Security Rules for BRSIMA
service firebase.storage {
  match /b/{bucket}/o {

    // Helper functions
    function isAuthenticated() {
      return request.auth != null;
    }

    function isAdmin() {
      return isAuthenticated() && request.auth.token.userType == 'admin';
    }

    function isRestaurantOwner(restaurantId) {
      return isAuthenticated() &&
             request.auth.token.userType == 'restaurant' &&
             request.auth.token.restaurantId == restaurantId;
    }

    function isDriver() {
      return isAuthenticated() && request.auth.token.userType == 'driver';
    }

    function isCustomer() {
      return isAuthenticated() && request.auth.token.userType == 'customer';
    }

    function isValidImageFile() {
      return request.resource.contentType.matches('image/.*');
    }

    function isValidImageSize() {
      return request.resource.size < 5 * 1024 * 1024; // 5MB limit
    }

    function isValidImage() {
      return isValidImageFile() && isValidImageSize();
    }

    // Restaurant images - can be uploaded by restaurant owners and admins
    match /restaurants/{restaurantId}/{imageType}/{fileName} {
      allow read: if true; // Public read access for all restaurant images
      allow write: if (isRestaurantOwner(restaurantId) || isAdmin()) &&
                      isValidImage();
      allow delete: if isRestaurantOwner(restaurantId) || isAdmin();
    }

    // Menu item images - can be uploaded by restaurant owners and admins
    match /menu-items/{restaurantId}/{fileName} {
      allow read: if true; // Public read access for all menu item images
      allow write: if ((isRestaurantOwner(restaurantId) || isAdmin()) &&
                      isValidImage()) ||
                      // Temporary: Allow unauthenticated uploads for admin panel testing
                      (request.auth == null && isValidImage());
      allow delete: if isRestaurantOwner(restaurantId) || isAdmin();
    }

    // Driver profile images - can be uploaded by driver owners and admins
    match /drivers/{driverId}/{imageType}/{fileName} {
      allow read: if true; // Public read access for driver profile images
      allow write: if (isAuthenticated() &&
                      (request.auth.uid == driverId || isAdmin())) &&
                      isValidImage();
      allow delete: if isAuthenticated() &&
                       (request.auth.uid == driverId || isAdmin());
    }

    // Admin uploads - only admins can upload
    match /admin/{category}/{fileName} {
      allow read: if true; // Public read access
      allow write: if isAdmin() && isValidImage();
      allow delete: if isAdmin();
    }

    // Temporary uploads - authenticated users can upload temporarily
    match /temp/{userId}/{fileName} {
      allow read, write: if isAuthenticated() &&
                            request.auth.uid == userId &&
                            isValidImage();
      allow delete: if isAuthenticated() && request.auth.uid == userId;
    }

    // Customer profile images (if needed in future)
    match /customers/{customerId}/{fileName} {
      allow read: if true; // Public read access
      allow write: if (isAuthenticated() &&
                      (request.auth.uid == customerId || isAdmin())) &&
                      isValidImage();
      allow delete: if isAuthenticated() &&
                       (request.auth.uid == customerId || isAdmin());
    }

    // Default deny all other paths
    match /{allPaths=**} {
      allow read, write: if false;
    }
  }
}
