const { pool } = require('../config/database');

async function openAllRestaurants() {
  const client = await pool.connect();
  try {
    console.log('🔄 Opening all restaurants...');
    
    // Update all restaurants to be open
    const result = await client.query(`
      UPDATE restaurant_profiles 
      SET is_open = true, updated_at = CURRENT_TIMESTAMP 
      WHERE is_open = false
    `);
    
    console.log(`✅ Opened ${result.rowCount} restaurants`);
    
    // Show current restaurant status
    const restaurants = await client.query(`
      SELECT restaurant_name, is_open, created_at 
      FROM restaurant_profiles 
      ORDER BY created_at DESC
    `);
    
    console.log('\n📋 Current restaurant status:');
    restaurants.rows.forEach(r => {
      const status = r.is_open ? '🟢 OPEN' : '🔴 CLOSED';
      console.log(`  ${r.restaurant_name}: ${status}`);
    });
    
    console.log('\n🎉 All restaurants are now visible to customers!');
    
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    client.release();
    process.exit(0);
  }
}

openAllRestaurants();
