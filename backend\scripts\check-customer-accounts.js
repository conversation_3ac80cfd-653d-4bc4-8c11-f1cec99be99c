const { pool } = require('../config/database');

async function checkCustomerAccounts() {
  const client = await pool.connect();
  try {
    console.log('🔍 Checking all customer accounts and profiles...\n');
    
    // Get all users with customer type
    const users = await client.query(`
      SELECT u.id as user_id, u.email, u.user_type, u.is_active, u.created_at,
             cp.id as customer_profile_id, cp.full_name
      FROM users u
      LEFT JOIN customer_profiles cp ON u.id = cp.user_id
      WHERE u.user_type = 'customer'
      ORDER BY u.created_at DESC
    `);
    
    console.log(`Found ${users.rows.length} customer accounts:\n`);
    
    users.rows.forEach((user, index) => {
      const status = user.is_active ? '✅ ACTIVE' : '❌ INACTIVE';
      const profile = user.customer_profile_id ? '✅ HAS PROFILE' : '❌ NO PROFILE';
      
      console.log(`${index + 1}. ${user.email}`);
      console.log(`   User ID: ${user.user_id} | ${status} | ${profile}`);
      if (user.customer_profile_id) {
        console.log(`   Profile ID: ${user.customer_profile_id} | Name: ${user.full_name}`);
      }
      console.log(`   Created: ${user.created_at}`);
      console.log('');
    });
    
    // Check for users without customer profiles
    const usersWithoutProfiles = users.rows.filter(u => !u.customer_profile_id);
    if (usersWithoutProfiles.length > 0) {
      console.log('⚠️  USERS WITHOUT CUSTOMER PROFILES:');
      usersWithoutProfiles.forEach(user => {
        console.log(`   - ${user.email} (User ID: ${user.user_id})`);
      });
      console.log('\nThese users will get 400 errors when trying to submit ratings!\n');
    }
    
    // Check recent orders to see which customers have completed orders
    console.log('📦 Recent completed orders by customers:');
    const recentOrders = await client.query(`
      SELECT o.id, o.customer_id, o.status, o.created_at,
             cp.full_name, u.email
      FROM orders o
      JOIN customer_profiles cp ON o.customer_id = cp.id
      JOIN users u ON cp.user_id = u.id
      WHERE o.status = 'delivered'
      ORDER BY o.created_at DESC
      LIMIT 10
    `);
    
    recentOrders.rows.forEach(order => {
      console.log(`   Order ${order.id}: ${order.full_name} (${order.email}) - ${order.created_at}`);
    });
    
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    client.release();
    process.exit(0);
  }
}

checkCustomerAccounts();
