const { uploadFile, deleteFile } = require('../config/firebase');
const { query } = require('../config/database');

/**
 * Image Service for managing restaurant and menu item images
 */
class ImageService {
  
  /**
   * Update restaurant profile image
   * @param {number} restaurantId - Restaurant ID
   * @param {string} imageUrl - New image URL
   * @param {string} imageType - Type of image (profile, banner, etc.)
   * @returns {Promise<Object>} Updated restaurant data
   */
  async updateRestaurantImage(restaurantId, imageUrl, imageType = 'profile') {
    try {
      let updateField;
      switch (imageType) {
        case 'banner':
          updateField = 'banner_image_url';
          break;
        case 'profile':
        default:
          updateField = 'profile_image_url';
          break;
      }

      // First, check if the field exists in the table
      // If not, we'll add it to the database schema
      const result = await query(
        `UPDATE restaurant_profiles 
         SET ${updateField} = $1, updated_at = CURRENT_TIMESTAMP 
         WHERE id = $2 
         RETURNING *`,
        [imageUrl, restaurantId]
      );

      if (result.rows.length === 0) {
        throw new Error('Restaurant not found');
      }

      return result.rows[0];
    } catch (error) {
      // If column doesn't exist, we need to add it
      if (error.message.includes('column') && error.message.includes('does not exist')) {
        console.log(`Adding ${updateField} column to restaurant_profiles table`);
        await this.addImageColumnsToRestaurants();
        // Retry the update
        return this.updateRestaurantImage(restaurantId, imageUrl, imageType);
      }
      throw error;
    }
  }

  /**
   * Update menu item image
   * @param {number} menuItemId - Menu item ID
   * @param {string} imageUrl - New image URL
   * @returns {Promise<Object>} Updated menu item data
   */
  async updateMenuItemImage(menuItemId, imageUrl) {
    try {
      const result = await query(
        `UPDATE menu_items 
         SET image_url = $1, updated_at = CURRENT_TIMESTAMP 
         WHERE id = $2 
         RETURNING *`,
        [imageUrl, menuItemId]
      );

      if (result.rows.length === 0) {
        throw new Error('Menu item not found');
      }

      return result.rows[0];
    } catch (error) {
      throw error;
    }
  }

  /**
   * Delete image and update database
   * @param {string} uploadPath - Firebase storage path
   * @param {string} entityType - 'restaurant' or 'menu_item'
   * @param {number} entityId - Entity ID
   * @param {string} imageType - Image type (for restaurants)
   */
  async deleteImageAndUpdateDB(uploadPath, entityType, entityId, imageType = 'profile') {
    try {
      // Delete from Firebase Storage
      await deleteFile(uploadPath);

      // Update database to remove image URL
      if (entityType === 'restaurant') {
        let updateField = imageType === 'banner' ? 'banner_image_url' : 'profile_image_url';
        await query(
          `UPDATE restaurant_profiles 
           SET ${updateField} = NULL, updated_at = CURRENT_TIMESTAMP 
           WHERE id = $1`,
          [entityId]
        );
      } else if (entityType === 'menu_item') {
        await query(
          `UPDATE menu_items 
           SET image_url = NULL, updated_at = CURRENT_TIMESTAMP 
           WHERE id = $1`,
          [entityId]
        );
      }

      return { success: true, message: 'Image deleted successfully' };
    } catch (error) {
      throw error;
    }
  }

  /**
   * Get all images for a restaurant
   * @param {number} restaurantId - Restaurant ID
   * @returns {Promise<Object>} Restaurant images data
   */
  async getRestaurantImages(restaurantId) {
    try {
      const restaurantResult = await query(
        `SELECT profile_image_url, banner_image_url 
         FROM restaurant_profiles 
         WHERE id = $1`,
        [restaurantId]
      );

      const menuItemsResult = await query(
        `SELECT id, name, image_url 
         FROM menu_items 
         WHERE restaurant_id = $1 AND image_url IS NOT NULL`,
        [restaurantId]
      );

      return {
        restaurant: restaurantResult.rows[0] || {},
        menuItems: menuItemsResult.rows
      };
    } catch (error) {
      throw error;
    }
  }

  /**
   * Add image columns to restaurant_profiles table if they don't exist
   */
  async addImageColumnsToRestaurants() {
    try {
      // Add profile_image_url column
      await query(`
        ALTER TABLE restaurant_profiles 
        ADD COLUMN IF NOT EXISTS profile_image_url TEXT
      `);

      // Add banner_image_url column
      await query(`
        ALTER TABLE restaurant_profiles 
        ADD COLUMN IF NOT EXISTS banner_image_url TEXT
      `);

      console.log('✅ Image columns added to restaurant_profiles table');
    } catch (error) {
      console.error('Error adding image columns:', error);
      throw error;
    }
  }

  /**
   * Migrate existing image URLs (if any) to Firebase Storage
   * This is a utility function for data migration
   */
  async migrateExistingImages() {
    try {
      // Get all menu items with image URLs
      const menuItems = await query(
        'SELECT id, image_url FROM menu_items WHERE image_url IS NOT NULL'
      );

      console.log(`Found ${menuItems.rows.length} menu items with images to potentially migrate`);

      // Note: This would require downloading existing images and re-uploading to Firebase
      // Implementation depends on where current images are stored
      
      return {
        menuItemsFound: menuItems.rows.length,
        message: 'Migration analysis complete. Manual migration may be required for existing images.'
      };
    } catch (error) {
      throw error;
    }
  }

  /**
   * Validate image URL belongs to this project's Firebase Storage
   * @param {string} imageUrl - Image URL to validate
   * @returns {boolean} True if URL is from our Firebase Storage
   */
  isFirebaseStorageUrl(imageUrl) {
    if (!imageUrl) return false;
    
    const firebaseDomain = process.env.FIREBASE_STORAGE_BUCKET || 'brsima.appspot.com';
    return imageUrl.includes(`storage.googleapis.com/${firebaseDomain}`) ||
           imageUrl.includes(`${firebaseDomain}/o/`);
  }

  /**
   * Extract Firebase Storage path from URL
   * @param {string} imageUrl - Firebase Storage URL
   * @returns {string|null} Storage path or null if invalid
   */
  extractStoragePathFromUrl(imageUrl) {
    try {
      if (!this.isFirebaseStorageUrl(imageUrl)) return null;
      
      // Extract path from Firebase Storage URL
      const url = new URL(imageUrl);
      const pathMatch = url.pathname.match(/\/o\/(.+?)(\?|$)/);
      
      if (pathMatch) {
        return decodeURIComponent(pathMatch[1]);
      }
      
      return null;
    } catch (error) {
      return null;
    }
  }
}

module.exports = new ImageService();
