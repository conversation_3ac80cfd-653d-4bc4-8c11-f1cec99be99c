import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:url_launcher/url_launcher.dart';
import '../../models/user.dart';
import '../../utils/location_utils.dart';

class OrderLocationMapScreen extends StatefulWidget {
  final Order order;
  final RestaurantProfile? restaurantLocation;

  const OrderLocationMapScreen({
    super.key,
    required this.order,
    this.restaurantLocation,
  });

  @override
  State<OrderLocationMapScreen> createState() => _OrderLocationMapScreenState();
}

class _OrderLocationMapScreenState extends State<OrderLocationMapScreen> {
  GoogleMapController? _mapController;
  Set<Marker> _markers = {};

  @override
  void initState() {
    super.initState();
    _setupMarkers();
  }

  void _setupMarkers() {
    _markers = {};

    // Add restaurant marker
    if (widget.restaurantLocation?.latitude != null &&
        widget.restaurantLocation?.longitude != null) {
      _markers.add(
        Marker(
          markerId: const MarkerId('restaurant'),
          position: LatLng(
            widget.restaurantLocation!.latitude!,
            widget.restaurantLocation!.longitude!,
          ),
          infoWindow: InfoWindow(
            title: widget.restaurantLocation!.restaurantName,
            snippet: 'Restaurant Location',
          ),
          icon: BitmapDescriptor.defaultMarkerWithHue(
            BitmapDescriptor.hueOrange,
          ),
        ),
      );
    }

    // Add delivery marker
    if (widget.order.deliveryLatitude != null &&
        widget.order.deliveryLongitude != null) {
      _markers.add(
        Marker(
          markerId: const MarkerId('delivery'),
          position: LatLng(
            widget.order.deliveryLatitude!,
            widget.order.deliveryLongitude!,
          ),
          infoWindow: InfoWindow(
            title: 'Delivery Location',
            snippet: widget.order.deliveryAddress,
          ),
          icon: BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueRed),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    // Calculate center point and zoom level
    LatLng center;
    double zoom = 14.0;

    if (widget.restaurantLocation?.latitude != null &&
        widget.restaurantLocation?.longitude != null &&
        widget.order.deliveryLatitude != null &&
        widget.order.deliveryLongitude != null) {
      // Calculate center between restaurant and delivery location
      center = LatLng(
        (widget.restaurantLocation!.latitude! +
                widget.order.deliveryLatitude!) /
            2,
        (widget.restaurantLocation!.longitude! +
                widget.order.deliveryLongitude!) /
            2,
      );

      // Calculate distance to adjust zoom
      final distance = LocationUtils.calculateDistance(
        widget.restaurantLocation!.latitude!,
        widget.restaurantLocation!.longitude!,
        widget.order.deliveryLatitude!,
        widget.order.deliveryLongitude!,
      );

      // Adjust zoom based on distance
      if (distance > 10) {
        zoom = 10.0;
      } else if (distance > 5) {
        zoom = 12.0;
      } else if (distance > 2) {
        zoom = 14.0;
      } else {
        zoom = 16.0;
      }
    } else if (widget.order.deliveryLatitude != null &&
        widget.order.deliveryLongitude != null) {
      center = LatLng(
        widget.order.deliveryLatitude!,
        widget.order.deliveryLongitude!,
      );
    } else {
      // Fallback to a default location
      center = const LatLng(37.7749, -122.4194); // San Francisco
    }

    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Order #${widget.order.orderNumber}',
          style: GoogleFonts.poppins(fontWeight: FontWeight.w600),
        ),
        backgroundColor: Colors.orange,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            onPressed: _openInGoogleMaps,
            icon: const Icon(Icons.directions),
            tooltip: 'Get Directions',
          ),
        ],
      ),
      body: Column(
        children: [
          // Map
          Expanded(
            child: GoogleMap(
              initialCameraPosition: CameraPosition(target: center, zoom: zoom),
              markers: _markers,
              onMapCreated: (GoogleMapController controller) {
                _mapController = controller;
                _fitMarkersInView();
              },
              mapType: MapType.normal,
              myLocationEnabled: false,
              myLocationButtonEnabled: false,
              zoomControlsEnabled: true,
            ),
          ),

          // Bottom info panel
          _buildBottomPanel(),
        ],
      ),
    );
  }

  Widget _buildBottomPanel() {
    double? distance;
    String? direction;
    int? estimatedTime;

    if (widget.restaurantLocation?.latitude != null &&
        widget.restaurantLocation?.longitude != null &&
        widget.order.deliveryLatitude != null &&
        widget.order.deliveryLongitude != null) {
      distance = LocationUtils.calculateDistance(
        widget.restaurantLocation!.latitude!,
        widget.restaurantLocation!.longitude!,
        widget.order.deliveryLatitude!,
        widget.order.deliveryLongitude!,
      );

      final bearing = LocationUtils.calculateBearing(
        widget.restaurantLocation!.latitude!,
        widget.restaurantLocation!.longitude!,
        widget.order.deliveryLatitude!,
        widget.order.deliveryLongitude!,
      );

      direction = LocationUtils.getDirection(bearing);
      estimatedTime = LocationUtils.calculateEstimatedDeliveryMinutes(distance);
    }

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.2),
            spreadRadius: 1,
            blurRadius: 4,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            children: [
              Icon(Icons.location_on, color: Colors.orange, size: 24),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  widget.order.deliveryAddress,
                  style: GoogleFonts.poppins(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),

          if (distance != null &&
              direction != null &&
              estimatedTime != null) ...[
            const SizedBox(height: 12),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _buildInfoItem(
                  'Distance',
                  LocationUtils.formatDistance(distance),
                  Icons.straighten,
                  Colors.blue,
                ),
                _buildInfoItem(
                  'Direction',
                  direction,
                  Icons.navigation,
                  Colors.green,
                ),
                _buildInfoItem(
                  'Est. Time',
                  '${estimatedTime}min',
                  Icons.access_time,
                  Colors.orange,
                ),
              ],
            ),
          ],

          const SizedBox(height: 16),
          SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: _openInGoogleMaps,
              icon: const Icon(Icons.directions, size: 20),
              label: const Text('Get Directions'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.orange,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoItem(
    String label,
    String value,
    IconData icon,
    Color color,
  ) {
    return Column(
      children: [
        Icon(icon, color: color, size: 20),
        const SizedBox(height: 4),
        Text(
          value,
          style: GoogleFonts.poppins(fontSize: 14, fontWeight: FontWeight.bold),
        ),
        Text(
          label,
          style: GoogleFonts.poppins(fontSize: 12, color: Colors.grey[600]),
        ),
      ],
    );
  }

  void _fitMarkersInView() {
    if (_mapController != null && _markers.length > 1) {
      final bounds = _calculateBounds();
      _mapController!.animateCamera(
        CameraUpdate.newLatLngBounds(bounds, 100.0),
      );
    }
  }

  LatLngBounds _calculateBounds() {
    double minLat = double.infinity;
    double maxLat = -double.infinity;
    double minLng = double.infinity;
    double maxLng = -double.infinity;

    for (final marker in _markers) {
      minLat = math.min(minLat, marker.position.latitude);
      maxLat = math.max(maxLat, marker.position.latitude);
      minLng = math.min(minLng, marker.position.longitude);
      maxLng = math.max(maxLng, marker.position.longitude);
    }

    return LatLngBounds(
      southwest: LatLng(minLat, minLng),
      northeast: LatLng(maxLat, maxLng),
    );
  }

  void _openInGoogleMaps() async {
    if (widget.order.deliveryLatitude != null &&
        widget.order.deliveryLongitude != null) {
      final url =
          'https://www.google.com/maps/dir/?api=1&destination=${widget.order.deliveryLatitude},${widget.order.deliveryLongitude}';

      if (await canLaunchUrl(Uri.parse(url))) {
        await launchUrl(Uri.parse(url), mode: LaunchMode.externalApplication);
      }
    }
  }
}
