const bcrypt = require('bcryptjs');
const { Pool } = require('pg');

// Use the same database configuration as the main app
const pool = new Pool({
  connectionString: process.env.DATABASE_URL || 'postgresql://postgres:<EMAIL>:54231/railway',
  ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false
});

async function createTestRestaurants() {
  const client = await pool.connect();
  
  try {
    console.log('🍕 Creating test restaurants...');
    
    const restaurants = [
      {
        email: '<EMAIL>',
        password: 'password123',
        restaurantName: 'Pizza Palace',
        description: 'Authentic Italian pizzas with fresh ingredients',
        cuisineType: 'Italian',
        address: '123 Main Street, Downtown',
        latitude: 35.1742371,
        longitude: 45.9789801,
        phone: '+1234567890'
      },
      {
        email: '<EMAIL>',
        password: 'password123',
        restaurantName: 'Burger House',
        description: 'Gourmet burgers and crispy fries',
        cuisineType: 'American',
        address: '456 Oak Avenue, City Center',
        latitude: 35.1752371,
        longitude: 45.9799801,
        phone: '+1234567891'
      },
      {
        email: '<EMAIL>',
        password: 'password123',
        restaurantName: 'Sushi Zen',
        description: 'Fresh sushi and Japanese cuisine',
        cuisineType: 'Japanese',
        address: '789 Pine Street, Uptown',
        latitude: 35.1732371,
        longitude: 45.9779801,
        phone: '+1234567892'
      },
      {
        email: '<EMAIL>',
        password: 'password123',
        restaurantName: 'Taco Fiesta',
        description: 'Authentic Mexican tacos and burritos',
        cuisineType: 'Mexican',
        address: '321 Elm Street, Westside',
        latitude: 35.1762371,
        longitude: 45.9769801,
        phone: '+1234567893'
      }
    ];

    for (const restaurant of restaurants) {
      console.log(`Creating restaurant: ${restaurant.restaurantName}`);
      
      // Hash password
      const hashedPassword = await bcrypt.hash(restaurant.password, 10);
      
      // Create user
      const userResult = await client.query(
        'INSERT INTO users (email, password_hash, user_type) VALUES ($1, $2, $3) RETURNING id',
        [restaurant.email, hashedPassword, 'restaurant']
      );
      
      const userId = userResult.rows[0].id;
      
      // Create restaurant profile
      await client.query(
        `INSERT INTO restaurant_profiles 
         (user_id, restaurant_name, description, cuisine_type, address, latitude, longitude, phone, is_open, rating, total_orders) 
         VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)`,
        [
          userId,
          restaurant.restaurantName,
          restaurant.description,
          restaurant.cuisineType,
          restaurant.address,
          restaurant.latitude,
          restaurant.longitude,
          restaurant.phone,
          true, // is_open
          4.5, // rating
          Math.floor(Math.random() * 100) + 10 // total_orders
        ]
      );
      
      console.log(`✅ Created restaurant: ${restaurant.restaurantName}`);
    }
    
    console.log('🎉 All test restaurants created successfully!');
    
  } catch (error) {
    console.error('❌ Error creating test restaurants:', error);
  } finally {
    client.release();
    await pool.end();
  }
}

// Run the script
createTestRestaurants();
