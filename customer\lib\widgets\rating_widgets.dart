import 'package:flutter/material.dart';

class StarRating extends StatelessWidget {
  final int rating;
  final Function(int) onRatingChanged;
  final int maxRating;
  final double size;
  final Color activeColor;
  final Color inactiveColor;
  final bool allowHalfRating;
  final bool readOnly;

  const StarRating({
    Key? key,
    required this.rating,
    required this.onRatingChanged,
    this.maxRating = 5,
    this.size = 32.0,
    this.activeColor = Colors.amber,
    this.inactiveColor = Colors.grey,
    this.allowHalfRating = false,
    this.readOnly = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: List.generate(maxRating, (index) {
        return GestureDetector(
          onTap: readOnly ? null : () => onRatingChanged(index + 1),
          child: Icon(
            index < rating ? Icons.star : Icons.star_border,
            size: size,
            color: index < rating ? activeColor : inactiveColor,
          ),
        );
      }),
    );
  }
}

class DisplayStarRating extends StatelessWidget {
  final double rating;
  final int maxRating;
  final double size;
  final Color activeColor;
  final Color inactiveColor;
  final bool showRatingText;
  final TextStyle? textStyle;

  const DisplayStarRating({
    Key? key,
    required this.rating,
    this.maxRating = 5,
    this.size = 16.0,
    this.activeColor = Colors.amber,
    this.inactiveColor = Colors.grey,
    this.showRatingText = true,
    this.textStyle,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        ...List.generate(maxRating, (index) {
          double starRating = rating - index;
          IconData iconData;
          
          if (starRating >= 1) {
            iconData = Icons.star;
          } else if (starRating >= 0.5) {
            iconData = Icons.star_half;
          } else {
            iconData = Icons.star_border;
          }

          return Icon(
            iconData,
            size: size,
            color: starRating > 0 ? activeColor : inactiveColor,
          );
        }),
        if (showRatingText) ...[
          const SizedBox(width: 4),
          Text(
            rating.toStringAsFixed(1),
            style: textStyle ?? TextStyle(
              fontSize: size * 0.75,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ],
    );
  }
}

class RatingCard extends StatelessWidget {
  final String customerName;
  final int rating;
  final String? reviewText;
  final DateTime createdAt;
  final EdgeInsets? padding;

  const RatingCard({
    Key? key,
    required this.customerName,
    required this.rating,
    this.reviewText,
    required this.createdAt,
    this.padding,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: padding ?? const EdgeInsets.symmetric(vertical: 4),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                CircleAvatar(
                  backgroundColor: Colors.orange,
                  radius: 16,
                  child: Text(
                    customerName.isNotEmpty ? customerName[0].toUpperCase() : 'U',
                    style: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        customerName,
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 14,
                        ),
                      ),
                      Text(
                        _formatDate(createdAt),
                        style: TextStyle(
                          color: Colors.grey[600],
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                ),
                DisplayStarRating(
                  rating: rating.toDouble(),
                  size: 16,
                  showRatingText: false,
                ),
              ],
            ),
            if (reviewText != null && reviewText!.isNotEmpty) ...[
              const SizedBox(height: 12),
              Text(
                reviewText!,
                style: const TextStyle(fontSize: 14),
              ),
            ],
          ],
        ),
      ),
    );
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays > 0) {
      return '${difference.inDays} day${difference.inDays == 1 ? '' : 's'} ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} hour${difference.inHours == 1 ? '' : 's'} ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes} minute${difference.inMinutes == 1 ? '' : 's'} ago';
    } else {
      return 'Just now';
    }
  }
}

class RatingSummary extends StatelessWidget {
  final double averageRating;
  final int totalRatings;
  final Map<int, int>? ratingDistribution;
  final bool showDistribution;

  const RatingSummary({
    Key? key,
    required this.averageRating,
    required this.totalRatings,
    this.ratingDistribution,
    this.showDistribution = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Row(
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      averageRating.toStringAsFixed(1),
                      style: const TextStyle(
                        fontSize: 32,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    DisplayStarRating(
                      rating: averageRating,
                      size: 20,
                      showRatingText: false,
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '$totalRatings review${totalRatings == 1 ? '' : 's'}',
                      style: TextStyle(
                        color: Colors.grey[600],
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
                if (showDistribution && ratingDistribution != null) ...[
                  const SizedBox(width: 24),
                  Expanded(
                    child: _buildRatingDistribution(),
                  ),
                ],
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRatingDistribution() {
    if (ratingDistribution == null || totalRatings == 0) {
      return const SizedBox.shrink();
    }

    return Column(
      children: List.generate(5, (index) {
        final starCount = 5 - index;
        final count = ratingDistribution![starCount] ?? 0;
        final percentage = totalRatings > 0 ? count / totalRatings : 0.0;

        return Padding(
          padding: const EdgeInsets.symmetric(vertical: 2),
          child: Row(
            children: [
              Text(
                '$starCount',
                style: const TextStyle(fontSize: 12),
              ),
              const SizedBox(width: 4),
              const Icon(Icons.star, size: 12, color: Colors.amber),
              const SizedBox(width: 8),
              Expanded(
                child: LinearProgressIndicator(
                  value: percentage,
                  backgroundColor: Colors.grey[300],
                  valueColor: const AlwaysStoppedAnimation<Color>(Colors.amber),
                ),
              ),
              const SizedBox(width: 8),
              Text(
                count.toString(),
                style: const TextStyle(fontSize: 12),
              ),
            ],
          ),
        );
      }),
    );
  }
}

class QuickRatingButton extends StatelessWidget {
  final VoidCallback onPressed;
  final bool hasRatings;
  final String text;

  const QuickRatingButton({
    Key? key,
    required this.onPressed,
    this.hasRatings = false,
    this.text = 'Rate Order',
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return OutlinedButton.icon(
      onPressed: onPressed,
      icon: Icon(
        hasRatings ? Icons.star : Icons.star_border,
        size: 16,
        color: hasRatings ? Colors.amber : Colors.grey,
      ),
      label: Text(
        hasRatings ? 'View Ratings' : text,
        style: TextStyle(
          color: hasRatings ? Colors.amber : Colors.grey[700],
          fontSize: 12,
        ),
      ),
      style: OutlinedButton.styleFrom(
        side: BorderSide(
          color: hasRatings ? Colors.amber : Colors.grey,
        ),
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      ),
    );
  }
}
