import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from 'react-query';
import { useForm } from 'react-hook-form';
import { apiService } from '../services/apiService';
import toast from 'react-hot-toast';
import ImageUpload from '../components/ImageUpload';
import {
  PlusIcon,
  PencilIcon,
  TrashIcon,
  TagIcon,
  CurrencyDollarIcon,
  ClockIcon,
} from '@heroicons/react/24/outline';

function CreateCategoryModal({ isOpen, onClose, restaurantId }) {
  const { register, handleSubmit, formState: { errors }, reset } = useForm();
  const queryClient = useQueryClient();

  const createMutation = useMutation(
    (data) => apiService.createCategory(restaurantId, data),
    {
      onSuccess: () => {
        queryClient.invalidateQueries(['restaurant-menu', restaurantId]);
        toast.success('Category created successfully!');
        reset();
        onClose();
      },
      onError: (error) => {
        toast.error(error.error || 'Failed to create category');
      },
    }
  );

  const onSubmit = (data) => {
    createMutation.mutate(data);
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div className="relative top-20 mx-auto p-5 border w-full max-w-md shadow-lg rounded-md bg-white">
        <div className="mt-3">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-medium text-gray-900">Create New Category</h3>
            <button onClick={onClose} className="text-gray-400 hover:text-gray-600">×</button>
          </div>

          <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700">Category Name</label>
              <input
                {...register('name', { required: 'Category name is required' })}
                type="text"
                className="input-field"
                placeholder="e.g., Appetizers, Main Courses"
              />
              {errors.name && <p className="text-red-600 text-sm">{errors.name.message}</p>}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700">Description</label>
              <textarea
                {...register('description')}
                rows={3}
                className="input-field"
                placeholder="Brief description of the category"
              />
            </div>

            <div className="flex justify-end space-x-3 pt-4">
              <button type="button" onClick={onClose} className="btn-secondary">
                Cancel
              </button>
              <button
                type="submit"
                disabled={createMutation.isLoading}
                className="btn-primary"
              >
                {createMutation.isLoading ? 'Creating...' : 'Create Category'}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}

function CreateMenuItemModal({ isOpen, onClose, restaurantId, categories }) {
  const { register, handleSubmit, formState: { errors }, reset } = useForm();
  const queryClient = useQueryClient();
  const [imageUrl, setImageUrl] = useState('');

  const createMutation = useMutation(
    (data) => apiService.createMenuItem(restaurantId, data),
    {
      onSuccess: () => {
        queryClient.invalidateQueries(['restaurant-menu', restaurantId]);
        toast.success('Menu item created successfully!');
        reset();
        setImageUrl('');
        onClose();
      },
      onError: (error) => {
        toast.error(error.error || 'Failed to create menu item');
      },
    }
  );

  const onSubmit = (data) => {
    createMutation.mutate({
      ...data,
      price: parseFloat(data.price),
      preparationTime: parseInt(data.preparationTime) || 15,
      categoryId: data.categoryId || null,
      imageUrl: imageUrl,
    });
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div className="relative top-20 mx-auto p-5 border w-full max-w-2xl shadow-lg rounded-md bg-white">
        <div className="mt-3">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-medium text-gray-900">Create New Menu Item</h3>
            <button onClick={onClose} className="text-gray-400 hover:text-gray-600">×</button>
          </div>

          <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700">Item Name</label>
                <input
                  {...register('name', { required: 'Item name is required' })}
                  type="text"
                  className="input-field"
                  placeholder="e.g., Margherita Pizza"
                />
                {errors.name && <p className="text-red-600 text-sm">{errors.name.message}</p>}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700">Price ($)</label>
                <input
                  {...register('price', { 
                    required: 'Price is required',
                    min: { value: 0.01, message: 'Price must be greater than 0' }
                  })}
                  type="number"
                  step="0.01"
                  className="input-field"
                  placeholder="12.99"
                />
                {errors.price && <p className="text-red-600 text-sm">{errors.price.message}</p>}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700">Category</label>
                <select {...register('categoryId')} className="input-field">
                  <option value="">No Category</option>
                  {categories.map((category) => (
                    <option key={category.id} value={category.id}>
                      {category.name}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700">Preparation Time (minutes)</label>
                <input
                  {...register('preparationTime')}
                  type="number"
                  className="input-field"
                  placeholder="15"
                  defaultValue="15"
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700">Description</label>
              <textarea
                {...register('description')}
                rows={3}
                className="input-field"
                placeholder="Describe the menu item..."
              />
            </div>

            <ImageUpload
              value={imageUrl}
              onChange={setImageUrl}
              uploadPath={`menu-items/${restaurantId}`}
              placeholder="Upload menu item image"
              disabled={createMutation.isLoading}
            />

            <div className="flex items-center">
              <input
                {...register('isAvailable')}
                type="checkbox"
                className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                defaultChecked
              />
              <label className="ml-2 block text-sm text-gray-900">
                Available for ordering
              </label>
            </div>

            <div className="flex justify-end space-x-3 pt-4">
              <button type="button" onClick={onClose} className="btn-secondary">
                Cancel
              </button>
              <button
                type="submit"
                disabled={createMutation.isLoading}
                className="btn-primary"
              >
                {createMutation.isLoading ? 'Creating...' : 'Create Item'}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}

function MenuItemCard({ item, onEdit, onDelete }) {
  return (
    <div className="card">
      <div className="flex items-start justify-between">
        <div className="flex-1">
          <div className="flex items-start space-x-3">
            {item.image_url && (
              <img
                src={item.image_url}
                alt={item.name}
                className="w-16 h-16 object-cover rounded-lg"
              />
            )}
            <div className="flex-1">
              <h4 className="text-lg font-medium text-gray-900">{item.name}</h4>
              {item.description && (
                <p className="text-sm text-gray-600 mt-1">{item.description}</p>
              )}
              
              <div className="mt-2 flex items-center space-x-4">
                <div className="flex items-center text-sm text-gray-600">
                  <CurrencyDollarIcon className="h-4 w-4 mr-1" />
                  ${parseFloat(item.price).toFixed(2)}
                </div>
                <div className="flex items-center text-sm text-gray-600">
                  <ClockIcon className="h-4 w-4 mr-1" />
                  {item.preparation_time} min
                </div>
                <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                  item.is_available ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                }`}>
                  {item.is_available ? 'Available' : 'Unavailable'}
                </span>
              </div>
            </div>
          </div>
        </div>

        <div className="flex items-center space-x-2">
          <button
            onClick={() => onEdit(item)}
            className="text-blue-600 hover:text-blue-800"
          >
            <PencilIcon className="h-5 w-5" />
          </button>
          <button
            onClick={() => onDelete(item.id)}
            className="text-red-600 hover:text-red-800"
          >
            <TrashIcon className="h-5 w-5" />
          </button>
        </div>
      </div>
    </div>
  );
}

function RestaurantSelector({ restaurants, selectedRestaurant, onSelect }) {
  return (
    <div className="mb-6">
      <label className="block text-sm font-medium text-gray-700 mb-2">
        Select Restaurant
      </label>
      <select
        value={selectedRestaurant || ''}
        onChange={(e) => onSelect(e.target.value)}
        className="input-field max-w-md"
      >
        <option value="">Choose a restaurant...</option>
        {restaurants?.restaurants?.map((restaurant) => (
          <option key={restaurant.user.id} value={restaurant.profile.id}>
            {restaurant.profile.restaurantName}
          </option>
        ))}
      </select>
    </div>
  );
}

function Products() {
  const [selectedRestaurant, setSelectedRestaurant] = useState(null);
  const [showCreateCategory, setShowCreateCategory] = useState(false);
  const [showCreateMenuItem, setShowCreateMenuItem] = useState(false);
  const queryClient = useQueryClient();

  const { data: restaurants } = useQuery('restaurants', apiService.getRestaurants);
  
  const { data: menu, isLoading, error } = useQuery(
    ['restaurant-menu', selectedRestaurant],
    () => apiService.getRestaurantMenu(selectedRestaurant),
    {
      enabled: !!selectedRestaurant,
    }
  );

  const deleteItemMutation = useMutation(apiService.deleteMenuItem, {
    onSuccess: () => {
      queryClient.invalidateQueries(['restaurant-menu', selectedRestaurant]);
      toast.success('Menu item deleted successfully!');
    },
    onError: (error) => {
      toast.error(error.error || 'Failed to delete menu item');
    },
  });

  const handleDeleteItem = (itemId) => {
    if (window.confirm('Are you sure you want to delete this menu item?')) {
      deleteItemMutation.mutate(itemId);
    }
  };

  const handleEditItem = (item) => {
    // TODO: Implement edit functionality
    toast.info('Edit functionality coming soon!');
  };

  if (!selectedRestaurant) {
    return (
      <div className="space-y-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Product Management</h1>
          <p className="text-gray-600">Manage restaurant menus and menu items</p>
        </div>

        <RestaurantSelector
          restaurants={restaurants}
          selectedRestaurant={selectedRestaurant}
          onSelect={setSelectedRestaurant}
        />

        <div className="text-center py-12">
          <TagIcon className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">No restaurant selected</h3>
          <p className="mt-1 text-sm text-gray-500">
            Choose a restaurant to manage its menu and products.
          </p>
        </div>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-8">
        <p className="text-red-600">Error loading menu data</p>
      </div>
    );
  }

  const selectedRestaurantData = restaurants?.restaurants?.find(
    r => r.profile.id.toString() === selectedRestaurant
  );

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Product Management</h1>
          <p className="text-gray-600">
            Managing menu for: <span className="font-medium">{selectedRestaurantData?.profile.restaurantName}</span>
          </p>
        </div>
        <div className="flex space-x-3">
          <button
            onClick={() => setShowCreateCategory(true)}
            className="btn-secondary flex items-center"
          >
            <PlusIcon className="h-5 w-5 mr-2" />
            Add Category
          </button>
          <button
            onClick={() => setShowCreateMenuItem(true)}
            className="btn-primary flex items-center"
          >
            <PlusIcon className="h-5 w-5 mr-2" />
            Add Menu Item
          </button>
        </div>
      </div>

      <RestaurantSelector
        restaurants={restaurants}
        selectedRestaurant={selectedRestaurant}
        onSelect={setSelectedRestaurant}
      />

      <div className="space-y-8">
        {menu?.categories?.map((category) => (
          <div key={category.id || 'uncategorized'} className="space-y-4">
            <div className="border-b border-gray-200 pb-2">
              <h2 className="text-xl font-semibold text-gray-900">{category.name}</h2>
              {category.description && (
                <p className="text-sm text-gray-600">{category.description}</p>
              )}
              <p className="text-sm text-gray-500 mt-1">
                {category.items?.length || 0} items
              </p>
            </div>

            {category.items?.length > 0 ? (
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                {category.items.map((item) => (
                  <MenuItemCard
                    key={item.id}
                    item={item}
                    onEdit={handleEditItem}
                    onDelete={handleDeleteItem}
                  />
                ))}
              </div>
            ) : (
              <div className="text-center py-8 bg-gray-50 rounded-lg">
                <TagIcon className="mx-auto h-8 w-8 text-gray-400" />
                <p className="text-sm text-gray-500 mt-2">No items in this category</p>
              </div>
            )}
          </div>
        ))}

        {(!menu?.categories || menu.categories.length === 0) && (
          <div className="text-center py-12">
            <TagIcon className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No menu items</h3>
            <p className="mt-1 text-sm text-gray-500">
              Get started by creating categories and adding menu items.
            </p>
          </div>
        )}
      </div>

      <CreateCategoryModal
        isOpen={showCreateCategory}
        onClose={() => setShowCreateCategory(false)}
        restaurantId={selectedRestaurant}
      />

      <CreateMenuItemModal
        isOpen={showCreateMenuItem}
        onClose={() => setShowCreateMenuItem(false)}
        restaurantId={selectedRestaurant}
        categories={menu?.categories?.filter(c => c.id) || []}
      />
    </div>
  );
}

export default Products;
