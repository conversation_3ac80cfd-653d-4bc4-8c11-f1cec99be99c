# BRSIMA Admin Panel

A React-based admin panel for managing the BRSIMA food delivery system.

## Features

- **Authentication**: Secure admin login with JWT tokens
- **Dashboard**: Overview of system statistics and recent orders
- **Restaurant Management**: Create, view, and manage restaurant accounts
- **Driver Management**: Create, view, and manage driver accounts
- **User Status Control**: Activate/deactivate user accounts
- **Responsive Design**: Works on desktop and mobile devices

## Technology Stack

- **Frontend**: React 18 with Hooks
- **Routing**: React Router DOM
- **State Management**: React Query for server state
- **Forms**: React Hook Form with validation
- **Styling**: Tailwind CSS
- **Icons**: Heroicons
- **Notifications**: React Hot Toast
- **HTTP Client**: Axios

## Getting Started

### Prerequisites

- Node.js (v14 or higher)
- npm or yarn
- BRSIMA Backend server running

### Installation

1. Navigate to the admin directory:
   ```bash
   cd admin
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Create a `.env` file in the admin directory:
   ```env
   REACT_APP_API_URL=http://localhost:3000/api
   ```

4. Start the development server:
   ```bash
   npm start
   ```

5. Open your browser and navigate to `http://localhost:3001`

### Default Admin Account

To create an admin account, you'll need to manually insert an admin user into your database:

```sql
INSERT INTO users (email, password_hash, user_type, is_verified, is_active) 
VALUES ('<EMAIL>', '$2a$12$hashedpassword', 'admin', true, true);
```

Or use the backend API to register a user and then manually change their `user_type` to 'admin' in the database.

## Project Structure

```
src/
├── components/          # Reusable UI components
│   └── Layout.js       # Main layout with sidebar and navigation
├── contexts/           # React contexts
│   └── AuthContext.js  # Authentication context and provider
├── pages/              # Page components
│   ├── Dashboard.js    # Dashboard with statistics
│   ├── Drivers.js      # Driver management page
│   ├── Login.js        # Login page
│   └── Restaurants.js  # Restaurant management page
├── services/           # API services
│   └── apiService.js   # HTTP client and API methods
├── App.js              # Main app component with routing
├── index.css           # Global styles and Tailwind imports
└── index.js            # App entry point
```

## API Endpoints Used

### Authentication
- `POST /api/auth/login` - Admin login
- `GET /api/auth/me` - Get current user profile

### Admin Operations
- `GET /api/admin/stats` - Get system statistics
- `GET /api/admin/restaurants` - Get all restaurants
- `POST /api/admin/restaurants` - Create new restaurant
- `GET /api/admin/drivers` - Get all drivers
- `POST /api/admin/drivers` - Create new driver
- `GET /api/admin/customers` - Get all customers
- `PATCH /api/admin/users/:userId/status` - Toggle user active status

## Features Overview

### Dashboard
- System statistics (total customers, restaurants, drivers, orders)
- Recent orders table with status indicators
- Quick overview of system health

### Restaurant Management
- View all registered restaurants
- Create new restaurant accounts with complete profiles
- Toggle restaurant active/inactive status
- Display restaurant details (cuisine, location, rating, orders)

### Driver Management
- View all registered drivers
- Create new driver accounts with vehicle information
- Toggle driver active/inactive status
- Display driver details (vehicle info, earnings, deliveries, rating)

## Security Features

- JWT-based authentication
- Admin-only access control
- Automatic token refresh handling
- Secure API communication
- Input validation and sanitization

## Styling

The admin panel uses Tailwind CSS for styling with a professional, clean design:
- Responsive grid layouts
- Consistent color scheme with primary blue theme
- Interactive hover states and transitions
- Form validation styling
- Loading states and animations

## Development

### Available Scripts

- `npm start` - Start development server
- `npm build` - Build for production
- `npm test` - Run tests
- `npm eject` - Eject from Create React App

### Environment Variables

- `REACT_APP_API_URL` - Backend API base URL (default: http://localhost:3000/api)

## Deployment

1. Build the production version:
   ```bash
   npm run build
   ```

2. Deploy the `build` folder to your web server or hosting platform.

3. Ensure the backend API is accessible from your production domain.

## Contributing

1. Follow the existing code structure and naming conventions
2. Use TypeScript for new components (optional)
3. Add proper error handling for all API calls
4. Include loading states for better UX
5. Test on both desktop and mobile devices

## License

This project is part of the BRSIMA food delivery system.
