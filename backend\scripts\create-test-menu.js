const { Pool } = require('pg');

// Use the same database configuration as the main app
const pool = new Pool({
  connectionString: process.env.DATABASE_URL || 'postgresql://postgres:<EMAIL>:54231/railway',
  ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false
});

async function createTestMenu() {
  const client = await pool.connect();
  
  try {
    console.log('🍽️ Creating test menu items...');
    
    // Get restaurant IDs
    const restaurantsResult = await client.query(`
      SELECT rp.id, rp.restaurant_name 
      FROM restaurant_profiles rp 
      JOIN users u ON rp.user_id = u.id 
      WHERE u.user_type = 'restaurant'
    `);
    
    const restaurants = restaurantsResult.rows;
    console.log(`Found ${restaurants.length} restaurants`);
    
    for (const restaurant of restaurants) {
      console.log(`Creating menu for: ${restaurant.restaurant_name}`);
      
      let categories = [];
      let menuItems = [];
      
      if (restaurant.restaurant_name === 'Pizza Palace') {
        // Create categories
        const appetizersResult = await client.query(
          'INSERT INTO categories (restaurant_id, name, description) VALUES ($1, $2, $3) RETURNING id',
          [restaurant.id, 'Appetizers', 'Start your meal right']
        );
        const pizzasResult = await client.query(
          'INSERT INTO categories (restaurant_id, name, description) VALUES ($1, $2, $3) RETURNING id',
          [restaurant.id, 'Pizzas', 'Our signature pizzas']
        );
        
        categories = [
          { id: appetizersResult.rows[0].id, name: 'Appetizers' },
          { id: pizzasResult.rows[0].id, name: 'Pizzas' }
        ];
        
        menuItems = [
          { name: 'Garlic Bread', description: 'Fresh bread with garlic butter', price: 6.99, category_id: categories[0].id, prep_time: 10 },
          { name: 'Mozzarella Sticks', description: 'Crispy mozzarella with marinara sauce', price: 8.99, category_id: categories[0].id, prep_time: 12 },
          { name: 'Margherita Pizza', description: 'Fresh tomatoes, mozzarella, and basil', price: 14.99, category_id: categories[1].id, prep_time: 20 },
          { name: 'Pepperoni Pizza', description: 'Classic pepperoni with mozzarella', price: 16.99, category_id: categories[1].id, prep_time: 20 },
          { name: 'Supreme Pizza', description: 'Pepperoni, sausage, peppers, and mushrooms', price: 19.99, category_id: categories[1].id, prep_time: 25 }
        ];
      } else if (restaurant.restaurant_name === 'Burger House') {
        const burgersResult = await client.query(
          'INSERT INTO categories (restaurant_id, name, description) VALUES ($1, $2, $3) RETURNING id',
          [restaurant.id, 'Burgers', 'Gourmet burgers made fresh']
        );
        const sidesResult = await client.query(
          'INSERT INTO categories (restaurant_id, name, description) VALUES ($1, $2, $3) RETURNING id',
          [restaurant.id, 'Sides', 'Perfect sides for your meal']
        );
        
        categories = [
          { id: burgersResult.rows[0].id, name: 'Burgers' },
          { id: sidesResult.rows[0].id, name: 'Sides' }
        ];
        
        menuItems = [
          { name: 'Classic Burger', description: 'Beef patty with lettuce, tomato, and onion', price: 12.99, category_id: categories[0].id, prep_time: 15 },
          { name: 'Cheeseburger', description: 'Classic burger with melted cheese', price: 13.99, category_id: categories[0].id, prep_time: 15 },
          { name: 'BBQ Bacon Burger', description: 'Burger with BBQ sauce and crispy bacon', price: 16.99, category_id: categories[0].id, prep_time: 18 },
          { name: 'French Fries', description: 'Crispy golden fries', price: 4.99, category_id: categories[1].id, prep_time: 8 },
          { name: 'Onion Rings', description: 'Beer-battered onion rings', price: 5.99, category_id: categories[1].id, prep_time: 10 }
        ];
      } else if (restaurant.restaurant_name === 'Sushi Zen') {
        const sushiResult = await client.query(
          'INSERT INTO categories (restaurant_id, name, description) VALUES ($1, $2, $3) RETURNING id',
          [restaurant.id, 'Sushi Rolls', 'Fresh sushi rolls']
        );
        const appetizerResult = await client.query(
          'INSERT INTO categories (restaurant_id, name, description) VALUES ($1, $2, $3) RETURNING id',
          [restaurant.id, 'Appetizers', 'Japanese appetizers']
        );
        
        categories = [
          { id: sushiResult.rows[0].id, name: 'Sushi Rolls' },
          { id: appetizerResult.rows[0].id, name: 'Appetizers' }
        ];
        
        menuItems = [
          { name: 'California Roll', description: 'Crab, avocado, and cucumber', price: 8.99, category_id: categories[0].id, prep_time: 12 },
          { name: 'Salmon Roll', description: 'Fresh salmon and avocado', price: 10.99, category_id: categories[0].id, prep_time: 12 },
          { name: 'Dragon Roll', description: 'Eel, cucumber, topped with avocado', price: 14.99, category_id: categories[0].id, prep_time: 15 },
          { name: 'Edamame', description: 'Steamed soybeans with sea salt', price: 4.99, category_id: categories[1].id, prep_time: 5 },
          { name: 'Gyoza', description: 'Pan-fried pork dumplings', price: 7.99, category_id: categories[1].id, prep_time: 10 }
        ];
      } else if (restaurant.restaurant_name === 'Taco Fiesta') {
        const tacosResult = await client.query(
          'INSERT INTO categories (restaurant_id, name, description) VALUES ($1, $2, $3) RETURNING id',
          [restaurant.id, 'Tacos', 'Authentic Mexican tacos']
        );
        const burritosResult = await client.query(
          'INSERT INTO categories (restaurant_id, name, description) VALUES ($1, $2, $3) RETURNING id',
          [restaurant.id, 'Burritos', 'Large flour tortilla wraps']
        );
        
        categories = [
          { id: tacosResult.rows[0].id, name: 'Tacos' },
          { id: burritosResult.rows[0].id, name: 'Burritos' }
        ];
        
        menuItems = [
          { name: 'Beef Taco', description: 'Seasoned ground beef with lettuce and cheese', price: 3.99, category_id: categories[0].id, prep_time: 8 },
          { name: 'Chicken Taco', description: 'Grilled chicken with salsa and sour cream', price: 4.99, category_id: categories[0].id, prep_time: 8 },
          { name: 'Fish Taco', description: 'Grilled fish with cabbage slaw', price: 5.99, category_id: categories[0].id, prep_time: 10 },
          { name: 'Beef Burrito', description: 'Large burrito with beef, rice, and beans', price: 9.99, category_id: categories[1].id, prep_time: 12 },
          { name: 'Chicken Burrito', description: 'Grilled chicken burrito with fresh ingredients', price: 10.99, category_id: categories[1].id, prep_time: 12 }
        ];
      }
      
      // Insert menu items
      for (const item of menuItems) {
        await client.query(
          `INSERT INTO menu_items 
           (restaurant_id, category_id, name, description, price, preparation_time, is_available) 
           VALUES ($1, $2, $3, $4, $5, $6, $7)`,
          [
            restaurant.id,
            item.category_id,
            item.name,
            item.description,
            item.price,
            item.prep_time,
            true
          ]
        );
      }
      
      console.log(`✅ Created ${menuItems.length} menu items for ${restaurant.restaurant_name}`);
    }
    
    console.log('🎉 All test menu items created successfully!');
    
  } catch (error) {
    console.error('❌ Error creating test menu:', error);
  } finally {
    client.release();
    await pool.end();
  }
}

// Run the script
createTestMenu();
