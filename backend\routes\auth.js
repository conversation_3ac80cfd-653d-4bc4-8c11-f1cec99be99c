const express = require('express');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const rateLimit = require('express-rate-limit');
const { query } = require('../config/database');
const { validateRequest, schemas } = require('../middleware/validation');
const { authenticateToken } = require('../middleware/auth');

const router = express.Router();

// Rate limiting for auth endpoints
const authLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 5, // limit each IP to 5 requests per windowMs
  message: { error: 'Too many authentication attempts, please try again later.' }
});

// Register endpoint
router.post('/register', authLimiter, validateRequest(schemas.register), async (req, res) => {
  try {
    const { email, password, userType } = req.body;

    // Check if user already exists
    const existingUser = await query('SELECT id FROM users WHERE email = $1', [email]);
    
    if (existingUser.rows.length > 0) {
      return res.status(400).json({ error: 'User already exists with this email' });
    }

    // Hash password
    const saltRounds = 12;
    const passwordHash = await bcrypt.hash(password, saltRounds);

    // Create user
    const result = await query(
      'INSERT INTO users (email, password_hash, user_type) VALUES ($1, $2, $3) RETURNING id, email, user_type, created_at',
      [email, passwordHash, userType]
    );

    const user = result.rows[0];

    // Generate JWT token
    const token = jwt.sign(
      { userId: user.id, email: user.email, userType: user.user_type },
      process.env.JWT_SECRET,
      { expiresIn: process.env.JWT_EXPIRES_IN }
    );

    res.status(201).json({
      message: 'User registered successfully',
      user: {
        id: user.id,
        email: user.email,
        userType: user.user_type,
        createdAt: user.created_at
      },
      token,
      profileCompleted: false
    });

  } catch (error) {
    console.error('Registration error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Login endpoint
router.post('/login', authLimiter, validateRequest(schemas.login), async (req, res) => {
  try {
    const { email, password } = req.body;

    // Get user from database
    const result = await query(
      'SELECT id, email, password_hash, user_type, is_active FROM users WHERE email = $1',
      [email]
    );

    if (result.rows.length === 0) {
      return res.status(401).json({ error: 'Invalid email or password' });
    }

    const user = result.rows[0];

    if (!user.is_active) {
      return res.status(401).json({ error: 'Account is deactivated' });
    }

    // Verify password
    const isValidPassword = await bcrypt.compare(password, user.password_hash);
    
    if (!isValidPassword) {
      return res.status(401).json({ error: 'Invalid email or password' });
    }

    // Check if profile is completed
    let profileCompleted = false;
    let profileData = null;

    if (user.user_type === 'customer') {
      const profileResult = await query(
        'SELECT profile_completed, full_name, phone, address FROM customer_profiles WHERE user_id = $1',
        [user.id]
      );
      profileCompleted = profileResult.rows.length > 0 && profileResult.rows[0].profile_completed;
      profileData = profileResult.rows[0] || null;
    } else if (user.user_type === 'restaurant') {
      const profileResult = await query(
        'SELECT * FROM restaurant_profiles WHERE user_id = $1',
        [user.id]
      );
      profileCompleted = profileResult.rows.length > 0;
      profileData = profileResult.rows[0] || null;
    } else if (user.user_type === 'driver') {
      const profileResult = await query(
        'SELECT * FROM driver_profiles WHERE user_id = $1',
        [user.id]
      );
      profileCompleted = profileResult.rows.length > 0;
      profileData = profileResult.rows[0] || null;
    }

    // Generate JWT token
    const token = jwt.sign(
      { userId: user.id, email: user.email, userType: user.user_type },
      process.env.JWT_SECRET,
      { expiresIn: process.env.JWT_EXPIRES_IN }
    );

    res.json({
      message: 'Login successful',
      user: {
        id: user.id,
        email: user.email,
        userType: user.user_type
      },
      token,
      profileCompleted,
      profile: profileData
    });

  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Get current user profile
router.get('/me', authenticateToken, async (req, res) => {
  try {
    const { user } = req;
    
    let profileData = null;
    let profileCompleted = false;

    // Get profile data based on user type
    if (user.user_type === 'customer') {
      const result = await query(
        'SELECT * FROM customer_profiles WHERE user_id = $1',
        [user.id]
      );
      profileData = result.rows[0] || null;
      profileCompleted = profileData ? profileData.profile_completed : false;
    } else if (user.user_type === 'driver') {
      const result = await query(
        'SELECT * FROM driver_profiles WHERE user_id = $1',
        [user.id]
      );
      profileData = result.rows[0] || null;
      profileCompleted = profileData ? (
        profileData.full_name && 
        profileData.phone && 
        profileData.license_number && 
        profileData.vehicle_type && 
        profileData.vehicle_plate
      ) : false;
    } else if (user.user_type === 'restaurant') {
      const result = await query(
        'SELECT * FROM restaurant_profiles WHERE user_id = $1',
        [user.id]
      );
      profileData = result.rows[0] || null;
      profileCompleted = profileData ? (
        profileData.restaurant_name &&
        profileData.phone &&
        profileData.address &&
        profileData.cuisine_type
      ) : false;
    }

    res.json({
      user: {
        id: user.id,
        email: user.email,
        userType: user.user_type
      },
      profileCompleted,
      profile: profileData
    });

  } catch (error) {
    console.error('Get profile error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

module.exports = router;
