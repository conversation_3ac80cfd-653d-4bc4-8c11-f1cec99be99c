{"name": "brsima-admin", "version": "1.0.0", "private": true, "dependencies": {"@headlessui/react": "^1.7.13", "@heroicons/react": "^2.0.16", "@testing-library/jest-dom": "^5.16.4", "@testing-library/react": "^13.3.0", "@testing-library/user-event": "^13.5.0", "axios": "^1.3.4", "firebase": "^12.0.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.43.5", "react-hot-toast": "^2.4.0", "react-query": "^3.39.3", "react-router-dom": "^6.8.1", "react-scripts": "5.0.1", "tailwindcss": "^3.2.7", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"autoprefixer": "^10.4.14", "postcss": "^8.4.21"}, "proxy": "http://localhost:3000"}