import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import '../models/user.dart';

class ApiService {
  static String get baseUrl {
    if (kIsWeb) {
      return 'http://localhost:3000/api';
    } else if (Platform.isAndroid) {
      // For Android emulator
      //return 'http://********:3000/api';
      // For physical device, use your actual IP:
      return 'http://**************:3000/api';
    } else {
      return 'http://localhost:3000/api';
    }
  }

  // Retrieve saved JWT.
  // First try the new canonical key 'auth_token'.
  // Fall back to legacy 'token' to support users who still have the old value
  // in their SharedPreferences. This makes the migration seamless and avoids
  // type errors caused by an unexpectedly missing token.
  static Future<String?> getToken() async {
    final prefs = await SharedPreferences.getInstance();
    String? token = prefs.getString('auth_token');

    // Fallback for legacy key
    token ??= prefs.getString('token');
    return token;
  }

  // Save JWT under the new key and, temporarily, the legacy key so that
  // older code versions (if any) can still find it. The extra write can be
  // removed once every client is confirmed to use 'auth_token'.
  static Future<void> saveToken(String token) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('auth_token', token);
    // Write legacy key for backward compatibility
    await prefs.setString('token', token);
  }

  static Future<void> removeToken() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('auth_token');
    // Also remove legacy key if present
    await prefs.remove('token');
  }

  static Map<String, String> get defaultHeaders => {
    'Content-Type': 'application/json',
  };

  static Future<Map<String, String>> get authHeaders async {
    final token = await getToken();
    return {
      ...defaultHeaders,
      if (token != null) 'Authorization': 'Bearer $token',
    };
  }

  // Authentication
  static Future<Map<String, dynamic>> register({
    required String email,
    required String password,
  }) async {
    try {
      print('🔄 Attempting registration to: $baseUrl/auth/register');

      final response = await http.post(
        Uri.parse('$baseUrl/auth/register'),
        headers: defaultHeaders,
        body: jsonEncode({
          'email': email,
          'password': password,
          'userType': 'restaurant',
        }),
      );

      print('📡 Registration response status: ${response.statusCode}');
      print('📡 Registration response body: ${response.body}');

      final data = jsonDecode(response.body);

      if (response.statusCode == 201) {
        if (data['token'] != null) {
          await saveToken(data['token']);
        }
        return {'success': true, 'data': data};
      } else {
        return {
          'success': false,
          'error': data['error'] ?? 'Registration failed',
        };
      }
    } catch (e) {
      print('❌ Registration error: $e');
      return {'success': false, 'error': 'Network error: $e'};
    }
  }

  static Future<Map<String, dynamic>> login({
    required String email,
    required String password,
  }) async {
    try {
      print('🔄 Attempting login to: $baseUrl/auth/login');

      final response = await http.post(
        Uri.parse('$baseUrl/auth/login'),
        headers: defaultHeaders,
        body: jsonEncode({'email': email, 'password': password}),
      );

      print('📡 Login response status: ${response.statusCode}');
      print('📡 Login response body: ${response.body}');

      final data = jsonDecode(response.body);

      if (response.statusCode == 200) {
        if (data['token'] != null) {
          await saveToken(data['token']);
        }
        return {'success': true, 'data': data};
      } else {
        return {'success': false, 'error': data['error'] ?? 'Login failed'};
      }
    } catch (e) {
      print('❌ Login error: $e');
      return {'success': false, 'error': 'Network error: $e'};
    }
  }

  static Future<Map<String, dynamic>> getCurrentUser() async {
    try {
      final headers = await authHeaders;

      final response = await http.get(
        Uri.parse('$baseUrl/auth/me'),
        headers: headers,
      );

      final data = jsonDecode(response.body);

      if (response.statusCode == 200) {
        return {'success': true, 'data': data};
      } else {
        return {
          'success': false,
          'error': data['error'] ?? 'Failed to get user',
        };
      }
    } catch (e) {
      print('❌ Get current user error: $e');
      return {'success': false, 'error': 'Network error: $e'};
    }
  }

  // Restaurant Profile
  static Future<Map<String, dynamic>> completeProfile({
    required String restaurantName,
    required String description,
    required String phone,
    required String address,
    required double latitude,
    required double longitude,
    required String cuisineType,
  }) async {
    try {
      print('🔄 Completing restaurant profile...');

      final headers = await authHeaders;

      final response = await http.post(
        Uri.parse('$baseUrl/restaurant/profile'),
        headers: headers,
        body: jsonEncode({
          'restaurantName': restaurantName,
          'description': description,
          'phone': phone,
          'address': address,
          'latitude': latitude,
          'longitude': longitude,
          'cuisineType': cuisineType,
        }),
      );

      print('📡 Profile completion response status: ${response.statusCode}');
      print('📡 Profile completion response body: ${response.body}');

      final data = jsonDecode(response.body);

      if (response.statusCode == 200) {
        return {'success': true, 'data': data};
      } else {
        return {
          'success': false,
          'error': data['error'] ?? 'Profile completion failed',
        };
      }
    } catch (e) {
      print('❌ Profile completion error: $e');
      return {'success': false, 'error': 'Network error: $e'};
    }
  }

  static Future<Map<String, dynamic>> getProfile() async {
    try {
      final headers = await authHeaders;

      final response = await http.get(
        Uri.parse('$baseUrl/restaurant/profile'),
        headers: headers,
      );

      final data = jsonDecode(response.body);

      if (response.statusCode == 200) {
        return {'success': true, 'data': data};
      } else {
        return {
          'success': false,
          'error': data['error'] ?? 'Failed to get profile',
        };
      }
    } catch (e) {
      print('❌ Get profile error: $e');
      return {'success': false, 'error': 'Network error: $e'};
    }
  }

  static Future<Map<String, dynamic>> toggleRestaurantStatus({
    required bool isOpen,
  }) async {
    try {
      final headers = await authHeaders;

      final response = await http.patch(
        Uri.parse('$baseUrl/restaurant/status'),
        headers: headers,
        body: jsonEncode({'isOpen': isOpen}),
      );

      final data = jsonDecode(response.body);

      if (response.statusCode == 200) {
        return {'success': true, 'data': data};
      } else {
        return {
          'success': false,
          'error': data['error'] ?? 'Failed to update status',
        };
      }
    } catch (e) {
      print('❌ Toggle status error: $e');
      return {'success': false, 'error': 'Network error: $e'};
    }
  }

  // Orders
  static Future<Map<String, dynamic>> getOrders({String? status}) async {
    try {
      final headers = await authHeaders;

      String url = '$baseUrl/restaurant/orders';
      if (status != null) {
        url += '?status=$status';
      }

      final response = await http.get(Uri.parse(url), headers: headers);

      final data = jsonDecode(response.body);

      if (response.statusCode == 200) {
        return {'success': true, 'data': data};
      } else {
        return {
          'success': false,
          'error': data['error'] ?? 'Failed to get orders',
        };
      }
    } catch (e) {
      print('❌ Get orders error: $e');
      return {'success': false, 'error': 'Network error: $e'};
    }
  }

  static Future<Map<String, dynamic>> getOrderDetails(int orderId) async {
    try {
      final headers = await authHeaders;

      final response = await http.get(
        Uri.parse('$baseUrl/restaurant/orders/$orderId'),
        headers: headers,
      );

      final data = jsonDecode(response.body);

      if (response.statusCode == 200) {
        return {'success': true, 'data': data};
      } else {
        return {
          'success': false,
          'error': data['error'] ?? 'Failed to get order details',
        };
      }
    } catch (e) {
      print('❌ Get order details error: $e');
      return {'success': false, 'error': 'Network error: $e'};
    }
  }

  static Future<Map<String, dynamic>> cancelOrder({
    required int orderId,
    String? reason,
  }) async {
    try {
      final headers = await authHeaders;

      final response = await http.patch(
        Uri.parse('$baseUrl/restaurant/orders/$orderId/cancel'),
        headers: headers,
        body: jsonEncode({'reason': reason}),
      );

      final data = jsonDecode(response.body);

      if (response.statusCode == 200) {
        return {'success': true, 'data': data};
      } else {
        return {
          'success': false,
          'error': data['error'] ?? 'Failed to cancel order',
        };
      }
    } catch (e) {
      print('❌ Cancel order error: $e');
      return {'success': false, 'error': 'Network error: $e'};
    }
  }

  static Future<Map<String, dynamic>> updateOrderStatus({
    required int orderId,
    required String status,
  }) async {
    try {
      final headers = await authHeaders;

      final response = await http.patch(
        Uri.parse('$baseUrl/restaurant/orders/$orderId/status'),
        headers: headers,
        body: jsonEncode({'status': status}),
      );

      final data = jsonDecode(response.body);

      if (response.statusCode == 200) {
        return {'success': true, 'data': data};
      } else {
        return {
          'success': false,
          'error': data['error'] ?? 'Failed to update order status',
        };
      }
    } catch (e) {
      print('❌ Update order status error: $e');
      return {'success': false, 'error': 'Network error: $e'};
    }
  }

  static Future<void> logout() async {
    await removeToken();
  }
}
