import 'package:flutter/foundation.dart';
import '../models/order.dart';
import '../services/api_service.dart';

class OrdersProvider with ChangeNotifier {
  List<Order> _availableOrders = [];
  List<Order> _myOrders = [];
  Order? _currentOrder;
  bool _isLoading = false;
  String? _error;
  Map<String, dynamic>? _stats;

  List<Order> get availableOrders => _availableOrders;
  List<Order> get myOrders => _myOrders;
  Order? get currentOrder => _currentOrder;
  bool get isLoading => _isLoading;
  String? get error => _error;
  Map<String, dynamic>? get stats => _stats;

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String? error) {
    _error = error;
    notifyListeners();
  }

  Future<void> loadAvailableOrders() async {
    _setLoading(true);
    _setError(null);

    try {
      print('🔄 OrdersProvider: Loading available orders...');
      _availableOrders = await ApiService.getAvailableOrders();
      print(
        '✅ OrdersProvider: Loaded ${_availableOrders.length} available orders',
      );
      _setLoading(false);
      notifyListeners();
    } catch (e) {
      print('❌ OrdersProvider: Error loading available orders: $e');
      _setError(e.toString().replaceFirst('Exception: ', ''));
      _setLoading(false);
    }
  }

  Future<void> loadMyOrders() async {
    _setLoading(true);
    _setError(null);

    try {
      _myOrders = await ApiService.getMyOrders();

      // Set current order (the most recent picked up order)
      _currentOrder =
          _myOrders.isNotEmpty && _myOrders.first.status == 'picked_up'
          ? _myOrders.first
          : null;

      _setLoading(false);
      notifyListeners();
    } catch (e) {
      _setError(e.toString().replaceFirst('Exception: ', ''));
      _setLoading(false);
    }
  }

  Future<bool> declineOrder(int orderId) async {
    _setLoading(true);
    _setError(null);

    try {
      await ApiService.declineOrder(orderId);

      // Remove from available orders
      _availableOrders.removeWhere((order) => order.id == orderId);

      _setLoading(false);
      notifyListeners();
      return true;
    } catch (e) {
      _setError(e.toString().replaceFirst('Exception: ', ''));
      _setLoading(false);
      return false;
    }
  }

  Future<bool> acceptOrder(int orderId) async {
    _setLoading(true);
    _setError(null);

    try {
      final acceptedOrder = await ApiService.acceptOrder(orderId);

      // Remove from available orders
      _availableOrders.removeWhere((order) => order.id == orderId);

      // Add to my orders
      _myOrders.insert(0, acceptedOrder);

      // Set as current order
      _currentOrder = acceptedOrder;

      _setLoading(false);
      notifyListeners();
      return true;
    } catch (e) {
      _setError(e.toString().replaceFirst('Exception: ', ''));
      _setLoading(false);
      return false;
    }
  }

  Future<bool> updateOrderStatus(int orderId, String status) async {
    _setLoading(true);
    _setError(null);

    try {
      final updatedOrder = await ApiService.updateOrderStatus(orderId, status);

      // Update the order in my orders list
      final index = _myOrders.indexWhere((order) => order.id == orderId);
      if (index != -1) {
        _myOrders[index] = updatedOrder;
      }

      // Update current order if it matches
      if (_currentOrder?.id == orderId) {
        _currentOrder = updatedOrder;
      }

      _setLoading(false);
      notifyListeners();
      return true;
    } catch (e) {
      _setError(e.toString().replaceFirst('Exception: ', ''));
      _setLoading(false);
      return false;
    }
  }

  Future<bool> updateOrderStatusWithLocation(
    int orderId,
    String status,
    double latitude,
    double longitude,
  ) async {
    _setLoading(true);
    _setError(null);

    try {
      final updatedOrder = await ApiService.updateOrderStatusWithLocation(
        orderId,
        status,
        latitude,
        longitude,
      );

      // Update the order in my orders list
      final index = _myOrders.indexWhere((order) => order.id == orderId);
      if (index != -1) {
        _myOrders[index] = updatedOrder;
      }

      // Update current order if it matches
      if (_currentOrder?.id == orderId) {
        _currentOrder = updatedOrder;
      }

      _setLoading(false);
      notifyListeners();
      return true;
    } catch (e) {
      _setError(e.toString().replaceFirst('Exception: ', ''));
      _setLoading(false);
      return false;
    }
  }

  Future<bool> completeOrder(int orderId) async {
    _setLoading(true);
    _setError(null);

    try {
      final completedOrder = await ApiService.completeOrder(orderId);

      // Update the order in my orders list
      final index = _myOrders.indexWhere((order) => order.id == orderId);
      if (index != -1) {
        _myOrders[index] = completedOrder;
      }

      // Clear current order if it was the completed one
      if (_currentOrder?.id == orderId) {
        _currentOrder = null;
      }

      _setLoading(false);
      notifyListeners();
      return true;
    } catch (e) {
      _setError(e.toString().replaceFirst('Exception: ', ''));
      _setLoading(false);
      return false;
    }
  }

  Future<void> loadStats() async {
    try {
      _stats = await ApiService.getDriverStats();
      notifyListeners();
    } catch (e) {
      print('Failed to load stats: $e');
    }
  }

  Future<void> refreshAll() async {
    await Future.wait([loadAvailableOrders(), loadMyOrders(), loadStats()]);
  }

  void clearError() {
    _setError(null);
  }

  void clearCurrentOrder() {
    _currentOrder = null;
    notifyListeners();
  }
}
