# Google Maps Setup Guide

This guide explains how to set up Google Maps API for the BRSIMA Customer App.

## Prerequisites

1. A Google Cloud Platform (GCP) account
2. A project in Google Cloud Console
3. Billing enabled on your GCP project

## Step 1: Enable Google Maps APIs

1. Go to the [Google Cloud Console](https://console.cloud.google.com/)
2. Select your project or create a new one
3. Navigate to "APIs & Services" > "Library"
4. Enable the following APIs:
   - **Maps SDK for Android**
   - **Maps SDK for iOS** (if you plan to build for iOS)
   - **Geocoding API**
   - **Places API** (optional, for enhanced location search)

## Step 2: Create API Key

1. Go to "APIs & Services" > "Credentials"
2. Click "Create Credentials" > "API Key"
3. Copy the generated API key
4. Click on the API key to configure restrictions (recommended)

## Step 3: Configure API Key Restrictions (Recommended)

### Application Restrictions
- Select "Android apps"
- Add your package name: `com.example.customer` (or your actual package name)
- Add your SHA-1 certificate fingerprint

### API Restrictions
- Select "Restrict key"
- Choose the APIs you enabled in Step 1

## Step 4: Configure the Flutter App

### Android Configuration

1. Open `customer/android/app/src/main/AndroidManifest.xml`
2. Replace `YOUR_GOOGLE_MAPS_API_KEY_HERE` with your actual API key:

```xml
<meta-data
    android:name="com.google.android.geo.API_KEY"
    android:value="YOUR_ACTUAL_API_KEY_HERE" />
```

### iOS Configuration (if building for iOS)

1. Open `customer/ios/Runner/AppDelegate.swift`
2. Add the following import at the top:
```swift
import GoogleMaps
```

3. Add this line in the `application` method:
```swift
GMSServices.provideAPIKey("YOUR_ACTUAL_API_KEY_HERE")
```

## Step 5: Get SHA-1 Certificate Fingerprint

### For Debug Certificate
```bash
cd customer/android
./gradlew signingReport
```

### For Release Certificate
```bash
keytool -list -v -keystore your-release-key.keystore -alias your-key-alias
```

## Step 6: Test the Integration

1. Run the app: `flutter run`
2. Navigate to the profile setup screen
3. The map should load and show your current location
4. Test tapping on the map to select different locations

## Troubleshooting

### Map Not Loading
- Check if the API key is correctly set
- Verify that Maps SDK for Android is enabled
- Check the SHA-1 fingerprint in API key restrictions

### Location Not Working
- Ensure location permissions are granted
- Check if Geocoding API is enabled
- Verify internet connection

### Build Errors
- Run `flutter clean` and `flutter pub get`
- Check if all dependencies are properly installed

## Security Best Practices

1. **Never commit API keys to version control**
2. Use environment variables or secure storage for API keys
3. Restrict API keys to specific apps and APIs
4. Monitor API usage in Google Cloud Console
5. Rotate API keys regularly

## Environment Variables (Recommended)

Instead of hardcoding the API key, you can use environment variables:

1. Create a `.env` file in the project root (add to `.gitignore`)
2. Add: `GOOGLE_MAPS_API_KEY=your_api_key_here`
3. Use a package like `flutter_dotenv` to load the key

## Cost Considerations

- Google Maps APIs have usage limits and pricing
- Monitor your usage in Google Cloud Console
- Set up billing alerts to avoid unexpected charges
- Consider implementing caching for geocoding results

## Support

For issues related to Google Maps APIs, refer to:
- [Google Maps Platform Documentation](https://developers.google.com/maps/documentation)
- [Flutter Google Maps Plugin Documentation](https://pub.dev/packages/google_maps_flutter)
