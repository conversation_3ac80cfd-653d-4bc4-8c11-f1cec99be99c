// Helper function to parse double values from various types
double _parseDouble(dynamic value) {
  if (value == null) return 0.0;
  if (value is double) return value;
  if (value is int) return value.toDouble();
  if (value is String) {
    return double.tryParse(value) ?? 0.0;
  }
  return 0.0;
}

class OrderItem {
  final String name;
  final String description;
  final String? imageUrl;
  final int quantity;
  final double unitPrice;
  final double totalPrice;
  final String? specialInstructions;

  OrderItem({
    required this.name,
    required this.description,
    this.imageUrl,
    required this.quantity,
    required this.unitPrice,
    required this.totalPrice,
    this.specialInstructions,
  });

  factory OrderItem.fromJson(Map<String, dynamic> json) {
    return OrderItem(
      name: json['name'] ?? json['menu_item_name'] ?? '',
      description: json['description'] ?? '',
      imageUrl: json['imageUrl'] ?? json['image_url'],
      quantity: json['quantity'] ?? 0,
      unitPrice: _parseDouble(json['unitPrice'] ?? json['unit_price']),
      totalPrice: _parseDouble(json['totalPrice'] ?? json['total_price']),
      specialInstructions:
          json['specialInstructions'] ?? json['special_instructions'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'description': description,
      'imageUrl': imageUrl,
      'quantity': quantity,
      'unitPrice': unitPrice,
      'totalPrice': totalPrice,
      'specialInstructions': specialInstructions,
    };
  }
}

class RestaurantInfo {
  final String name;
  final String address;
  final String? phone;
  final double? latitude;
  final double? longitude;

  RestaurantInfo({
    required this.name,
    required this.address,
    this.phone,
    this.latitude,
    this.longitude,
  });

  factory RestaurantInfo.fromJson(Map<String, dynamic> json) {
    return RestaurantInfo(
      name: json['name'],
      address: json['address'],
      phone: json['phone'],
      latitude: json['location']?['latitude']?.toDouble(),
      longitude: json['location']?['longitude']?.toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'address': address,
      'phone': phone,
      'location': latitude != null && longitude != null
          ? {'latitude': latitude, 'longitude': longitude}
          : null,
    };
  }
}

class CustomerInfo {
  final String name;
  final String phone;

  CustomerInfo({required this.name, required this.phone});

  factory CustomerInfo.fromJson(Map<String, dynamic> json) {
    return CustomerInfo(name: json['name'], phone: json['phone']);
  }

  Map<String, dynamic> toJson() {
    return {'name': name, 'phone': phone};
  }
}

class Order {
  final int id;
  final String orderNumber;
  final double totalAmount;
  final double deliveryFee;
  final String status;
  final String deliveryAddress;
  final double? deliveryLatitude;
  final double? deliveryLongitude;
  final String? specialInstructions;
  final DateTime? estimatedDeliveryTime;
  final DateTime createdAt;
  final String restaurantName;
  final String restaurantAddress;
  final String? restaurantPhone;
  final double? restaurantLatitude;
  final double? restaurantLongitude;
  final String customerName;
  final String customerPhone;
  final List<OrderItem> items;

  Order({
    required this.id,
    required this.orderNumber,
    required this.totalAmount,
    required this.deliveryFee,
    required this.status,
    required this.deliveryAddress,
    this.deliveryLatitude,
    this.deliveryLongitude,
    this.specialInstructions,
    this.estimatedDeliveryTime,
    required this.createdAt,
    required this.restaurantName,
    required this.restaurantAddress,
    this.restaurantPhone,
    this.restaurantLatitude,
    this.restaurantLongitude,
    required this.customerName,
    required this.customerPhone,
    this.items = const [],
  });

  factory Order.fromJson(Map<String, dynamic> json) {
    // Handle both snake_case and camelCase formats
    return Order(
      id: json['id'] ?? 0,
      orderNumber: json['order_number'] ?? json['orderNumber'] ?? '',
      totalAmount: _parseDouble(json['total_amount'] ?? json['totalAmount']),
      deliveryFee: _parseDouble(json['delivery_fee'] ?? json['deliveryFee']),
      status: json['status'] ?? 'pending',
      deliveryAddress:
          json['delivery_address'] ?? json['deliveryAddress'] ?? '',
      deliveryLatitude: _parseDouble(
        json['delivery_latitude'] ?? json['deliveryLatitude'],
      ),
      deliveryLongitude: _parseDouble(
        json['delivery_longitude'] ?? json['deliveryLongitude'],
      ),
      specialInstructions:
          json['special_instructions'] ?? json['specialInstructions'],
      estimatedDeliveryTime:
          (json['estimated_delivery_time'] ?? json['estimatedDeliveryTime']) !=
              null
          ? DateTime.tryParse(
              json['estimated_delivery_time'] ?? json['estimatedDeliveryTime'],
            )
          : null,
      createdAt:
          DateTime.tryParse(json['created_at'] ?? json['createdAt'] ?? '') ??
          DateTime.now(),
      restaurantName:
          json['restaurant_name'] ?? json['restaurant']?['name'] ?? '',
      restaurantAddress:
          json['restaurant_address'] ?? json['restaurant']?['address'] ?? '',
      restaurantPhone: json['restaurant_phone'] ?? json['restaurant']?['phone'],
      restaurantLatitude: _parseDouble(
        json['restaurant_latitude'] ??
            json['restaurant']?['location']?['latitude'],
      ),
      restaurantLongitude: _parseDouble(
        json['restaurant_longitude'] ??
            json['restaurant']?['location']?['longitude'],
      ),
      customerName: json['customer_name'] ?? json['customer']?['name'] ?? '',
      customerPhone: json['customer_phone'] ?? json['customer']?['phone'] ?? '',
      items: json['items'] != null
          ? (json['items'] as List)
                .map((item) => OrderItem.fromJson(item))
                .toList()
          : [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'order_number': orderNumber,
      'total_amount': totalAmount,
      'delivery_fee': deliveryFee,
      'status': status,
      'delivery_address': deliveryAddress,
      'delivery_latitude': deliveryLatitude,
      'delivery_longitude': deliveryLongitude,
      'special_instructions': specialInstructions,
      'estimated_delivery_time': estimatedDeliveryTime?.toIso8601String(),
      'created_at': createdAt.toIso8601String(),
      'restaurant_name': restaurantName,
      'restaurant_address': restaurantAddress,
      'restaurant_phone': restaurantPhone,
      'restaurant_latitude': restaurantLatitude,
      'restaurant_longitude': restaurantLongitude,
      'customer_name': customerName,
      'customer_phone': customerPhone,
      'items': items.map((item) => item.toJson()).toList(),
    };
  }

  String get statusDisplayName {
    switch (status) {
      case 'pending':
        return 'Pending';
      case 'confirmed':
        return 'Confirmed';
      case 'preparing':
        return 'Preparing';
      case 'ready':
        return 'Ready for Pickup';
      case 'assigned':
        return 'Assigned to You';
      case 'picked_up':
        return 'Picked Up';
      case 'delivered':
        return 'Delivered';
      case 'cancelled':
        return 'Cancelled';
      default:
        return status;
    }
  }

  String get formattedTotal {
    return '\$${totalAmount.toStringAsFixed(2)}';
  }

  String get formattedDeliveryFee {
    return '\$${deliveryFee.toStringAsFixed(2)}';
  }
}
