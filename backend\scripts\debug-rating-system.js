const { pool } = require('../config/database');

async function debugRatingSystem() {
  const client = await pool.connect();
  try {
    console.log('🔍 Debugging Rating System...\n');
    
    // Check if rating tables exist
    console.log('1. Checking rating tables...');
    const tables = await client.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      AND table_name IN ('food_ratings', 'driver_ratings')
      ORDER BY table_name
    `);
    
    console.log('Rating tables found:', tables.rows.map(r => r.table_name));
    
    // Check customers
    console.log('\n2. Checking customers...');
    const customers = await client.query(`
      SELECT cp.id, cp.full_name, u.email, u.user_type, u.is_active
      FROM customer_profiles cp
      JOIN users u ON cp.user_id = u.id
      LIMIT 5
    `);
    
    console.log(`Found ${customers.rows.length} customers:`);
    customers.rows.forEach(c => {
      console.log(`  - ID: ${c.id}, Name: ${c.full_name}, Email: ${c.email}, Active: ${c.is_active}`);
    });
    
    // Check completed orders
    console.log('\n3. Checking completed orders...');
    const orders = await client.query(`
      SELECT o.id, o.customer_id, o.driver_id, o.status, o.total_amount,
             cp.full_name as customer_name,
             dp.full_name as driver_name
      FROM orders o
      JOIN customer_profiles cp ON o.customer_id = cp.id
      LEFT JOIN driver_profiles dp ON o.driver_id = dp.id
      WHERE o.status = 'delivered'
      ORDER BY o.created_at DESC
      LIMIT 5
    `);
    
    console.log(`Found ${orders.rows.length} completed orders:`);
    orders.rows.forEach(o => {
      console.log(`  - Order ID: ${o.id}, Customer: ${o.customer_name}, Driver: ${o.driver_name}, Amount: $${o.total_amount}`);
    });
    
    // Check order items for rating
    if (orders.rows.length > 0) {
      const orderId = orders.rows[0].id;
      console.log(`\n4. Checking order items for Order ID ${orderId}...`);
      
      const orderItems = await client.query(`
        SELECT oi.id, oi.menu_item_id, oi.quantity, oi.unit_price,
               mi.name as item_name, mi.restaurant_id
        FROM order_items oi
        JOIN menu_items mi ON oi.menu_item_id = mi.id
        WHERE oi.order_id = $1
      `, [orderId]);
      
      console.log(`Found ${orderItems.rows.length} items in this order:`);
      orderItems.rows.forEach(item => {
        console.log(`  - Item: ${item.item_name}, Qty: ${item.quantity}, Price: $${item.unit_price}`);
      });
    }
    
    // Check existing ratings
    console.log('\n5. Checking existing ratings...');
    const foodRatings = await client.query('SELECT COUNT(*) as count FROM food_ratings');
    const driverRatings = await client.query('SELECT COUNT(*) as count FROM driver_ratings');
    
    console.log(`Food ratings: ${foodRatings.rows[0].count}`);
    console.log(`Driver ratings: ${driverRatings.rows[0].count}`);
    
    // Test data for rating submission
    if (customers.rows.length > 0 && orders.rows.length > 0) {
      console.log('\n6. Sample data for testing:');
      const testCustomer = customers.rows[0];
      const testOrder = orders.rows[0];
      
      console.log('Test Customer:', {
        id: testCustomer.id,
        name: testCustomer.full_name,
        email: testCustomer.email
      });
      
      console.log('Test Order:', {
        id: testOrder.id,
        customerId: testOrder.customer_id,
        driverId: testOrder.driver_id,
        status: testOrder.status
      });
      
      // Get menu items from this order
      const testItems = await client.query(`
        SELECT oi.menu_item_id, mi.name
        FROM order_items oi
        JOIN menu_items mi ON oi.menu_item_id = mi.id
        WHERE oi.order_id = $1
        LIMIT 1
      `, [testOrder.id]);
      
      if (testItems.rows.length > 0) {
        console.log('Test Menu Item:', {
          id: testItems.rows[0].menu_item_id,
          name: testItems.rows[0].name
        });
        
        console.log('\n📝 Sample rating request data:');
        console.log('Food Rating:', JSON.stringify({
          menuItemId: testItems.rows[0].menu_item_id,
          orderId: testOrder.id,
          rating: 5,
          reviewText: "Great food!"
        }, null, 2));
        
        if (testOrder.driver_id) {
          console.log('Driver Rating:', JSON.stringify({
            driverId: testOrder.driver_id,
            orderId: testOrder.id,
            rating: 4,
            reviewText: "Good service!"
          }, null, 2));
        }
      }
    }
    
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    client.release();
    process.exit(0);
  }
}

debugRatingSystem();
