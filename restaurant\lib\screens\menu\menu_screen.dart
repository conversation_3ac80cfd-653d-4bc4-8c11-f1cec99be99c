import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/auth_provider.dart';
import '../../shared/app_colors.dart';
import '../../shared/app_dimensions.dart';

class MenuScreen extends StatefulWidget {
  const MenuScreen({super.key});

  @override
  State<MenuScreen> createState() => _MenuScreenState();
}

class _MenuScreenState extends State<MenuScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: const Text(
          'Menu Management',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        elevation: 0,
        automaticallyImplyLeading: false,
        actions: [
          IconButton(
            onPressed: () {
              _showAddItemDialog();
            },
            icon: const Icon(Icons.add),
            tooltip: 'Add Menu Item',
          ),
        ],
      ),
      body: Consumer<AuthProvider>(
        builder: (context, authProvider, child) {
          return SingleChildScrollView(
            padding: const EdgeInsets.all(AppDimensions.paddingM),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Menu Stats
                Row(
                  children: [
                    Expanded(
                      child: _buildStatCard(
                        'Total Items',
                        '24',
                        Icons.restaurant_menu,
                        AppColors.primary,
                      ),
                    ),
                    const SizedBox(width: AppDimensions.spaceM),
                    Expanded(
                      child: _buildStatCard(
                        'Categories',
                        '6',
                        Icons.category,
                        AppColors.secondary,
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: AppDimensions.spaceL),

                // Categories Section
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const Text(
                      'Categories',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    TextButton.icon(
                      onPressed: () {
                        _showAddCategoryDialog();
                      },
                      icon: const Icon(Icons.add),
                      label: const Text('Add Category'),
                    ),
                  ],
                ),

                const SizedBox(height: AppDimensions.spaceM),

                // Categories List
                _buildCategorySection('Appetizers', [
                  _buildMenuItem('Spring Rolls', '\$8.99', 'Available'),
                  _buildMenuItem('Chicken Wings', '\$12.99', 'Available'),
                ]),

                _buildCategorySection('Main Courses', [
                  _buildMenuItem('Grilled Chicken', '\$18.99', 'Available'),
                  _buildMenuItem('Beef Steak', '\$24.99', 'Out of Stock'),
                  _buildMenuItem('Fish & Chips', '\$16.99', 'Available'),
                ]),

                _buildCategorySection('Desserts', [
                  _buildMenuItem('Chocolate Cake', '\$6.99', 'Available'),
                  _buildMenuItem('Ice Cream', '\$4.99', 'Available'),
                ]),

                const SizedBox(height: AppDimensions.spaceXL),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(AppDimensions.paddingL),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadow,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.all(AppDimensions.paddingS),
            decoration: BoxDecoration(
              color: color.withOpacity(0.1),
              borderRadius: BorderRadius.circular(AppDimensions.radiusS),
            ),
            child: Icon(
              icon,
              color: color,
              size: AppDimensions.iconM,
            ),
          ),
          const SizedBox(height: AppDimensions.spaceM),
          Text(
            value,
            style: const TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: AppColors.textPrimary,
            ),
          ),
          const SizedBox(height: AppDimensions.spaceXS),
          Text(
            title,
            style: const TextStyle(
              fontSize: 14,
              color: AppColors.textSecondary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCategorySection(String categoryName, List<Widget> items) {
    return Container(
      margin: const EdgeInsets.only(bottom: AppDimensions.marginL),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadow,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(AppDimensions.paddingM),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  categoryName,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: AppColors.textPrimary,
                  ),
                ),
                PopupMenuButton<String>(
                  onSelected: (value) {
                    if (value == 'edit') {
                      _showEditCategoryDialog(categoryName);
                    } else if (value == 'delete') {
                      _showDeleteCategoryDialog(categoryName);
                    }
                  },
                  itemBuilder: (context) => [
                    const PopupMenuItem(
                      value: 'edit',
                      child: Row(
                        children: [
                          Icon(Icons.edit, size: 20),
                          SizedBox(width: 8),
                          Text('Edit'),
                        ],
                      ),
                    ),
                    const PopupMenuItem(
                      value: 'delete',
                      child: Row(
                        children: [
                          Icon(Icons.delete, size: 20, color: AppColors.error),
                          SizedBox(width: 8),
                          Text('Delete', style: TextStyle(color: AppColors.error)),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          const Divider(height: 1),
          ...items,
        ],
      ),
    );
  }

  Widget _buildMenuItem(String name, String price, String status) {
    final isAvailable = status == 'Available';
    
    return Container(
      padding: const EdgeInsets.all(AppDimensions.paddingM),
      child: Row(
        children: [
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              color: AppColors.primary.withOpacity(0.1),
              borderRadius: BorderRadius.circular(AppDimensions.radiusS),
            ),
            child: const Icon(
              Icons.fastfood,
              color: AppColors.primary,
              size: 30,
            ),
          ),
          const SizedBox(width: AppDimensions.spaceM),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  name,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: AppDimensions.spaceXS),
                Text(
                  price,
                  style: const TextStyle(
                    fontSize: 14,
                    color: AppColors.textSecondary,
                  ),
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(
              horizontal: AppDimensions.paddingS,
              vertical: AppDimensions.paddingXS,
            ),
            decoration: BoxDecoration(
              color: isAvailable 
                  ? AppColors.success.withOpacity(0.1)
                  : AppColors.error.withOpacity(0.1),
              borderRadius: BorderRadius.circular(AppDimensions.radiusS),
            ),
            child: Text(
              status,
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w500,
                color: isAvailable ? AppColors.success : AppColors.error,
              ),
            ),
          ),
          const SizedBox(width: AppDimensions.spaceS),
          PopupMenuButton<String>(
            onSelected: (value) {
              if (value == 'edit') {
                _showEditItemDialog(name);
              } else if (value == 'delete') {
                _showDeleteItemDialog(name);
              } else if (value == 'toggle') {
                _toggleItemAvailability(name);
              }
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'edit',
                child: Row(
                  children: [
                    Icon(Icons.edit, size: 20),
                    SizedBox(width: 8),
                    Text('Edit'),
                  ],
                ),
              ),
              PopupMenuItem(
                value: 'toggle',
                child: Row(
                  children: [
                    Icon(
                      isAvailable ? Icons.visibility_off : Icons.visibility,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Text(isAvailable ? 'Mark Unavailable' : 'Mark Available'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'delete',
                child: Row(
                  children: [
                    Icon(Icons.delete, size: 20, color: AppColors.error),
                    SizedBox(width: 8),
                    Text('Delete', style: TextStyle(color: AppColors.error)),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  void _showAddItemDialog() {
    // TODO: Implement add item dialog
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Add item functionality coming soon')),
    );
  }

  void _showAddCategoryDialog() {
    // TODO: Implement add category dialog
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Add category functionality coming soon')),
    );
  }

  void _showEditCategoryDialog(String categoryName) {
    // TODO: Implement edit category dialog
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Edit $categoryName functionality coming soon')),
    );
  }

  void _showDeleteCategoryDialog(String categoryName) {
    // TODO: Implement delete category dialog
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Delete $categoryName functionality coming soon')),
    );
  }

  void _showEditItemDialog(String itemName) {
    // TODO: Implement edit item dialog
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Edit $itemName functionality coming soon')),
    );
  }

  void _showDeleteItemDialog(String itemName) {
    // TODO: Implement delete item dialog
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Delete $itemName functionality coming soon')),
    );
  }

  void _toggleItemAvailability(String itemName) {
    // TODO: Implement toggle availability
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Toggle $itemName availability functionality coming soon')),
    );
  }
}
