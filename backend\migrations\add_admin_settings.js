const { pool } = require('../config/database');

const migrations = [
  // Create admin settings table
  `
    CREATE TABLE IF NOT EXISTS admin_settings (
      id SERIAL PRIMARY KEY,
      setting_key VARCHAR(100) UNIQUE NOT NULL,
      setting_value TEXT NOT NULL,
      description TEXT,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );
  `,
  
  // Insert default settings
  `
    INSERT INTO admin_settings (setting_key, setting_value, description) 
    VALUES 
      ('restaurant_search_radius', '10', 'Default radius in kilometers for restaurant search'),
      ('max_delivery_distance', '15', 'Maximum delivery distance in kilometers'),
      ('app_name', 'BRSIMA', 'Application name'),
      ('support_email', '<EMAIL>', 'Support email address')
    ON CONFLICT (setting_key) DO NOTHING;
  `,
];

async function runMigrations() {
  const client = await pool.connect();
  
  try {
    console.log('🌟 Starting admin settings migrations...');
    
    for (let i = 0; i < migrations.length; i++) {
      console.log(`📝 Running admin settings migration ${i + 1}/${migrations.length}...`);
      await client.query(migrations[i]);
    }
    
    console.log('✅ Admin settings migrations completed successfully!');
    console.log('🎉 Admin settings setup complete!');
    
  } catch (error) {
    console.error('❌ Migration error:', error);
    throw error;
  } finally {
    client.release();
  }
}

// Run migrations if this file is executed directly
if (require.main === module) {
  runMigrations()
    .then(() => process.exit(0))
    .catch((error) => {
      console.error('Migration failed:', error);
      process.exit(1);
    });
}

module.exports = { runMigrations };
