import 'package:flutter/material.dart';
import '../models/restaurant.dart';
import '../services/api_service.dart';

class RestaurantProvider with ChangeNotifier {
  List<Restaurant> _restaurants = [];
  List<Restaurant> _searchResults = [];
  RestaurantDetail? _selectedRestaurant;
  bool _isLoading = false;
  String? _error;
  String _searchQuery = '';
  bool _hasLoadedRestaurants = false;

  List<Restaurant> get restaurants => _restaurants;
  List<Restaurant> get searchResults => _searchResults;
  RestaurantDetail? get selectedRestaurant => _selectedRestaurant;
  bool get isLoading => _isLoading;
  String? get error => _error;
  String get searchQuery => _searchQuery;
  bool get hasLoadedRestaurants => _hasLoadedRestaurants;

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String? error) {
    _error = error;
    notifyListeners();
  }

  Future<void> loadRestaurants({
    double? latitude,
    double? longitude,
    double radius = 10,
    bool isGuest = false,
    bool forceReload = false,
  }) async {
    // Skip loading if already loaded and not forcing reload
    if (_hasLoadedRestaurants && !forceReload && _error == null) {
      return;
    }
    _setLoading(true);
    _setError(null);

    try {
      _restaurants = await ApiService.getRestaurants(
        latitude: latitude,
        longitude: longitude,
        radius: radius,
        isGuest: isGuest,
      );
      _hasLoadedRestaurants = true;
      notifyListeners();
    } catch (e) {
      _setError(e.toString());
    } finally {
      _setLoading(false);
    }
  }

  Future<void> loadRestaurantDetails(
    int restaurantId, {
    bool isGuest = false,
  }) async {
    _setLoading(true);
    _setError(null);

    try {
      _selectedRestaurant = await ApiService.getRestaurantDetails(
        restaurantId,
        isGuest: isGuest,
      );
      notifyListeners();
    } catch (e) {
      _setError(e.toString());
    } finally {
      _setLoading(false);
    }
  }

  Future<void> searchRestaurants({
    required String query,
    double? latitude,
    double? longitude,
    String? cuisine,
    bool isGuest = false,
  }) async {
    if (query.trim().isEmpty) {
      _searchResults = [];
      _searchQuery = '';
      notifyListeners();
      return;
    }

    _setLoading(true);
    _setError(null);
    _searchQuery = query;

    try {
      _searchResults = await ApiService.searchRestaurants(
        query: query,
        latitude: latitude,
        longitude: longitude,
        cuisine: cuisine,
        isGuest: isGuest,
      );
      notifyListeners();
    } catch (e) {
      _setError(e.toString());
    } finally {
      _setLoading(false);
    }
  }

  void clearSearch() {
    _searchResults = [];
    _searchQuery = '';
    _setError(null);
    notifyListeners();
  }

  void clearSelectedRestaurant() {
    _selectedRestaurant = null;
    notifyListeners();
  }

  void clearError() {
    _setError(null);
  }
}
