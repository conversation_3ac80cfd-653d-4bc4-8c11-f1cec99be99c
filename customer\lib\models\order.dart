import 'rating.dart';

class Order {
  final int id;
  final String orderNumber;
  final String status;
  final double totalAmount;
  final double deliveryFee;
  final String deliveryAddress;
  final double? deliveryLatitude;
  final double? deliveryLongitude;
  final String? specialInstructions;
  final DateTime? estimatedDeliveryTime;
  final DateTime createdAt;
  final DateTime updatedAt;
  final OrderRestaurant restaurant;
  final OrderDriver? driver;
  final List<OrderItem> items;
  final OrderRatingStatus? ratings;

  Order({
    required this.id,
    required this.orderNumber,
    required this.status,
    required this.totalAmount,
    required this.deliveryFee,
    required this.deliveryAddress,
    this.deliveryLatitude,
    this.deliveryLongitude,
    this.specialInstructions,
    this.estimatedDeliveryTime,
    required this.createdAt,
    required this.updatedAt,
    required this.restaurant,
    this.driver,
    required this.items,
    this.ratings,
  });

  factory Order.fromJson(Map<String, dynamic> json) {
    return Order(
      id: json['id'],
      orderNumber: json['orderNumber'],
      status: json['status'],
      totalAmount: json['totalAmount'].toDouble(),
      deliveryFee: json['deliveryFee'].toDouble(),
      deliveryAddress: json['deliveryAddress'],
      deliveryLatitude: json['deliveryLatitude']?.toDouble(),
      deliveryLongitude: json['deliveryLongitude']?.toDouble(),
      specialInstructions: json['specialInstructions'],
      estimatedDeliveryTime: json['estimatedDeliveryTime'] != null
          ? DateTime.parse(json['estimatedDeliveryTime'])
          : null,
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: DateTime.parse(json['updatedAt']),
      restaurant: OrderRestaurant.fromJson(json['restaurant']),
      driver: json['driver'] != null
          ? OrderDriver.fromJson(json['driver'])
          : null,
      items: (json['items'] as List)
          .map((item) => OrderItem.fromJson(item))
          .toList(),
      ratings: json['ratings'] != null
          ? OrderRatingStatus.fromJson(json['ratings'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'orderNumber': orderNumber,
      'status': status,
      'totalAmount': totalAmount,
      'deliveryFee': deliveryFee,
      'deliveryAddress': deliveryAddress,
      'specialInstructions': specialInstructions,
      'estimatedDeliveryTime': estimatedDeliveryTime?.toIso8601String(),
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'restaurant': restaurant.toJson(),
      'driver': driver?.toJson(),
      'items': items.map((item) => item.toJson()).toList(),
      'ratings': ratings?.toJson(),
    };
  }

  // Helper method to check if order can be rated
  bool get canRate => status == 'delivered' && ratings?.canRate == true;

  // Helper method to check if order has any ratings
  bool get hasAnyRatings =>
      ratings?.hasFoodRatings == true || ratings?.hasDriverRating == true;

  bool get canBeCancelled => ['pending', 'confirmed'].contains(status);

  String get statusDisplayName {
    switch (status) {
      case 'pending':
        return 'Pending';
      case 'confirmed':
        return 'Confirmed';
      case 'preparing':
        return 'Preparing';
      case 'ready':
        return 'Ready for Pickup';
      case 'assigned':
        return 'Driver Assigned';
      case 'picked_up':
        return 'Out for Delivery';
      case 'delivered':
        return 'Delivered';
      case 'cancelled':
        return 'Cancelled';
      default:
        return status;
    }
  }
}

class OrderRestaurant {
  final String name;
  final String cuisineType;
  final String phone;
  final String? address;
  final double? latitude;
  final double? longitude;
  final Location? location;

  OrderRestaurant({
    required this.name,
    required this.cuisineType,
    required this.phone,
    this.address,
    this.latitude,
    this.longitude,
    this.location,
  });

  factory OrderRestaurant.fromJson(Map<String, dynamic> json) {
    return OrderRestaurant(
      name: json['name'],
      cuisineType: json['cuisineType'],
      phone: json['phone'],
      address: json['address'],
      latitude: json['latitude']?.toDouble(),
      longitude: json['longitude']?.toDouble(),
      location: json['location'] != null
          ? Location.fromJson(json['location'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'cuisineType': cuisineType,
      'phone': phone,
      'address': address,
      'location': location?.toJson(),
    };
  }
}

class OrderDriver {
  final String name;
  final String phone;
  final Location? currentLocation;

  OrderDriver({required this.name, required this.phone, this.currentLocation});

  factory OrderDriver.fromJson(Map<String, dynamic> json) {
    return OrderDriver(
      name: json['name'],
      phone: json['phone'],
      currentLocation: json['currentLocation'] != null
          ? Location.fromJson(json['currentLocation'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'phone': phone,
      'currentLocation': currentLocation?.toJson(),
    };
  }
}

class OrderItem {
  final String name;
  final String description;
  final String? imageUrl;
  final int quantity;
  final double unitPrice;
  final double totalPrice;
  final String? specialInstructions;

  OrderItem({
    required this.name,
    required this.description,
    this.imageUrl,
    required this.quantity,
    required this.unitPrice,
    required this.totalPrice,
    this.specialInstructions,
  });

  factory OrderItem.fromJson(Map<String, dynamic> json) {
    return OrderItem(
      name: json['name'],
      description: json['description'],
      imageUrl: json['imageUrl'],
      quantity: json['quantity'],
      unitPrice: json['unitPrice'].toDouble(),
      totalPrice: json['totalPrice'].toDouble(),
      specialInstructions: json['specialInstructions'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'description': description,
      'imageUrl': imageUrl,
      'quantity': quantity,
      'unitPrice': unitPrice,
      'totalPrice': totalPrice,
      'specialInstructions': specialInstructions,
    };
  }
}

class OrderDetail {
  final int id;
  final String orderNumber;
  final String status;
  final double totalAmount;
  final double deliveryFee;
  final String deliveryAddress;
  final Location deliveryLocation;
  final String? specialInstructions;
  final DateTime? estimatedDeliveryTime;
  final DateTime createdAt;
  final DateTime updatedAt;
  final OrderRestaurant restaurant;
  final OrderDriver? driver;
  final List<OrderItem> items;

  OrderDetail({
    required this.id,
    required this.orderNumber,
    required this.status,
    required this.totalAmount,
    required this.deliveryFee,
    required this.deliveryAddress,
    required this.deliveryLocation,
    this.specialInstructions,
    this.estimatedDeliveryTime,
    required this.createdAt,
    required this.updatedAt,
    required this.restaurant,
    this.driver,
    required this.items,
  });

  factory OrderDetail.fromJson(Map<String, dynamic> json) {
    return OrderDetail(
      id: json['id'],
      orderNumber: json['orderNumber'],
      status: json['status'],
      totalAmount: json['totalAmount'].toDouble(),
      deliveryFee: json['deliveryFee'].toDouble(),
      deliveryAddress: json['deliveryAddress'],
      deliveryLocation: Location.fromJson(json['deliveryLocation']),
      specialInstructions: json['specialInstructions'],
      estimatedDeliveryTime: json['estimatedDeliveryTime'] != null
          ? DateTime.parse(json['estimatedDeliveryTime'])
          : null,
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: DateTime.parse(json['updatedAt']),
      restaurant: OrderRestaurant.fromJson(json['restaurant']),
      driver: json['driver'] != null
          ? OrderDriver.fromJson(json['driver'])
          : null,
      items: (json['items'] as List)
          .map((item) => OrderItem.fromJson(item))
          .toList(),
    );
  }

  bool get canBeCancelled => ['pending', 'confirmed'].contains(status);

  String get statusDisplayName {
    switch (status) {
      case 'pending':
        return 'Pending';
      case 'confirmed':
        return 'Confirmed';
      case 'preparing':
        return 'Preparing';
      case 'ready':
        return 'Ready for Pickup';
      case 'assigned':
        return 'Driver Assigned';
      case 'picked_up':
        return 'Out for Delivery';
      case 'delivered':
        return 'Delivered';
      case 'cancelled':
        return 'Cancelled';
      default:
        return status;
    }
  }
}

// Import Location class from restaurant.dart
class Location {
  final double latitude;
  final double longitude;

  Location({required this.latitude, required this.longitude});

  factory Location.fromJson(Map<String, dynamic> json) {
    return Location(
      latitude: json['latitude'].toDouble(),
      longitude: json['longitude'].toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {'latitude': latitude, 'longitude': longitude};
  }
}
