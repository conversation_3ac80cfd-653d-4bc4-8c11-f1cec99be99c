import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:firebase_auth/firebase_auth.dart';

class FirebaseConfig {
  static FirebaseApp? _app;
  static FirebaseStorage? _storage;
  static FirebaseAuth? _auth;

  // Firebase configuration options
  static const FirebaseOptions _firebaseOptions = FirebaseOptions(
    apiKey: 'AIzaSyDdYQ-PNUxbgiyg-6wqnBGxVRJZ1AfTPp4',
    authDomain: 'brsima-30138.firebaseapp.com',
    projectId: 'brsima-30138',
    storageBucket: 'brsima-30138.firebasestorage.app',
    messagingSenderId: '120480442939',
    appId: '1:120480442939:android:8df3ccb4a0b86d9120ca1e',
  );

  /// Initialize Firebase
  static Future<void> initialize() async {
    try {
      _app = await Firebase.initializeApp(
        name: 'brsima-restaurant',
        options: _firebaseOptions,
      );

      _storage = FirebaseStorage.instanceFor(app: _app!);
      _auth = FirebaseAuth.instanceFor(app: _app!);

      print('✅ Firebase initialized successfully for Restaurant app');
    } catch (e) {
      print('❌ Firebase initialization failed: $e');
      rethrow;
    }
  }

  /// Get Firebase Storage instance
  static FirebaseStorage get storage {
    if (_storage == null) {
      throw Exception(
        'Firebase not initialized. Call FirebaseConfig.initialize() first.',
      );
    }
    return _storage!;
  }

  /// Get Firebase Auth instance
  static FirebaseAuth get auth {
    if (_auth == null) {
      throw Exception(
        'Firebase not initialized. Call FirebaseConfig.initialize() first.',
      );
    }
    return _auth!;
  }

  /// Get Firebase App instance
  static FirebaseApp get app {
    if (_app == null) {
      throw Exception(
        'Firebase not initialized. Call FirebaseConfig.initialize() first.',
      );
    }
    return _app!;
  }

  /// Check if Firebase is initialized
  static bool get isInitialized => _app != null;
}

/// Firebase Storage paths for restaurant-related images
class FirebaseStoragePaths {
  static const String restaurants = 'restaurants';
  static const String menuItems = 'menu-items';
  static const String temp = 'temp';

  /// Get restaurant profile image path
  static String restaurantProfileImage(String restaurantId) {
    return '$restaurants/$restaurantId/profile';
  }

  /// Get restaurant banner image path
  static String restaurantBannerImage(String restaurantId) {
    return '$restaurants/$restaurantId/banner';
  }

  /// Get restaurant gallery image path
  static String restaurantGalleryImage(String restaurantId) {
    return '$restaurants/$restaurantId/gallery';
  }

  /// Get menu item image path
  static String menuItemImage(String restaurantId) {
    return '$menuItems/$restaurantId';
  }

  /// Get temporary upload path
  static String tempUpload(String userId) {
    return '$temp/$userId';
  }
}
