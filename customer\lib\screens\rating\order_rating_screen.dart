import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../models/rating.dart';
import '../../providers/rating_provider.dart';
import '../../widgets/rating_widgets.dart';

class OrderRatingScreen extends StatefulWidget {
  final int orderId;
  final String orderNumber;

  const OrderRatingScreen({
    Key? key,
    required this.orderId,
    required this.orderNumber,
  }) : super(key: key);

  @override
  State<OrderRatingScreen> createState() => _OrderRatingScreenState();
}

class _OrderRatingScreenState extends State<OrderRatingScreen> {
  AvailableRatings? _availableRatings;
  bool _isLoading = true;
  String? _error;

  // Rating data
  final Map<int, int> _foodRatings = {};
  final Map<int, String> _foodReviews = {};
  int? _driverRating;
  String? _driverReview;

  @override
  void initState() {
    super.initState();
    _loadAvailableRatings();
  }

  Future<void> _loadAvailableRatings() async {
    try {
      final ratingProvider = Provider.of<RatingProvider>(
        context,
        listen: false,
      );
      final ratings = await ratingProvider.getAvailableRatings(widget.orderId);

      if (mounted) {
        setState(() {
          _availableRatings = ratings;
          _isLoading = false;
          _error = null;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _error = e.toString();
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _submitRatings() async {
    if (_availableRatings == null) return;

    final ratingProvider = Provider.of<RatingProvider>(context, listen: false);
    bool hasRatingsToSubmit = false;

    try {
      // Submit food ratings
      final foodRatingsToSubmit = <Map<String, dynamic>>[];
      for (final item in _availableRatings!.menuItems) {
        if (!item.alreadyRated && _foodRatings.containsKey(item.menuItemId)) {
          foodRatingsToSubmit.add({
            'menuItemId': item.menuItemId,
            'orderId': widget.orderId,
            'rating': _foodRatings[item.menuItemId]!,
            'reviewText': _foodReviews[item.menuItemId],
          });
          hasRatingsToSubmit = true;
        }
      }

      if (foodRatingsToSubmit.isNotEmpty) {
        await ratingProvider.submitMultipleFoodRatings(foodRatingsToSubmit);
      }

      // Submit driver rating
      if (_availableRatings!.driver != null &&
          !_availableRatings!.driver!.alreadyRated &&
          _driverRating != null) {
        await ratingProvider.submitDriverRating(
          driverId: _availableRatings!.driver!.driverId,
          orderId: widget.orderId,
          rating: _driverRating!,
          reviewText: _driverReview,
        );
        hasRatingsToSubmit = true;
      }

      if (hasRatingsToSubmit) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Thank you for your feedback!'),
              backgroundColor: Colors.green,
            ),
          );
          Navigator.of(
            context,
          ).pop(true); // Return true to indicate ratings were submitted
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Please provide at least one rating'),
              backgroundColor: Colors.orange,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to submit ratings: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Rate Order #${widget.orderNumber}'),
        backgroundColor: Colors.orange,
        foregroundColor: Colors.white,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _error != null
          ? _buildErrorState()
          : _availableRatings == null
          ? _buildEmptyState()
          : _buildRatingForm(),
      bottomNavigationBar: _availableRatings != null && !_isLoading
          ? _buildSubmitButton()
          : null,
    );
  }

  Widget _buildErrorState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.error_outline, size: 64, color: Colors.red),
          const SizedBox(height: 16),
          Text(
            'Failed to load rating options',
            style: Theme.of(context).textTheme.headlineSmall,
          ),
          const SizedBox(height: 8),
          Text(
            _error ?? 'Unknown error occurred',
            textAlign: TextAlign.center,
            style: Theme.of(context).textTheme.bodyMedium,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: _loadAvailableRatings,
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.star_outline, size: 64, color: Colors.grey),
          SizedBox(height: 16),
          Text(
            'No items available for rating',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.w500),
          ),
          SizedBox(height: 8),
          Text(
            'All items in this order have already been rated',
            textAlign: TextAlign.center,
            style: TextStyle(color: Colors.grey),
          ),
        ],
      ),
    );
  }

  Widget _buildRatingForm() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Food items section
          if (_availableRatings!.menuItems.isNotEmpty) ...[
            Text(
              'Rate Food Items',
              style: Theme.of(
                context,
              ).textTheme.headlineSmall?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            ..._availableRatings!.menuItems.map(
              (item) => _buildFoodRatingCard(item),
            ),
          ],

          // Driver section
          if (_availableRatings!.driver != null &&
              !_availableRatings!.driver!.alreadyRated) ...[
            const SizedBox(height: 24),
            Text(
              'Rate Delivery',
              style: Theme.of(
                context,
              ).textTheme.headlineSmall?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            _buildDriverRatingCard(_availableRatings!.driver!),
          ],

          const SizedBox(height: 100), // Space for bottom button
        ],
      ),
    );
  }

  Widget _buildFoodRatingCard(RatableItem item) {
    if (item.alreadyRated) return const SizedBox.shrink();

    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                if (item.imageUrl != null)
                  ClipRRect(
                    borderRadius: BorderRadius.circular(8),
                    child: Image.network(
                      item.imageUrl!,
                      width: 60,
                      height: 60,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) => Container(
                        width: 60,
                        height: 60,
                        color: Colors.grey[300],
                        child: const Icon(Icons.restaurant),
                      ),
                    ),
                  ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        item.name,
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                      if (item.description != null)
                        Text(
                          item.description!,
                          style: TextStyle(
                            color: Colors.grey[600],
                            fontSize: 14,
                          ),
                        ),
                      Text(
                        'Qty: ${item.quantity} • \$${item.unitPrice.toStringAsFixed(2)}',
                        style: TextStyle(color: Colors.grey[600], fontSize: 14),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            StarRating(
              rating: _foodRatings[item.menuItemId] ?? 0,
              onRatingChanged: (rating) {
                setState(() {
                  _foodRatings[item.menuItemId] = rating;
                });
              },
            ),
            const SizedBox(height: 12),
            TextField(
              decoration: const InputDecoration(
                hintText: 'Write a review (optional)',
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
              onChanged: (value) {
                if (value.trim().isEmpty) {
                  _foodReviews.remove(item.menuItemId);
                } else {
                  _foodReviews[item.menuItemId] = value.trim();
                }
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDriverRatingCard(RatableDriver driver) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const CircleAvatar(
                  backgroundColor: Colors.orange,
                  child: Icon(Icons.delivery_dining, color: Colors.white),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        driver.driverName,
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                      const Text(
                        'Delivery Driver',
                        style: TextStyle(color: Colors.grey, fontSize: 14),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            StarRating(
              rating: _driverRating ?? 0,
              onRatingChanged: (rating) {
                setState(() {
                  _driverRating = rating;
                });
              },
            ),
            const SizedBox(height: 12),
            TextField(
              decoration: const InputDecoration(
                hintText: 'How was your delivery experience? (optional)',
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
              onChanged: (value) {
                _driverReview = value.trim().isEmpty ? null : value.trim();
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSubmitButton() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: SizedBox(
        width: double.infinity,
        child: ElevatedButton(
          onPressed: _submitRatings,
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.orange,
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(vertical: 16),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
          ),
          child: Consumer<RatingProvider>(
            builder: (context, ratingProvider, child) {
              if (ratingProvider.isLoading) {
                return const SizedBox(
                  height: 20,
                  width: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  ),
                );
              }
              return const Text(
                'Submit Ratings',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
              );
            },
          ),
        ),
      ),
    );
  }
}
