import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:geolocator/geolocator.dart';

class LocationTrackingService {
  static final LocationTrackingService _instance = LocationTrackingService._internal();
  factory LocationTrackingService() => _instance;
  LocationTrackingService._internal();

  StreamSubscription<Position>? _positionStreamSubscription;
  Position? _lastKnownPosition;
  bool _isTracking = false;
  
  // Callbacks for location updates
  final List<Function(Position)> _locationCallbacks = [];
  
  // Getters
  bool get isTracking => _isTracking;
  Position? get lastKnownPosition => _lastKnownPosition;

  /// Add a callback to receive location updates
  void addLocationCallback(Function(Position) callback) {
    _locationCallbacks.add(callback);
  }

  /// Remove a location callback
  void removeLocationCallback(Function(Position) callback) {
    _locationCallbacks.remove(callback);
  }

  /// Start tracking location with optimized settings for delivery
  Future<bool> startTracking({
    LocationAccuracy accuracy = LocationAccuracy.high,
    int distanceFilter = 10, // meters
    int timeInterval = 5000, // milliseconds
  }) async {
    try {
      // Check if already tracking
      if (_isTracking) {
        return true;
      }

      // Check permissions
      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          throw Exception('Location permissions are denied');
        }
      }

      if (permission == LocationPermission.deniedForever) {
        throw Exception('Location permissions are permanently denied');
      }

      // Check if location services are enabled
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        throw Exception('Location services are disabled');
      }

      // Configure location settings for delivery tracking
      const LocationSettings locationSettings = LocationSettings(
        accuracy: LocationAccuracy.high,
        distanceFilter: 10, // Update every 10 meters
        timeLimit: Duration(seconds: 30), // Timeout for location requests
      );

      // Start position stream
      _positionStreamSubscription = Geolocator.getPositionStream(
        locationSettings: locationSettings,
      ).listen(
        (Position position) {
          _lastKnownPosition = position;
          _notifyLocationCallbacks(position);
        },
        onError: (error) {
          debugPrint('Location tracking error: $error');
        },
      );

      _isTracking = true;
      debugPrint('Location tracking started');
      return true;

    } catch (e) {
      debugPrint('Failed to start location tracking: $e');
      return false;
    }
  }

  /// Stop location tracking
  Future<void> stopTracking() async {
    if (_positionStreamSubscription != null) {
      await _positionStreamSubscription!.cancel();
      _positionStreamSubscription = null;
    }
    _isTracking = false;
    debugPrint('Location tracking stopped');
  }

  /// Get current location once (without starting continuous tracking)
  Future<Position?> getCurrentLocation() async {
    try {
      // Check permissions
      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          return null;
        }
      }

      if (permission == LocationPermission.deniedForever) {
        return null;
      }

      // Check if location services are enabled
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        return null;
      }

      Position position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
        timeLimit: const Duration(seconds: 10),
      );

      _lastKnownPosition = position;
      return position;

    } catch (e) {
      debugPrint('Failed to get current location: $e');
      return null;
    }
  }

  /// Calculate distance between two positions in meters
  double calculateDistance(
    double startLatitude,
    double startLongitude,
    double endLatitude,
    double endLongitude,
  ) {
    return Geolocator.distanceBetween(
      startLatitude,
      startLongitude,
      endLatitude,
      endLongitude,
    );
  }

  /// Calculate bearing between two positions in degrees
  double calculateBearing(
    double startLatitude,
    double startLongitude,
    double endLatitude,
    double endLongitude,
  ) {
    return Geolocator.bearingBetween(
      startLatitude,
      startLongitude,
      endLatitude,
      endLongitude,
    );
  }

  /// Check if location permissions are granted
  Future<bool> hasLocationPermission() async {
    LocationPermission permission = await Geolocator.checkPermission();
    return permission == LocationPermission.always || 
           permission == LocationPermission.whileInUse;
  }

  /// Request location permissions
  Future<bool> requestLocationPermission() async {
    LocationPermission permission = await Geolocator.checkPermission();
    
    if (permission == LocationPermission.denied) {
      permission = await Geolocator.requestPermission();
    }
    
    return permission == LocationPermission.always || 
           permission == LocationPermission.whileInUse;
  }

  /// Open location settings
  Future<bool> openLocationSettings() async {
    return await Geolocator.openLocationSettings();
  }

  /// Open app settings
  Future<bool> openAppSettings() async {
    return await Geolocator.openAppSettings();
  }

  /// Notify all registered callbacks about location updates
  void _notifyLocationCallbacks(Position position) {
    for (var callback in _locationCallbacks) {
      try {
        callback(position);
      } catch (e) {
        debugPrint('Error in location callback: $e');
      }
    }
  }

  /// Dispose resources
  Future<void> dispose() async {
    await stopTracking();
    _locationCallbacks.clear();
    _lastKnownPosition = null;
  }

  /// Get location accuracy description
  String getAccuracyDescription(LocationAccuracy accuracy) {
    switch (accuracy) {
      case LocationAccuracy.lowest:
        return 'Lowest (500m)';
      case LocationAccuracy.low:
        return 'Low (500m)';
      case LocationAccuracy.medium:
        return 'Medium (100-500m)';
      case LocationAccuracy.high:
        return 'High (0-100m)';
      case LocationAccuracy.best:
        return 'Best (0-100m)';
      case LocationAccuracy.bestForNavigation:
        return 'Best for Navigation (0-100m)';
      default:
        return 'Unknown';
    }
  }

  /// Check if location is stale (older than specified duration)
  bool isLocationStale(Duration maxAge) {
    if (_lastKnownPosition == null) return true;
    
    final now = DateTime.now();
    final locationTime = _lastKnownPosition!.timestamp;
    final age = now.difference(locationTime);
    
    return age > maxAge;
  }
}
