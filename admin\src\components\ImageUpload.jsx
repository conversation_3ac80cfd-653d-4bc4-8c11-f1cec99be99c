import React, { useState, useRef } from 'react';
import { ref, uploadBytes, getDownloadURL, deleteObject } from 'firebase/storage';
import { storage } from '../config/firebase';
import toast from 'react-hot-toast';

const ImageUpload = ({ 
  value, 
  onChange, 
  uploadPath = 'admin/general',
  placeholder = 'Upload Image',
  className = '',
  disabled = false 
}) => {
  const [isUploading, setIsUploading] = useState(false);
  // const [uploadProgress, setUploadProgress] = useState(0);
  const fileInputRef = useRef(null);

  const validateFile = (file) => {
    // Check file type
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
    if (!allowedTypes.includes(file.type)) {
      throw new Error('Please select a valid image file (JPEG, PNG, GIF, WebP)');
    }

    // Check file size (5MB limit)
    const maxSize = 5 * 1024 * 1024; // 5MB
    if (file.size > maxSize) {
      throw new Error('Image size must be less than 5MB');
    }

    return true;
  };

  const generateFileName = (originalName) => {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 15);
    const extension = originalName.split('.').pop();
    return `${timestamp}_${random}.${extension}`;
  };

  const handleFileSelect = async (event) => {
    const file = event.target.files[0];
    if (!file) return;

    try {
      validateFile(file);
      await uploadFile(file);
    } catch (error) {
      toast.error(error.message);
    }

    // Reset file input
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const uploadFile = async (file) => {
    setIsUploading(true);
    // setUploadProgress(0);

    try {
      // Generate unique filename
      const fileName = generateFileName(file.name);
      const fullPath = `${uploadPath}/${fileName}`;

      // Create storage reference
      const storageRef = ref(storage, fullPath);

      // Upload file
      const snapshot = await uploadBytes(storageRef, file);
      
      // Get download URL
      const downloadURL = await getDownloadURL(snapshot.ref);

      // Update parent component
      onChange(downloadURL);

      toast.success('Image uploaded successfully!');
      
    } catch (error) {
      console.error('Upload error:', error);
      toast.error(`Failed to upload image: ${error.message}`);
    } finally {
      setIsUploading(false);
      // setUploadProgress(0);
    }
  };

  const handleRemoveImage = async () => {
    if (!value) return;

    try {
      // Extract path from Firebase URL
      const url = new URL(value);
      const pathMatch = url.pathname.match(/\/o\/(.+?)(\?|$)/);
      
      if (pathMatch) {
        const decodedPath = decodeURIComponent(pathMatch[1]);
        const storageRef = ref(storage, decodedPath);
        
        // Delete from Firebase Storage
        await deleteObject(storageRef);
      }

      // Clear the value
      onChange('');
      toast.success('Image removed successfully!');

    } catch (error) {
      console.error('Delete error:', error);
      toast.error(`Failed to remove image: ${error.message}`);
    }
  };

  const triggerFileSelect = () => {
    if (!disabled && fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  return (
    <div className={`space-y-2 ${className}`}>
      <label className="block text-sm font-medium text-gray-700">
        Image
      </label>
      
      <div className="flex items-start space-x-4">
        {/* Image Preview */}
        <div className="flex-shrink-0">
          <div 
            className={`
              relative w-32 h-32 border-2 border-dashed border-gray-300 rounded-lg
              flex items-center justify-center cursor-pointer hover:border-gray-400
              transition-colors duration-200 overflow-hidden
              ${disabled ? 'opacity-50 cursor-not-allowed' : ''}
              ${isUploading ? 'border-blue-400 bg-blue-50' : ''}
            `}
            onClick={triggerFileSelect}
          >
            {value ? (
              <>
                <img
                  src={value}
                  alt="Preview"
                  className="w-full h-full object-cover rounded-lg"
                />
                {!disabled && (
                  <button
                    type="button"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleRemoveImage();
                    }}
                    className="absolute top-1 right-1 bg-red-500 text-white rounded-full p-1 hover:bg-red-600 transition-colors"
                  >
                    <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                )}
              </>
            ) : (
              <div className="text-center">
                {isUploading ? (
                  <div className="space-y-2">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                    <p className="text-xs text-blue-600">Uploading...</p>
                  </div>
                ) : (
                  <div className="space-y-2">
                    <svg className="mx-auto h-8 w-8 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                      <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" strokeWidth={2} strokeLinecap="round" strokeLinejoin="round" />
                    </svg>
                    <p className="text-xs text-gray-500">{placeholder}</p>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>

        {/* Upload Instructions */}
        <div className="flex-1 text-sm text-gray-600">
          <p className="mb-2">Click the box to upload an image</p>
          <ul className="list-disc list-inside space-y-1 text-xs">
            <li>Supported formats: JPEG, PNG, GIF, WebP</li>
            <li>Maximum file size: 5MB</li>
            <li>Recommended size: 1024x1024 pixels</li>
          </ul>
        </div>
      </div>

      {/* Hidden file input */}
      <input
        ref={fileInputRef}
        type="file"
        accept="image/*"
        onChange={handleFileSelect}
        className="hidden"
        disabled={disabled}
      />

      {/* Current URL display (for debugging) */}
      {value && (
        <div className="text-xs text-gray-500 break-all">
          <strong>URL:</strong> {value}
        </div>
      )}
    </div>
  );
};

export default ImageUpload;
