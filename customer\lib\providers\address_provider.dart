import 'package:flutter/foundation.dart';
import '../models/address.dart';
import '../services/api_service.dart';

class AddressProvider extends ChangeNotifier {
  List<CustomerAddress> _addresses = [];
  bool _isLoading = false;
  String? _error;

  // Getters
  List<CustomerAddress> get addresses => _addresses;
  bool get isLoading => _isLoading;
  String? get error => _error;

  CustomerAddress? get defaultAddress {
    try {
      return _addresses.firstWhere((address) => address.isDefault);
    } catch (e) {
      return _addresses.isNotEmpty ? _addresses.first : null;
    }
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String? error) {
    _error = error;
    notifyListeners();
  }

  void _setAddresses(List<CustomerAddress> addresses) {
    _addresses = addresses;
    notifyListeners();
  }

  /// Load all addresses for the current user
  Future<bool> loadAddresses() async {
    try {
      _setLoading(true);
      _setError(null);

      final addresses = await ApiService.getAddresses();
      _setAddresses(addresses);

      return true;
    } catch (e) {
      _setError(e.toString().replaceFirst('Exception: ', ''));
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// Add a new address
  Future<bool> addAddress({
    required String label,
    required String address,
    required double latitude,
    required double longitude,
    bool isDefault = false,
  }) async {
    try {
      _setLoading(true);
      _setError(null);

      final newAddress = await ApiService.addAddress(
        label: label,
        address: address,
        latitude: latitude,
        longitude: longitude,
        isDefault: isDefault,
      );

      // If this is set as default, update other addresses
      if (isDefault) {
        _addresses = _addresses
            .map((addr) => addr.copyWith(isDefault: false))
            .toList();
      }

      _addresses.add(newAddress);
      notifyListeners();

      return true;
    } catch (e) {
      _setError(e.toString().replaceFirst('Exception: ', ''));
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// Update an existing address
  Future<bool> updateAddress({
    required int addressId,
    String? label,
    String? address,
    double? latitude,
    double? longitude,
    bool? isDefault,
  }) async {
    try {
      _setLoading(true);
      _setError(null);

      final updatedAddress = await ApiService.updateAddress(
        addressId: addressId,
        label: label,
        address: address,
        latitude: latitude,
        longitude: longitude,
        isDefault: isDefault,
      );

      // Update local list
      final index = _addresses.indexWhere((addr) => addr.id == addressId);
      if (index != -1) {
        // If this is set as default, update other addresses
        if (isDefault == true) {
          _addresses = _addresses
              .map(
                (addr) => addr.id == addressId
                    ? addr
                    : addr.copyWith(isDefault: false),
              )
              .toList();
        }

        _addresses[index] = updatedAddress;
        notifyListeners();
      }

      return true;
    } catch (e) {
      _setError(e.toString().replaceFirst('Exception: ', ''));
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// Delete an address
  Future<bool> deleteAddress(int addressId) async {
    try {
      _setLoading(true);
      _setError(null);

      await ApiService.deleteAddress(addressId);

      // Remove from local list
      _addresses.removeWhere((addr) => addr.id == addressId);
      notifyListeners();

      return true;
    } catch (e) {
      _setError(e.toString().replaceFirst('Exception: ', ''));
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// Set an address as default
  Future<bool> setDefaultAddress(int addressId) async {
    return await updateAddress(addressId: addressId, isDefault: true);
  }

  /// Get address by ID
  CustomerAddress? getAddressById(int id) {
    try {
      return _addresses.firstWhere((address) => address.id == id);
    } catch (e) {
      return null;
    }
  }

  /// Clear all addresses (for logout)
  void clearAddresses() {
    _addresses.clear();
    _error = null;
    notifyListeners();
  }

  /// Refresh addresses
  Future<bool> refreshAddresses() async {
    return await loadAddresses();
  }
}
