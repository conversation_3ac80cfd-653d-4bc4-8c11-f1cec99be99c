class User {
  final int id;
  final String email;
  final String userType;

  User({
    required this.id,
    required this.email,
    required this.userType,
  });

  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      id: json['id'],
      email: json['email'],
      userType: json['userType'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'email': email,
      'userType': userType,
    };
  }
}

class CustomerProfile {
  final int? id;
  final String? fullName;
  final String? phone;
  final String? address;
  final double? latitude;
  final double? longitude;
  final bool profileCompleted;

  CustomerProfile({
    this.id,
    this.fullName,
    this.phone,
    this.address,
    this.latitude,
    this.longitude,
    this.profileCompleted = false,
  });

  factory CustomerProfile.fromJson(Map<String, dynamic> json) {
    return CustomerProfile(
      id: json['id'],
      fullName: json['fullName'],
      phone: json['phone'],
      address: json['address'],
      latitude: _parseDouble(json['latitude']),
      longitude: _parseDouble(json['longitude']),
      profileCompleted: json['profileCompleted'] ?? false,
    );
  }
  
  // Helper method to safely parse latitude/longitude
  static double? _parseDouble(dynamic value) {
    if (value == null) return null;
    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) {
      return double.tryParse(value);
    }
    return null;
  }

  Map<String, dynamic> toJson() {
    return {
      'fullName': fullName,
      'phone': phone,
      'address': address,
      'latitude': latitude,
      'longitude': longitude,
    };
  }
}
