const http = require('http');

function makeRequest(options, data = null) {
  return new Promise((resolve, reject) => {
    const req = http.request(options, (res) => {
      let responseData = '';
      res.on('data', (chunk) => { responseData += chunk; });
      res.on('end', () => {
        resolve({
          statusCode: res.statusCode,
          data: responseData,
          headers: res.headers
        });
      });
    });

    req.on('error', reject);
    
    if (data) {
      req.write(data);
    }
    req.end();
  });
}

async function testRatingWithRealAuth() {
  try {
    console.log('🔐 Testing Rating System with Real Authentication...\n');
    
    // Step 1: Login to get real token
    console.log('1. Logging <NAME_EMAIL>...');
    const loginData = JSON.stringify({
      email: '<EMAIL>',
      password: 'password'
    });

    const loginOptions = {
      hostname: 'localhost',
      port: 3000,
      path: '/api/auth/login',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      }
    };

    const loginResponse = await makeRequest(loginOptions, loginData);
    console.log(`Login Status: ${loginResponse.statusCode}`);
    
    if (loginResponse.statusCode !== 200) {
      console.log('❌ Login failed:', loginResponse.data);
      return;
    }

    const loginResult = JSON.parse(loginResponse.data);
    const token = loginResult.token;
    console.log('✅ Login successful! Got token.');
    console.log('User info:', {
      id: loginResult.user.id,
      email: loginResult.user.email,
      userType: loginResult.user.userType
    });

    // Step 2: Test food rating submission
    console.log('\n2. Testing food rating submission...');
    const foodRatingData = JSON.stringify({
      menuItemId: 26,
      orderId: 34,
      rating: 5,
      reviewText: "Great food!"
    });

    const foodRatingOptions = {
      hostname: 'localhost',
      port: 3000,
      path: '/api/ratings/food',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      }
    };

    const foodRatingResponse = await makeRequest(foodRatingOptions, foodRatingData);
    console.log(`Food Rating Status: ${foodRatingResponse.statusCode}`);
    console.log('Food Rating Response:', foodRatingResponse.data);

    // Step 3: Test driver rating submission
    console.log('\n3. Testing driver rating submission...');
    const driverRatingData = JSON.stringify({
      driverId: 1,
      orderId: 34,
      rating: 4,
      reviewText: "Good service!"
    });

    const driverRatingOptions = {
      hostname: 'localhost',
      port: 3000,
      path: '/api/ratings/driver',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      }
    };

    const driverRatingResponse = await makeRequest(driverRatingOptions, driverRatingData);
    console.log(`Driver Rating Status: ${driverRatingResponse.statusCode}`);
    console.log('Driver Rating Response:', driverRatingResponse.data);

    // Step 4: Test getting order ratings
    console.log('\n4. Testing get order ratings...');
    const getRatingsOptions = {
      hostname: 'localhost',
      port: 3000,
      path: '/api/ratings/customer/order/34',
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`
      }
    };

    const getRatingsResponse = await makeRequest(getRatingsOptions);
    console.log(`Get Ratings Status: ${getRatingsResponse.statusCode}`);
    console.log('Get Ratings Response:', getRatingsResponse.data);

    // Step 5: Test getting available ratings
    console.log('\n5. Testing get available ratings...');
    const getAvailableOptions = {
      hostname: 'localhost',
      port: 3000,
      path: '/api/ratings/customer/order/34/available',
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`
      }
    };

    const getAvailableResponse = await makeRequest(getAvailableOptions);
    console.log(`Get Available Ratings Status: ${getAvailableResponse.statusCode}`);
    console.log('Get Available Ratings Response:', getAvailableResponse.data);

    console.log('\n🎉 Rating system test completed!');

  } catch (error) {
    console.error('❌ Test error:', error);
  }
}

testRatingWithRealAuth();
