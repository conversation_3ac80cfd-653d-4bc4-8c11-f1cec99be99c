class Restaurant {
  final int id;
  final String name;
  final String description;
  final String cuisineType;
  final String address;
  final Location location;
  final double rating;
  final int totalOrders;
  final bool isOpen;
  final String? distance;
  final DateTime createdAt;

  Restaurant({
    required this.id,
    required this.name,
    required this.description,
    required this.cuisineType,
    required this.address,
    required this.location,
    required this.rating,
    required this.totalOrders,
    required this.isOpen,
    this.distance,
    required this.createdAt,
  });

  factory Restaurant.fromJson(Map<String, dynamic> json) {
    return Restaurant(
      id: json['id'],
      name: json['name'],
      description: json['description'],
      cuisineType: json['cuisineType'],
      address: json['address'],
      location: Location.fromJson(json['location']),
      rating: json['rating'].toDouble(),
      totalOrders: json['totalOrders'],
      isOpen: json['isOpen'],
      distance: json['distance'],
      createdAt: DateTime.parse(json['createdAt']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'cuisineType': cuisineType,
      'address': address,
      'location': location.toJson(),
      'rating': rating,
      'totalOrders': totalOrders,
      'isOpen': isOpen,
      'distance': distance,
      'createdAt': createdAt.toIso8601String(),
    };
  }
}

class Location {
  final double latitude;
  final double longitude;

  Location({required this.latitude, required this.longitude});

  factory Location.fromJson(Map<String, dynamic> json) {
    return Location(
      latitude: json['latitude'].toDouble(),
      longitude: json['longitude'].toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {'latitude': latitude, 'longitude': longitude};
  }
}

class MenuCategory {
  final int? id;
  final String name;
  final String? description;
  final List<MenuItem> items;

  MenuCategory({
    this.id,
    required this.name,
    this.description,
    required this.items,
  });

  factory MenuCategory.fromJson(Map<String, dynamic> json) {
    return MenuCategory(
      id: json['id'],
      name: json['name'],
      description: json['description'],
      items: (json['items'] as List)
          .map((item) => MenuItem.fromJson(item))
          .toList(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'items': items.map((item) => item.toJson()).toList(),
    };
  }
}

class MenuItem {
  final int id;
  final String name;
  final String? description;
  final double price;
  final String? imageUrl;
  final int preparationTime;
  final bool isAvailable;
  final double averageRating;
  final int totalRatings;
  final DateTime createdAt;

  MenuItem({
    required this.id,
    required this.name,
    this.description,
    required this.price,
    this.imageUrl,
    required this.preparationTime,
    required this.isAvailable,
    this.averageRating = 0.0,
    this.totalRatings = 0,
    required this.createdAt,
  });

  factory MenuItem.fromJson(Map<String, dynamic> json) {
    return MenuItem(
      id: json['id'],
      name: json['name'],
      description: json['description'],
      price: json['price'].toDouble(),
      imageUrl: json['imageUrl'],
      preparationTime: json['preparationTime'],
      isAvailable: json['isAvailable'],
      averageRating: (json['averageRating'] ?? 0).toDouble(),
      totalRatings: json['totalRatings'] ?? 0,
      createdAt: DateTime.parse(json['createdAt']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'price': price,
      'imageUrl': imageUrl,
      'preparationTime': preparationTime,
      'isAvailable': isAvailable,
      'averageRating': averageRating,
      'totalRatings': totalRatings,
      'createdAt': createdAt.toIso8601String(),
    };
  }
}

class RestaurantDetail {
  final Restaurant restaurant;
  final List<MenuCategory> menu;

  RestaurantDetail({required this.restaurant, required this.menu});

  factory RestaurantDetail.fromJson(Map<String, dynamic> json) {
    return RestaurantDetail(
      restaurant: Restaurant.fromJson(json['restaurant']),
      menu: (json['menu'] as List)
          .map((category) => MenuCategory.fromJson(category))
          .toList(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'restaurant': restaurant.toJson(),
      'menu': menu.map((category) => category.toJson()).toList(),
    };
  }
}
