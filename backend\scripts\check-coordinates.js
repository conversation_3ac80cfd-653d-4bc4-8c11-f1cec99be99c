const { pool } = require('../config/database');

async function checkCoordinates() {
  const client = await pool.connect();
  try {
    console.log('🗺️  Checking restaurant coordinates...\n');
    
    const restaurants = await client.query(`
      SELECT id, restaurant_name, latitude, longitude, address
      FROM restaurant_profiles 
      ORDER BY id
    `);
    
    console.log('📍 Restaurant Coordinates Analysis:');
    console.log('=====================================\n');
    
    restaurants.rows.forEach(r => {
      const lat = parseFloat(r.latitude);
      const lng = parseFloat(r.longitude);
      const validCoords = lat >= -90 && lat <= 90 && lng >= -180 && lng <= 180;
      const status = validCoords ? '✅ VALID' : '❌ INVALID';
      
      console.log(`${status} ${r.id}: ${r.restaurant_name}`);
      console.log(`    Coordinates: (${r.latitude}, ${r.longitude})`);
      console.log(`    Address: ${r.address}`);
      
      if (!validCoords) {
        console.log('    ⚠️  This restaurant may be filtered out by location-based queries!');
      }
      console.log('');
    });
    
    // Test distance calculation with a sample customer location
    console.log('🧪 Testing distance calculation with sample customer location...');
    console.log('Customer location: (35.174, 45.978) - Sample location in Iraq\n');
    
    const testQuery = await client.query(`
      SELECT 
        id, restaurant_name, latitude, longitude,
        (
          6371 * acos(
            cos(radians(35.174)) * cos(radians(latitude)) *
            cos(radians(longitude) - radians(45.978)) +
            sin(radians(35.174)) * sin(radians(latitude))
          )
        ) AS distance
      FROM restaurant_profiles 
      WHERE latitude IS NOT NULL AND longitude IS NOT NULL
      ORDER BY distance
    `);
    
    console.log('📏 Distance calculations:');
    testQuery.rows.forEach(r => {
      const distance = parseFloat(r.distance);
      const withinRadius = distance <= 10; // 10km default radius
      const status = withinRadius ? '✅ WITHIN RADIUS' : '❌ TOO FAR';
      console.log(`${status} ${r.restaurant_name}: ${distance.toFixed(2)} km`);
    });
    
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    client.release();
    process.exit(0);
  }
}

checkCoordinates();
