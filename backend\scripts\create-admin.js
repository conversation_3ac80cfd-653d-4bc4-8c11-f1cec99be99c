const bcrypt = require('bcryptjs');
const { Pool } = require('pg');
const path = require('path');
require('dotenv').config({ path: path.join(__dirname, '..', '.env') });

// Create database connection
const pool = new Pool({
  connectionString: process.env.DB_URL,
  ssl: process.env.NODE_ENV === 'production' ? {
    rejectUnauthorized: false
  } : false
});

const query = (text, params) => pool.query(text, params);

async function createAdminUser() {
  try {
    const email = process.argv[2] || '<EMAIL>';
    const password = process.argv[3] || 'admin123';

    console.log('Creating admin user...');
    console.log('Email:', email);

    // Check if admin user already exists
    const existingUser = await query('SELECT id FROM users WHERE email = $1', [email]);
    
    if (existingUser.rows.length > 0) {
      console.log('❌ Admin user already exists with this email');
      process.exit(1);
    }

    // Hash password
    const saltRounds = 12;
    const passwordHash = await bcrypt.hash(password, saltRounds);

    // Create admin user
    const result = await query(
      'INSERT INTO users (email, password_hash, user_type, is_verified, is_active) VALUES ($1, $2, $3, $4, $5) RETURNING id, email, user_type, created_at',
      [email, passwordHash, 'admin', true, true]
    );

    const user = result.rows[0];

    console.log('✅ Admin user created successfully!');
    console.log('User ID:', user.id);
    console.log('Email:', user.email);
    console.log('User Type:', user.user_type);
    console.log('Created At:', user.created_at);
    console.log('\nYou can now login to the admin panel with:');
    console.log('Email:', email);
    console.log('Password:', password);

  } catch (error) {
    console.error('❌ Error creating admin user:', error.message);
    await pool.end();
    process.exit(1);
  } finally {
    await pool.end();
    process.exit(0);
  }
}

// Usage: node scripts/create-admin.js [email] [password]
// Example: node scripts/create-admin.js <EMAIL> mypassword123
createAdminUser();
