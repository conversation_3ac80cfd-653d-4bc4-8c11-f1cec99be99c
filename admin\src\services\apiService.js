import axios from 'axios';

const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:3000/api';

// Create axios instance
const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Response interceptor for error handling
api.interceptors.response.use(
  (response) => response.data,
  (error) => {
    if (error.response?.status === 401) {
      localStorage.removeItem('admin_token');
      window.location.href = '/login';
    }
    throw error.response?.data || error;
  }
);

// API service object with arrow functions
export const apiService = {
  setAuthToken: (token) => {
    if (token) {
      api.defaults.headers.common['Authorization'] = `Bearer ${token}`;
    } else {
      delete api.defaults.headers.common['Authorization'];
    }
  },

  // Auth endpoints
  login: (email, password) => {
    return api.post('/auth/login', { email, password });
  },

  getCurrentUser: () => {
    return api.get('/auth/me');
  },

  // Admin endpoints
  getStats: () => {
    return api.get('/admin/stats');
  },

  // Restaurant management
  getRestaurants: () => {
    return api.get('/admin/restaurants');
  },

  createRestaurant: (restaurantData) => {
    return api.post('/admin/restaurants', restaurantData);
  },

  // Driver management
  getDrivers: () => {
    return api.get('/admin/drivers');
  },

  createDriver: (driverData) => {
    return api.post('/admin/drivers', driverData);
  },

  // Customer management
  getCustomers: () => {
    return api.get('/admin/customers');
  },

  // User management
  toggleUserStatus: (userId, isActive) => {
    return api.patch(`/admin/users/${userId}/status`, { isActive });
  },

  // Product management
  getRestaurantMenu: (restaurantId) => {
    return api.get(`/admin/restaurants/${restaurantId}/menu`);
  },

  createCategory: (restaurantId, categoryData) => {
    return api.post(`/admin/restaurants/${restaurantId}/categories`, categoryData);
  },

  createMenuItem: (restaurantId, menuItemData) => {
    return api.post(`/admin/restaurants/${restaurantId}/menu-items`, menuItemData);
  },

  updateMenuItem: (itemId, menuItemData) => {
    return api.patch(`/admin/menu-items/${itemId}`, menuItemData);
  },

  deleteMenuItem: (itemId) => {
    return api.delete(`/admin/menu-items/${itemId}`);
  },

  // Order management
  getOrders: (params = {}) => {
    const queryString = new URLSearchParams(params).toString();
    return api.get(`/admin/orders${queryString ? `?${queryString}` : ''}`);
  },

  getOrderDetails: (orderId) => {
    return api.get(`/admin/orders/${orderId}`);
  },

  updateOrderStatus: (orderId, status) => {
    return api.patch(`/admin/orders/${orderId}/status`, { status });
  },

  getOrderAnalytics: (period = '7d') => {
    return api.get(`/admin/analytics/orders?period=${period}`);
  },

  getRatingAnalytics: () => {
    return api.get('/admin/analytics/ratings');
  },

  // Settings management
  getSettings: () => {
    return api.get('/admin/settings');
  },

  updateSetting: (key, value) => {
    return api.put(`/admin/settings/${key}`, { value });
  },
};
