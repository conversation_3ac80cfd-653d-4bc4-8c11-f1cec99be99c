class FoodRating {
  final int? id;
  final int menuItemId;
  final int orderId;
  final int rating;
  final String? reviewText;
  final String? customerName;
  final DateTime? createdAt;

  FoodRating({
    this.id,
    required this.menuItemId,
    required this.orderId,
    required this.rating,
    this.reviewText,
    this.customerName,
    this.createdAt,
  });

  factory FoodRating.fromJson(Map<String, dynamic> json) {
    return FoodRating(
      id: json['id'],
      menuItemId: json['menuItemId'] ?? json['menu_item_id'],
      orderId: json['orderId'] ?? json['order_id'],
      rating: json['rating'],
      reviewText: json['reviewText'] ?? json['review_text'],
      customerName: json['customerName'] ?? json['customer_name'],
      createdAt: json['createdAt'] != null
          ? DateTime.parse(json['createdAt'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'menuItemId': menuItemId,
      'orderId': orderId,
      'rating': rating,
      'reviewText': reviewText,
    };
  }
}

class DriverRating {
  final int? id;
  final int driverId;
  final int orderId;
  final int rating;
  final String? reviewText;
  final String? customerName;
  final DateTime? createdAt;

  DriverRating({
    this.id,
    required this.driverId,
    required this.orderId,
    required this.rating,
    this.reviewText,
    this.customerName,
    this.createdAt,
  });

  factory DriverRating.fromJson(Map<String, dynamic> json) {
    return DriverRating(
      id: json['id'],
      driverId: json['driverId'] ?? json['driver_id'],
      orderId: json['orderId'] ?? json['order_id'],
      rating: json['rating'],
      reviewText: json['reviewText'] ?? json['review_text'],
      customerName: json['customerName'] ?? json['customer_name'],
      createdAt: json['createdAt'] != null
          ? DateTime.parse(json['createdAt'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'driverId': driverId,
      'orderId': orderId,
      'rating': rating,
      'reviewText': reviewText,
    };
  }
}

class RatingsSummary {
  final List<FoodRating> foodRatings;
  final DriverRating? driverRating;
  final int orderId;

  RatingsSummary({
    required this.foodRatings,
    this.driverRating,
    required this.orderId,
  });

  factory RatingsSummary.fromJson(Map<String, dynamic> json) {
    return RatingsSummary(
      orderId: json['orderId'],
      foodRatings: (json['foodRatings'] as List)
          .map((rating) => FoodRating.fromJson(rating))
          .toList(),
      driverRating: json['driverRating'] != null
          ? DriverRating.fromJson(json['driverRating'])
          : null,
    );
  }
}

class RatableItem {
  final int menuItemId;
  final String name;
  final String? description;
  final String? imageUrl;
  final int quantity;
  final double unitPrice;
  final bool alreadyRated;

  RatableItem({
    required this.menuItemId,
    required this.name,
    this.description,
    this.imageUrl,
    required this.quantity,
    required this.unitPrice,
    required this.alreadyRated,
  });

  factory RatableItem.fromJson(Map<String, dynamic> json) {
    return RatableItem(
      menuItemId: json['menuItemId'],
      name: json['name'],
      description: json['description'],
      imageUrl: json['imageUrl'],
      quantity: json['quantity'],
      unitPrice: (json['unitPrice'] as num).toDouble(),
      alreadyRated: json['alreadyRated'] ?? false,
    );
  }
}

class RatableDriver {
  final int driverId;
  final String driverName;
  final bool alreadyRated;

  RatableDriver({
    required this.driverId,
    required this.driverName,
    required this.alreadyRated,
  });

  factory RatableDriver.fromJson(Map<String, dynamic> json) {
    return RatableDriver(
      driverId: json['driverId'],
      driverName: json['driverName'],
      alreadyRated: json['alreadyRated'] ?? false,
    );
  }
}

class AvailableRatings {
  final int orderId;
  final List<RatableItem> menuItems;
  final RatableDriver? driver;

  AvailableRatings({
    required this.orderId,
    required this.menuItems,
    this.driver,
  });

  factory AvailableRatings.fromJson(Map<String, dynamic> json) {
    return AvailableRatings(
      orderId: json['orderId'],
      menuItems: (json['menuItems'] as List)
          .map((item) => RatableItem.fromJson(item))
          .toList(),
      driver: json['driver'] != null
          ? RatableDriver.fromJson(json['driver'])
          : null,
    );
  }
}

class MenuItemRatings {
  final List<FoodRating> ratings;
  final double averageRating;
  final int totalRatings;
  final RatingPagination pagination;

  MenuItemRatings({
    required this.ratings,
    required this.averageRating,
    required this.totalRatings,
    required this.pagination,
  });

  factory MenuItemRatings.fromJson(Map<String, dynamic> json) {
    return MenuItemRatings(
      ratings: (json['ratings'] as List)
          .map((rating) => FoodRating.fromJson(rating))
          .toList(),
      averageRating: (json['averageRating'] as num).toDouble(),
      totalRatings: json['totalRatings'],
      pagination: RatingPagination.fromJson(json['pagination']),
    );
  }
}

class DriverRatings {
  final List<DriverRating> ratings;
  final double averageRating;
  final int totalRatings;
  final RatingPagination pagination;

  DriverRatings({
    required this.ratings,
    required this.averageRating,
    required this.totalRatings,
    required this.pagination,
  });

  factory DriverRatings.fromJson(Map<String, dynamic> json) {
    return DriverRatings(
      ratings: (json['ratings'] as List)
          .map((rating) => DriverRating.fromJson(rating))
          .toList(),
      averageRating: (json['averageRating'] as num).toDouble(),
      totalRatings: json['totalRatings'],
      pagination: RatingPagination.fromJson(json['pagination']),
    );
  }
}

class RatingPagination {
  final int page;
  final int limit;
  final int total;
  final int totalPages;

  RatingPagination({
    required this.page,
    required this.limit,
    required this.total,
    required this.totalPages,
  });

  factory RatingPagination.fromJson(Map<String, dynamic> json) {
    return RatingPagination(
      page: json['page'],
      limit: json['limit'],
      total: json['total'],
      totalPages: json['totalPages'],
    );
  }
}

class OrderRatingStatus {
  final bool hasFoodRatings;
  final bool hasDriverRating;
  final bool canRate;

  OrderRatingStatus({
    required this.hasFoodRatings,
    required this.hasDriverRating,
    required this.canRate,
  });

  factory OrderRatingStatus.fromJson(Map<String, dynamic> json) {
    return OrderRatingStatus(
      hasFoodRatings: json['hasFoodRatings'] ?? false,
      hasDriverRating: json['hasDriverRating'] ?? false,
      canRate: json['canRate'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'hasFoodRatings': hasFoodRatings,
      'hasDriverRating': hasDriverRating,
      'canRate': canRate,
    };
  }
}
