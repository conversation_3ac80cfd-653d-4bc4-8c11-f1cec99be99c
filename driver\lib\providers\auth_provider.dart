import 'package:flutter/foundation.dart';
import '../models/user.dart';
import '../services/api_service.dart';
import '../services/location_service.dart';

class AuthProvider with ChangeNotifier {
  User? _user;
  DriverProfile? _profile;
  bool _isLoading = false;
  bool _isAuthenticated = false;
  String? _error;

  User? get user => _user;
  DriverProfile? get profile => _profile;
  bool get isLoading => _isLoading;
  bool get isAuthenticated => _isAuthenticated;
  String? get error => _error;
  bool get isProfileCompleted => _profile?.isProfileCompleted ?? false;

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String? error) {
    _error = error;
    notifyListeners();
  }

  Future<void> checkAuthStatus() async {
    _setLoading(true);
    try {
      final token = await ApiService.getToken();
      if (token != null) {
        await loadCurrentUser();
      }
    } catch (e) {
      print('Auth check failed: $e');
      _isAuthenticated = false;
    }
    _setLoading(false);
  }

  Future<void> loadCurrentUser() async {
    try {
      final userData = await ApiService.getCurrentUser();
      _user = User.fromJson(userData['user']);
      _isAuthenticated = true;
      
      // Load driver profile if exists
      if (userData['profile'] != null) {
        _profile = DriverProfile.fromJson(userData['profile']);
      }
      
      _setError(null);
      notifyListeners();
    } catch (e) {
      print('Failed to load current user: $e');
      _setError('Failed to load user data');
      _isAuthenticated = false;
      notifyListeners();
    }
  }

  Future<bool> register({
    required String email,
    required String password,
  }) async {
    _setLoading(true);
    _setError(null);

    try {
      final response = await ApiService.register(
        email: email,
        password: password,
      );

      _user = User.fromJson(response['user']);
      _isAuthenticated = true;
      
      // Profile will be null for new registrations
      _profile = null;
      
      _setLoading(false);
      notifyListeners();
      return true;
    } catch (e) {
      _setError(e.toString().replaceFirst('Exception: ', ''));
      _setLoading(false);
      return false;
    }
  }

  Future<bool> login({
    required String email,
    required String password,
  }) async {
    _setLoading(true);
    _setError(null);

    try {
      final response = await ApiService.login(
        email: email,
        password: password,
      );

      _user = User.fromJson(response['user']);
      _isAuthenticated = true;
      
      // Load profile if exists
      if (response['profile'] != null) {
        _profile = DriverProfile.fromJson(response['profile']);
      }
      
      _setLoading(false);
      notifyListeners();
      return true;
    } catch (e) {
      _setError(e.toString().replaceFirst('Exception: ', ''));
      _setLoading(false);
      return false;
    }
  }

  Future<bool> completeProfile({
    required String fullName,
    required String phone,
    required String licenseNumber,
    required String vehicleType,
    required String vehiclePlate,
  }) async {
    _setLoading(true);
    _setError(null);

    try {
      final response = await ApiService.completeProfile(
        fullName: fullName,
        phone: phone,
        licenseNumber: licenseNumber,
        vehicleType: vehicleType,
        vehiclePlate: vehiclePlate,
      );

      _profile = DriverProfile.fromJson(response['profile']);
      
      // Reload current user to ensure we have the latest data
      await loadCurrentUser();
      
      _setLoading(false);
      notifyListeners();
      return true;
    } catch (e) {
      _setError(e.toString().replaceFirst('Exception: ', ''));
      _setLoading(false);
      return false;
    }
  }

  Future<void> updateAvailability(bool isAvailable) async {
    try {
      final updatedProfile = await ApiService.updateAvailability(
        isAvailable: isAvailable,
      );
      _profile = updatedProfile;
      notifyListeners();
    } catch (e) {
      _setError(e.toString().replaceFirst('Exception: ', ''));
    }
  }

  Future<void> updateLocation(double latitude, double longitude) async {
    try {
      final updatedProfile = await ApiService.updateLocation(
        latitude: latitude,
        longitude: longitude,
      );
      _profile = updatedProfile;
      notifyListeners();
    } catch (e) {
      print('Failed to update location: $e');
      // Don't show error to user for location updates
    }
  }

  Future<void> refreshProfile() async {
    try {
      _profile = await ApiService.getDriverProfile();
      notifyListeners();
    } catch (e) {
      print('Failed to refresh profile: $e');
    }
  }

  Future<void> logout() async {
    _setLoading(true);
    
    try {
      await ApiService.logout();
    } catch (e) {
      print('Logout error: $e');
    }
    
    _user = null;
    _profile = null;
    _isAuthenticated = false;
    _error = null;
    
    _setLoading(false);
    notifyListeners();
  }

  void clearError() {
    _setError(null);
  }

  Future<void> requestLocationPermissions() async {
    try {
      await LocationService.requestLocationPermission();
    } catch (e) {
      print('Failed to request location permissions: $e');
    }
  }
}
