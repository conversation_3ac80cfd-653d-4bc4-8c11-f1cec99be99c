const admin = require('firebase-admin');
const path = require('path');

// Initialize Firebase Admin SDK
let firebaseApp;

try {
  // Try to use service account file first
  const serviceAccountPath = path.join(__dirname, 'firebase-service-account.json');
  
  try {
    const serviceAccount = require(serviceAccountPath);
    firebaseApp = admin.initializeApp({
      credential: admin.credential.cert(serviceAccount),
      storageBucket: process.env.FIREBASE_STORAGE_BUCKET || 'brsima.appspot.com'
    });
    console.log('✅ Firebase initialized with service account file');
  } catch (fileError) {
    // Fallback to environment variables
    if (process.env.FIREBASE_PRIVATE_KEY) {
      const serviceAccount = {
        type: "service_account",
        project_id: process.env.FIREBASE_PROJECT_ID,
        private_key_id: process.env.FIREBASE_PRIVATE_KEY_ID,
        private_key: process.env.FIREBASE_PRIVATE_KEY.replace(/\\n/g, '\n'),
        client_email: process.env.FIREBASE_CLIENT_EMAIL,
        client_id: process.env.FIREBASE_CLIENT_ID,
        auth_uri: process.env.FIREBASE_AUTH_URI || "https://accounts.google.com/o/oauth2/auth",
        token_uri: process.env.FIREBASE_TOKEN_URI || "https://oauth2.googleapis.com/token"
      };

      firebaseApp = admin.initializeApp({
        credential: admin.credential.cert(serviceAccount),
        storageBucket: process.env.FIREBASE_STORAGE_BUCKET || 'brsima.appspot.com'
      });
      console.log('✅ Firebase initialized with environment variables');
    } else {
      throw new Error('Firebase configuration not found. Please provide either service account file or environment variables.');
    }
  }
} catch (error) {
  console.error('❌ Firebase initialization failed:', error.message);
  console.log('📝 Please ensure you have either:');
  console.log('   1. firebase-service-account.json file in config/ directory, OR');
  console.log('   2. Firebase environment variables set in .env file');
  process.exit(1);
}

// Get Firebase services
const storage = admin.storage();
const auth = admin.auth();
const firestore = admin.firestore();

// Storage bucket reference
const bucket = storage.bucket();

/**
 * Upload file to Firebase Storage
 * @param {Buffer} fileBuffer - File buffer
 * @param {string} fileName - File name with path
 * @param {string} contentType - MIME type
 * @returns {Promise<string>} - Public URL of uploaded file
 */
async function uploadFile(fileBuffer, fileName, contentType) {
  try {
    const file = bucket.file(fileName);
    
    await file.save(fileBuffer, {
      metadata: {
        contentType: contentType,
      },
      public: true,
      validation: 'md5'
    });

    // Make the file publicly accessible
    await file.makePublic();

    // Return the public URL
    return `https://storage.googleapis.com/${bucket.name}/${fileName}`;
  } catch (error) {
    console.error('Error uploading file to Firebase Storage:', error);
    throw error;
  }
}

/**
 * Delete file from Firebase Storage
 * @param {string} fileName - File name with path
 * @returns {Promise<void>}
 */
async function deleteFile(fileName) {
  try {
    const file = bucket.file(fileName);
    await file.delete();
    console.log(`File ${fileName} deleted successfully`);
  } catch (error) {
    console.error('Error deleting file from Firebase Storage:', error);
    throw error;
  }
}

/**
 * Generate signed URL for file upload
 * @param {string} fileName - File name with path
 * @param {string} contentType - MIME type
 * @returns {Promise<string>} - Signed URL for upload
 */
async function generateSignedUploadUrl(fileName, contentType) {
  try {
    const file = bucket.file(fileName);
    
    const [signedUrl] = await file.getSignedUrl({
      version: 'v4',
      action: 'write',
      expires: Date.now() + 15 * 60 * 1000, // 15 minutes
      contentType: contentType,
    });

    return signedUrl;
  } catch (error) {
    console.error('Error generating signed URL:', error);
    throw error;
  }
}

/**
 * Verify Firebase Auth token
 * @param {string} idToken - Firebase ID token
 * @returns {Promise<Object>} - Decoded token
 */
async function verifyAuthToken(idToken) {
  try {
    const decodedToken = await auth.verifyIdToken(idToken);
    return decodedToken;
  } catch (error) {
    console.error('Error verifying auth token:', error);
    throw error;
  }
}

/**
 * Create custom token for user
 * @param {string} uid - User ID
 * @param {Object} additionalClaims - Additional claims to include
 * @returns {Promise<string>} - Custom token
 */
async function createCustomToken(uid, additionalClaims = {}) {
  try {
    const customToken = await auth.createCustomToken(uid, additionalClaims);
    return customToken;
  } catch (error) {
    console.error('Error creating custom token:', error);
    throw error;
  }
}

module.exports = {
  admin,
  storage,
  auth,
  firestore,
  bucket,
  uploadFile,
  deleteFile,
  generateSignedUploadUrl,
  verifyAuthToken,
  createCustomToken
};
