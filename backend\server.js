const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
require('dotenv').config();

// Initialize Firebase (this will also validate configuration)
require('./config/firebase');

// Import database and routes
const { pool } = require('./config/database');
const authRoutes = require('./routes/auth');
const customerRoutes = require('./routes/customer');
const restaurantRoutes = require('./routes/restaurant');
const driverRoutes = require('./routes/driver');
const adminRoutes = require('./routes/admin');
const uploadRoutes = require('./routes/upload');
const ratingsRoutes = require('./routes/ratings');

const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(helmet());
app.use(cors());
app.use(morgan('combined'));
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Routes
app.get('/', (req, res) => {
  res.json({
    message: 'BRSIMA Backend Server',
    status: 'Running',
    timestamp: new Date().toISOString()
  });
});

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    status: 'OK',
    uptime: process.uptime(),
    timestamp: new Date().toISOString()
  });
});

// API routes
app.use('/api/auth', authRoutes);
app.use('/api/customer', customerRoutes);
app.use('/api/restaurant', restaurantRoutes);
app.use('/api/driver', driverRoutes);
app.use('/api/admin', adminRoutes);
app.use('/api/upload', uploadRoutes);
app.use('/api/ratings', ratingsRoutes);

// API info endpoint
app.get('/api', (req, res) => {
  res.json({
    message: 'BRSIMA API v1.0',
    availableEndpoints: [
      'POST /api/auth/register',
      'POST /api/auth/login',
      'GET /api/auth/me',
      'POST /api/customer/profile',
      'GET /api/customer/profile',
      'PATCH /api/customer/location',
      'POST /api/driver/profile',
      'GET /api/driver/profile',
      'PATCH /api/driver/availability',
      'PATCH /api/driver/location',
      'GET /api/driver/available-orders',
      'POST /api/driver/accept-order/:orderId',
      'GET /api/driver/my-orders',
      'PATCH /api/driver/complete-order/:orderId',
      'GET /api/driver/stats'
    ],
    documentation: 'https://github.com/your-repo/brsima-api'
  });
});

// Error handling middleware
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({
    error: 'Something went wrong!',
    message: err.message
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    error: 'Route not found',
    message: `Cannot ${req.method} ${req.originalUrl}`
  });
});

app.listen(PORT, '0.0.0.0', () => {
  console.log(`🚀 BRSIMA Backend Server running on port ${PORT}`);
  console.log(`📊 Health check: http://localhost:${PORT}/health`);
  console.log(`📱 Android emulator: http://********:${PORT}/health`);
});
