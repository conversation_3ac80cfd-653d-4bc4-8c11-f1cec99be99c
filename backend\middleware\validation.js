const Joi = require('joi');

const validateRequest = (schema) => {
  return (req, res, next) => {
    const { error } = schema.validate(req.body);
    
    if (error) {
      return res.status(400).json({
        error: 'Validation failed',
        details: error.details.map(detail => ({
          field: detail.path.join('.'),
          message: detail.message
        }))
      });
    }
    
    next();
  };
};

// Validation schemas
const schemas = {
  register: Joi.object({
    email: Joi.string().email().required(),
    password: Joi.string().min(6).required(),
    userType: Joi.string().valid('customer', 'restaurant', 'driver').default('customer')
  }),
  
  login: Joi.object({
    email: Joi.string().email().required(),
    password: Joi.string().required()
  }),
  
  customerProfile: Joi.object({
    fullName: Joi.string().min(2).max(100).required(),
    phone: Joi.string().pattern(/^[+]?[0-9]{10,15}$/).required(),
    address: Joi.string().min(5).max(500).required(),
    latitude: Joi.number().min(-90).max(90).required(),
    longitude: Joi.number().min(-180).max(180).required()
  }),
  
  driverProfile: Joi.object({
    fullName: Joi.string().min(2).max(100).required(),
    phone: Joi.string().pattern(/^[+]?[1-9][\d\s\-\(\)]{7,15}$/).required(),
    licenseNumber: Joi.string().min(5).max(50).required(),
    vehicleType: Joi.string().valid('motorcycle', 'car', 'bicycle').required(),
    vehiclePlate: Joi.string().min(3).max(20).required()
  }),
  
  restaurantProfile: Joi.object({
    restaurantName: Joi.string().min(2).max(100).required(),
    description: Joi.string().min(10).max(1000).required(),
    phone: Joi.string().pattern(/^[+]?[0-9]{10,15}$/).required(),
    address: Joi.string().min(5).max(500).required(),
    latitude: Joi.number().min(-90).max(90).required(),
    longitude: Joi.number().min(-180).max(180).required(),
    cuisineType: Joi.string().min(2).max(50).required()
  })
};

// Middleware functions for each schema
const validateRegister = validateRequest(schemas.register);
const validateLogin = validateRequest(schemas.login);
const validateCustomerProfile = validateRequest(schemas.customerProfile);
const validateDriverProfile = validateRequest(schemas.driverProfile);
const validateRestaurantProfile = validateRequest(schemas.restaurantProfile);

module.exports = {
  validateRequest,
  schemas,
  validateRegister,
  validateLogin,
  validateCustomerProfile,
  validateDriverProfile,
  validateRestaurantProfile
};
