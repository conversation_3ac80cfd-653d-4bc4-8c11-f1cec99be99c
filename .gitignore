# ================================
# Node.js
# ================================
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
package-lock.json
.env
.env.*
.DS_Store

# Logs
logs/
*.log
*.log.*

# Build output
dist/
build/
coverage/
.cache/
.tmp/
out/

# Editor/IDE
.vscode/
.idea/
*.sublime-project
*.sublime-workspace

# OS files
Thumbs.db
.DS_Store

# Others
*.swp
*.bak
*.tmp


# ================================
# Flutter / Dart
# ================================
# Build and cache
.dart_tool/
.flutter-plugins
.flutter-plugins-dependencies
.packages
.pub-cache/
.pub/
build/
.melos_tool/

# IDE files
android/.gradle/
android/.idea/
*.iml

# iOS/macOS
ios/Pods/
ios/Runner.xcworkspace/
ios/Flutter/Flutter.framework
ios/Flutter/Flutter.podspec
macos/Pods/
macos/Runner.xcworkspace/

# Platform-specific
*.ipa
*.apk
*.app

# Firebase/Google services
android/app/google-services.json
ios/Runner/GoogleService-Info.plist
backend/config/firebase-service-account.json

# Misc
*.lock
