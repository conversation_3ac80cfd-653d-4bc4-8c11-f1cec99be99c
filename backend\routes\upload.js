const express = require('express');
const multer = require('multer');
const { uploadFile, deleteFile } = require('../config/firebase');
const { authenticateToken, requireRole } = require('../middleware/auth');
const path = require('path');

const router = express.Router();

// Configure multer for memory storage
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 5 * 1024 * 1024, // 5MB limit
  },
  fileFilter: (req, file, cb) => {
    // Check file type
    const allowedTypes = /jpeg|jpg|png|gif|webp/;
    const extname = allowedTypes.test(path.extname(file.originalname).toLowerCase());
    const mimetype = allowedTypes.test(file.mimetype);

    if (mimetype && extname) {
      return cb(null, true);
    } else {
      cb(new Error('Only image files are allowed (jpeg, jpg, png, gif, webp)'));
    }
  }
});

/**
 * Generate unique filename with timestamp
 */
function generateFileName(originalName, prefix = '') {
  const timestamp = Date.now();
  const random = Math.random().toString(36).substring(2, 15);
  const ext = path.extname(originalName);
  return `${prefix}${timestamp}_${random}${ext}`;
}

/**
 * Upload restaurant image
 * POST /api/upload/restaurant/:restaurantId/image
 */
router.post('/restaurant/:restaurantId/image', 
  authenticateToken, 
  requireRole(['restaurant', 'admin']), 
  upload.single('image'), 
  async (req, res) => {
    try {
      if (!req.file) {
        return res.status(400).json({ error: 'No image file provided' });
      }

      const { restaurantId } = req.params;
      const { type = 'profile' } = req.body; // profile, banner, gallery

      // Check if user owns this restaurant (unless admin)
      if (req.user.userType === 'restaurant') {
        const { query } = require('../config/database');
        const result = await query(
          'SELECT id FROM restaurant_profiles WHERE id = $1 AND user_id = $2',
          [restaurantId, req.user.id]
        );
        
        if (result.rows.length === 0) {
          return res.status(403).json({ error: 'Access denied to this restaurant' });
        }
      }

      // Generate filename and upload path
      const fileName = generateFileName(req.file.originalname, `restaurant_${restaurantId}_${type}_`);
      const uploadPath = `restaurants/${restaurantId}/${type}/${fileName}`;

      // Upload to Firebase Storage
      const imageUrl = await uploadFile(req.file.buffer, uploadPath, req.file.mimetype);

      res.json({
        success: true,
        imageUrl,
        fileName,
        uploadPath
      });

    } catch (error) {
      console.error('Restaurant image upload error:', error);
      res.status(500).json({ 
        error: 'Failed to upload image',
        message: error.message 
      });
    }
  }
);

/**
 * Upload menu item image
 * POST /api/upload/menu-item/:restaurantId/image
 */
router.post('/menu-item/:restaurantId/image', 
  authenticateToken, 
  requireRole(['restaurant', 'admin']), 
  upload.single('image'), 
  async (req, res) => {
    try {
      if (!req.file) {
        return res.status(400).json({ error: 'No image file provided' });
      }

      const { restaurantId } = req.params;

      // Check if user owns this restaurant (unless admin)
      if (req.user.userType === 'restaurant') {
        const { query } = require('../config/database');
        const result = await query(
          'SELECT id FROM restaurant_profiles WHERE id = $1 AND user_id = $2',
          [restaurantId, req.user.id]
        );
        
        if (result.rows.length === 0) {
          return res.status(403).json({ error: 'Access denied to this restaurant' });
        }
      }

      // Generate filename and upload path
      const fileName = generateFileName(req.file.originalname, `menu_item_`);
      const uploadPath = `menu-items/${restaurantId}/${fileName}`;

      // Upload to Firebase Storage
      const imageUrl = await uploadFile(req.file.buffer, uploadPath, req.file.mimetype);

      res.json({
        success: true,
        imageUrl,
        fileName,
        uploadPath
      });

    } catch (error) {
      console.error('Menu item image upload error:', error);
      res.status(500).json({ 
        error: 'Failed to upload image',
        message: error.message 
      });
    }
  }
);

/**
 * Upload admin image (general purpose)
 * POST /api/upload/admin/image
 */
router.post('/admin/image', 
  authenticateToken, 
  requireRole(['admin']), 
  upload.single('image'), 
  async (req, res) => {
    try {
      if (!req.file) {
        return res.status(400).json({ error: 'No image file provided' });
      }

      const { category = 'general' } = req.body;

      // Generate filename and upload path
      const fileName = generateFileName(req.file.originalname, `admin_${category}_`);
      const uploadPath = `admin/${category}/${fileName}`;

      // Upload to Firebase Storage
      const imageUrl = await uploadFile(req.file.buffer, uploadPath, req.file.mimetype);

      res.json({
        success: true,
        imageUrl,
        fileName,
        uploadPath
      });

    } catch (error) {
      console.error('Admin image upload error:', error);
      res.status(500).json({ 
        error: 'Failed to upload image',
        message: error.message 
      });
    }
  }
);

/**
 * Delete image
 * DELETE /api/upload/image
 */
router.delete('/image', 
  authenticateToken, 
  requireRole(['restaurant', 'admin']), 
  async (req, res) => {
    try {
      const { uploadPath } = req.body;

      if (!uploadPath) {
        return res.status(400).json({ error: 'Upload path is required' });
      }

      // Extract restaurant ID from path for permission check
      const pathParts = uploadPath.split('/');
      if (pathParts[0] === 'restaurants' || pathParts[0] === 'menu-items') {
        const restaurantId = pathParts[1];
        
        // Check if user owns this restaurant (unless admin)
        if (req.user.userType === 'restaurant') {
          const { query } = require('../config/database');
          const result = await query(
            'SELECT id FROM restaurant_profiles WHERE id = $1 AND user_id = $2',
            [restaurantId, req.user.id]
          );
          
          if (result.rows.length === 0) {
            return res.status(403).json({ error: 'Access denied to delete this image' });
          }
        }
      } else if (pathParts[0] === 'admin' && req.user.userType !== 'admin') {
        return res.status(403).json({ error: 'Only admins can delete admin images' });
      }

      // Delete from Firebase Storage
      await deleteFile(uploadPath);

      res.json({
        success: true,
        message: 'Image deleted successfully'
      });

    } catch (error) {
      console.error('Image deletion error:', error);
      res.status(500).json({ 
        error: 'Failed to delete image',
        message: error.message 
      });
    }
  }
);

/**
 * Get upload info (for debugging)
 * GET /api/upload/info
 */
router.get('/info', authenticateToken, (req, res) => {
  res.json({
    maxFileSize: '5MB',
    allowedTypes: ['jpeg', 'jpg', 'png', 'gif', 'webp'],
    uploadPaths: {
      restaurant: '/api/upload/restaurant/:restaurantId/image',
      menuItem: '/api/upload/menu-item/:restaurantId/image',
      admin: '/api/upload/admin/image'
    },
    deleteEndpoint: '/api/upload/image'
  });
});

module.exports = router;
