# Firebase Testing Guide for BRSIMA

This guide provides comprehensive testing procedures for the Firebase integration in your BRSIMA project.

## 🧪 Testing Overview

### Testing Levels:
1. **Unit Tests**: Individual Firebase service functions
2. **Integration Tests**: End-to-end image upload flows
3. **Security Tests**: Firebase Storage security rules
4. **Performance Tests**: Image upload/download performance
5. **Manual Tests**: User interface and experience

## 🔧 Setup Testing Environment

### 1. Install Firebase Emulator Suite
```bash
npm install -g firebase-tools
firebase login
```

### 2. Initialize Emulators
```bash
# In your project root
firebase init emulators
```

Select:
- ☑️ Storage Emulator
- ☑️ Authentication Emulator (optional)

### 3. Start Emulators
```bash
firebase emulators:start
```

This will start:
- Storage Emulator: http://localhost:9199
- Emulator UI: http://localhost:4000

## 🧪 Unit Testing

### Backend Tests

Create `backend/tests/firebase.test.js`:
```javascript
const { uploadFile, deleteFile } = require('../config/firebase');
const fs = require('fs');
const path = require('path');

describe('Firebase Storage', () => {
  test('should upload file successfully', async () => {
    const testImagePath = path.join(__dirname, 'fixtures/test-image.jpg');
    const imageBuffer = fs.readFileSync(testImagePath);
    
    const result = await uploadFile(
      imageBuffer,
      'test/test-image.jpg',
      'image/jpeg'
    );
    
    expect(result).toContain('storage.googleapis.com');
  });

  test('should delete file successfully', async () => {
    await expect(deleteFile('test/test-image.jpg')).resolves.not.toThrow();
  });
});
```

### Flutter Tests

Create `test/firebase_service_test.dart` in each Flutter app:
```dart
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:firebase_storage/firebase_storage.dart';
import '../lib/services/firebase_service.dart';

class MockFirebaseStorage extends Mock implements FirebaseStorage {}
class MockReference extends Mock implements Reference {}
class MockUploadTask extends Mock implements UploadTask {}

void main() {
  group('FirebaseService', () {
    late MockFirebaseStorage mockStorage;
    late MockReference mockRef;
    late MockUploadTask mockUploadTask;

    setUp(() {
      mockStorage = MockFirebaseStorage();
      mockRef = MockReference();
      mockUploadTask = MockUploadTask();
    });

    test('should upload image successfully', () async {
      // Mock the Firebase Storage chain
      when(mockStorage.ref()).thenReturn(mockRef);
      when(mockRef.child(any)).thenReturn(mockRef);
      when(mockRef.putFile(any)).thenReturn(mockUploadTask);
      
      // Test your upload logic here
    });
  });
}
```

## 🔗 Integration Testing

### 1. Backend Integration Test

Create `backend/tests/integration/image-upload.test.js`:
```javascript
const request = require('supertest');
const app = require('../../server');
const path = require('path');

describe('Image Upload Integration', () => {
  let authToken;
  let restaurantId;

  beforeAll(async () => {
    // Setup test user and restaurant
    authToken = await getTestAuthToken();
    restaurantId = await createTestRestaurant();
  });

  test('should upload restaurant image via API', async () => {
    const imagePath = path.join(__dirname, '../fixtures/test-restaurant.jpg');
    
    const response = await request(app)
      .post(`/api/upload/restaurant/${restaurantId}/image`)
      .set('Authorization', `Bearer ${authToken}`)
      .attach('image', imagePath)
      .field('type', 'profile');

    expect(response.status).toBe(200);
    expect(response.body.success).toBe(true);
    expect(response.body.imageUrl).toContain('storage.googleapis.com');
  });

  test('should update restaurant image URL in database', async () => {
    const imageUrl = 'https://storage.googleapis.com/test-bucket/test-image.jpg';
    
    const response = await request(app)
      .patch(`/api/restaurant/${restaurantId}/image`)
      .set('Authorization', `Bearer ${authToken}`)
      .send({
        imageUrl: imageUrl,
        imageType: 'profile'
      });

    expect(response.status).toBe(200);
    expect(response.body.success).toBe(true);
  });
});
```

### 2. Flutter Integration Test

Create `integration_test/image_upload_test.dart`:
```dart
import 'package:flutter/services.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:restaurant/main.dart' as app;

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  group('Image Upload Integration Tests', () {
    testWidgets('should upload restaurant image', (WidgetTester tester) async {
      app.main();
      await tester.pumpAndSettle();

      // Navigate to image upload screen
      await tester.tap(find.text('Upload Image'));
      await tester.pumpAndSettle();

      // Simulate image selection
      // Note: This requires additional setup for file picking simulation
      
      // Verify upload success
      expect(find.text('Image uploaded successfully!'), findsOneWidget);
    });
  });
}
```

## 🔒 Security Rule Testing

### 1. Create Security Rule Tests

Create `test/storage.rules.test.js`:
```javascript
const firebase = require('@firebase/rules-unit-testing');
const fs = require('fs');

const PROJECT_ID = 'brsima-test';
const RULES_FILE = 'storage.rules';

describe('Storage Security Rules', () => {
  let testEnv;

  beforeAll(async () => {
    testEnv = await firebase.initializeTestEnvironment({
      projectId: PROJECT_ID,
      storage: {
        rules: fs.readFileSync(RULES_FILE, 'utf8'),
      },
    });
  });

  afterAll(async () => {
    await testEnv.cleanup();
  });

  test('should allow public read access to restaurant images', async () => {
    const unauthedStorage = testEnv.unauthenticatedContext().storage();
    
    await firebase.assertSucceeds(
      unauthedStorage.ref('restaurants/123/profile/image.jpg').getDownloadURL()
    );
  });

  test('should deny unauthorized write to restaurant images', async () => {
    const unauthedStorage = testEnv.unauthenticatedContext().storage();
    
    await firebase.assertFails(
      unauthedStorage.ref('restaurants/123/profile/image.jpg').put(Buffer.from('fake-image'))
    );
  });

  test('should allow restaurant owner to upload images', async () => {
    const authedStorage = testEnv.authenticatedContext('restaurant-user', {
      userType: 'restaurant',
      restaurantId: '123'
    }).storage();
    
    await firebase.assertSucceeds(
      authedStorage.ref('restaurants/123/profile/image.jpg').put(Buffer.from('fake-image'))
    );
  });

  test('should deny restaurant owner from uploading to other restaurants', async () => {
    const authedStorage = testEnv.authenticatedContext('restaurant-user', {
      userType: 'restaurant',
      restaurantId: '123'
    }).storage();
    
    await firebase.assertFails(
      authedStorage.ref('restaurants/456/profile/image.jpg').put(Buffer.from('fake-image'))
    );
  });
});
```

### 2. Run Security Tests
```bash
npm install --save-dev @firebase/rules-unit-testing
npm test -- storage.rules.test.js
```

## 📊 Performance Testing

### 1. Image Upload Performance

Create `backend/tests/performance/upload-performance.test.js`:
```javascript
const { performance } = require('perf_hooks');
const { uploadFile } = require('../../config/firebase');

describe('Upload Performance', () => {
  test('should upload 1MB image within 5 seconds', async () => {
    const imageBuffer = Buffer.alloc(1024 * 1024); // 1MB fake image
    
    const startTime = performance.now();
    
    await uploadFile(imageBuffer, 'performance-test/1mb-image.jpg', 'image/jpeg');
    
    const endTime = performance.now();
    const uploadTime = endTime - startTime;
    
    expect(uploadTime).toBeLessThan(5000); // 5 seconds
  });

  test('should handle concurrent uploads', async () => {
    const uploadPromises = [];
    
    for (let i = 0; i < 5; i++) {
      const imageBuffer = Buffer.alloc(100 * 1024); // 100KB
      uploadPromises.push(
        uploadFile(imageBuffer, `concurrent-test/image-${i}.jpg`, 'image/jpeg')
      );
    }
    
    const startTime = performance.now();
    await Promise.all(uploadPromises);
    const endTime = performance.now();
    
    expect(endTime - startTime).toBeLessThan(10000); // 10 seconds for 5 uploads
  });
});
```

## 🖱️ Manual Testing Checklist

### Restaurant App Testing:
- [ ] **Profile Image Upload**
  - [ ] Select image from gallery
  - [ ] Select image from camera
  - [ ] Upload progress indicator works
  - [ ] Success message appears
  - [ ] Image displays correctly after upload
  - [ ] Image URL is saved to database

- [ ] **Menu Item Image Upload**
  - [ ] Upload image during menu item creation
  - [ ] Upload image for existing menu item
  - [ ] Replace existing image
  - [ ] Delete image

- [ ] **Error Handling**
  - [ ] Large file rejection (>5MB)
  - [ ] Invalid file type rejection
  - [ ] Network error handling
  - [ ] Permission denied handling

### Customer App Testing:
- [ ] **Image Viewing**
  - [ ] Restaurant profile images load
  - [ ] Menu item images load
  - [ ] Placeholder shown for missing images
  - [ ] Images load quickly
  - [ ] Images display correctly on different screen sizes

### Driver App Testing:
- [ ] **Profile Image Upload**
  - [ ] Upload driver profile image
  - [ ] Upload vehicle image
  - [ ] Upload license image

### Backend API Testing:
- [ ] **Upload Endpoints**
  - [ ] POST /api/upload/restaurant/:id/image
  - [ ] POST /api/upload/menu-item/:id/image
  - [ ] POST /api/upload/admin/image
  - [ ] DELETE /api/upload/image

- [ ] **Authentication**
  - [ ] Unauthorized requests are rejected
  - [ ] Valid tokens are accepted
  - [ ] Role-based access works

## 🚀 Running Tests

### Backend Tests:
```bash
cd backend
npm test
```

### Flutter Tests:
```bash
cd customer
flutter test

cd driver
flutter test

cd restaurant
flutter test
```

### Integration Tests:
```bash
cd restaurant
flutter test integration_test/
```

### Security Rule Tests:
```bash
npm run test:rules
```

## 📋 Test Results Documentation

Create a test report template:

```markdown
# Firebase Integration Test Report

**Date**: [Date]
**Tester**: [Name]
**Environment**: [Development/Staging/Production]

## Test Results Summary
- Unit Tests: ✅ Passed / ❌ Failed
- Integration Tests: ✅ Passed / ❌ Failed  
- Security Tests: ✅ Passed / ❌ Failed
- Performance Tests: ✅ Passed / ❌ Failed
- Manual Tests: ✅ Passed / ❌ Failed

## Issues Found
1. [Issue description]
   - Severity: High/Medium/Low
   - Steps to reproduce: [Steps]
   - Expected result: [Expected]
   - Actual result: [Actual]

## Recommendations
- [Recommendation 1]
- [Recommendation 2]
```

## 🔧 Continuous Testing

### GitHub Actions Example:
```yaml
name: Firebase Tests
on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - uses: actions/setup-node@v2
        with:
          node-version: '16'
      
      - name: Install dependencies
        run: npm install
      
      - name: Start Firebase Emulators
        run: firebase emulators:exec --only storage "npm test"
        env:
          FIREBASE_TOKEN: ${{ secrets.FIREBASE_TOKEN }}
```

This comprehensive testing approach ensures your Firebase integration is robust, secure, and performant.
