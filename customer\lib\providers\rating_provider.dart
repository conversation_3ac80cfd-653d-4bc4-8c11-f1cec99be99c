import 'package:flutter/foundation.dart';
import '../models/rating.dart';
import '../services/rating_service.dart';

class RatingProvider with ChangeNotifier {
  bool _isLoading = false;
  String? _error;
  
  // Cache for order ratings
  final Map<int, RatingsSummary> _orderRatingsCache = {};
  final Map<int, AvailableRatings> _availableRatingsCache = {};
  final Map<int, MenuItemRatings> _menuItemRatingsCache = {};

  bool get isLoading => _isLoading;
  String? get error => _error;

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String? error) {
    _error = error;
    notifyListeners();
  }

  // Submit food rating
  Future<bool> submitFoodRating({
    required int menuItemId,
    required int orderId,
    required int rating,
    String? reviewText,
  }) async {
    try {
      _setLoading(true);
      _setError(null);

      await RatingService.submitFoodRating(
        menuItemId: menuItemId,
        orderId: orderId,
        rating: rating,
        reviewText: reviewText,
      );

      // Clear cache for this order to force refresh
      _orderRatingsCache.remove(orderId);
      _availableRatingsCache.remove(orderId);
      _menuItemRatingsCache.remove(menuItemId);

      _setLoading(false);
      return true;
    } catch (e) {
      _setError(e.toString().replaceFirst('Exception: ', ''));
      _setLoading(false);
      return false;
    }
  }

  // Submit driver rating
  Future<bool> submitDriverRating({
    required int driverId,
    required int orderId,
    required int rating,
    String? reviewText,
  }) async {
    try {
      _setLoading(true);
      _setError(null);

      await RatingService.submitDriverRating(
        driverId: driverId,
        orderId: orderId,
        rating: rating,
        reviewText: reviewText,
      );

      // Clear cache for this order to force refresh
      _orderRatingsCache.remove(orderId);
      _availableRatingsCache.remove(orderId);

      _setLoading(false);
      return true;
    } catch (e) {
      _setError(e.toString().replaceFirst('Exception: ', ''));
      _setLoading(false);
      return false;
    }
  }

  // Submit multiple food ratings
  Future<bool> submitMultipleFoodRatings(
    List<Map<String, dynamic>> ratings,
  ) async {
    try {
      _setLoading(true);
      _setError(null);

      final submittedRatings = await RatingService.submitMultipleFoodRatings(ratings);
      
      // Clear cache for affected orders and menu items
      for (final ratingData in ratings) {
        _orderRatingsCache.remove(ratingData['orderId']);
        _availableRatingsCache.remove(ratingData['orderId']);
        _menuItemRatingsCache.remove(ratingData['menuItemId']);
      }

      _setLoading(false);
      return submittedRatings.isNotEmpty;
    } catch (e) {
      _setError(e.toString().replaceFirst('Exception: ', ''));
      _setLoading(false);
      return false;
    }
  }

  // Get order ratings (with caching)
  Future<RatingsSummary?> getOrderRatings(int orderId, {bool forceRefresh = false}) async {
    if (!forceRefresh && _orderRatingsCache.containsKey(orderId)) {
      return _orderRatingsCache[orderId];
    }

    try {
      _setLoading(true);
      _setError(null);

      final ratings = await RatingService.getOrderRatings(orderId);
      _orderRatingsCache[orderId] = ratings;

      _setLoading(false);
      return ratings;
    } catch (e) {
      _setError(e.toString().replaceFirst('Exception: ', ''));
      _setLoading(false);
      return null;
    }
  }

  // Get available ratings (with caching)
  Future<AvailableRatings?> getAvailableRatings(int orderId, {bool forceRefresh = false}) async {
    if (!forceRefresh && _availableRatingsCache.containsKey(orderId)) {
      return _availableRatingsCache[orderId];
    }

    try {
      _setLoading(true);
      _setError(null);

      final availableRatings = await RatingService.getAvailableRatings(orderId);
      _availableRatingsCache[orderId] = availableRatings;

      _setLoading(false);
      return availableRatings;
    } catch (e) {
      _setError(e.toString().replaceFirst('Exception: ', ''));
      _setLoading(false);
      return null;
    }
  }

  // Get menu item ratings (with caching)
  Future<MenuItemRatings?> getMenuItemRatings(
    int menuItemId, {
    int page = 1,
    int limit = 10,
    bool forceRefresh = false,
  }) async {
    final cacheKey = menuItemId;
    if (!forceRefresh && _menuItemRatingsCache.containsKey(cacheKey)) {
      return _menuItemRatingsCache[cacheKey];
    }

    try {
      _setLoading(true);
      _setError(null);

      final ratings = await RatingService.getMenuItemRatings(
        menuItemId,
        page: page,
        limit: limit,
      );
      _menuItemRatingsCache[cacheKey] = ratings;

      _setLoading(false);
      return ratings;
    } catch (e) {
      _setError(e.toString().replaceFirst('Exception: ', ''));
      _setLoading(false);
      return null;
    }
  }

  // Clear all caches
  void clearCache() {
    _orderRatingsCache.clear();
    _availableRatingsCache.clear();
    _menuItemRatingsCache.clear();
    notifyListeners();
  }

  // Clear cache for specific order
  void clearOrderCache(int orderId) {
    _orderRatingsCache.remove(orderId);
    _availableRatingsCache.remove(orderId);
    notifyListeners();
  }

  // Clear cache for specific menu item
  void clearMenuItemCache(int menuItemId) {
    _menuItemRatingsCache.remove(menuItemId);
    notifyListeners();
  }

  // Check if order has been rated
  bool hasOrderBeenRated(int orderId) {
    final ratings = _orderRatingsCache[orderId];
    if (ratings == null) return false;
    
    return ratings.foodRatings.isNotEmpty || ratings.driverRating != null;
  }

  // Get cached rating status for order
  OrderRatingStatus? getCachedRatingStatus(int orderId) {
    final availableRatings = _availableRatingsCache[orderId];
    if (availableRatings == null) return null;

    final hasUnratedFood = availableRatings.menuItems.any((item) => !item.alreadyRated);
    final hasUnratedDriver = availableRatings.driver != null && !availableRatings.driver!.alreadyRated;

    return OrderRatingStatus(
      hasFoodRatings: availableRatings.menuItems.any((item) => item.alreadyRated),
      hasDriverRating: availableRatings.driver?.alreadyRated ?? false,
      canRate: hasUnratedFood || hasUnratedDriver,
    );
  }
}
