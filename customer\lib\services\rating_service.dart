import 'dart:convert';
import 'package:http/http.dart' as http;
import '../models/rating.dart';
import 'api_service.dart';

class RatingService {
  static String get baseUrl => ApiService.baseUrl;

  // Submit food rating
  static Future<FoodRating> submitFoodRating({
    required int menuItemId,
    required int orderId,
    required int rating,
    String? reviewText,
  }) async {
    try {
      final token = await ApiService.getToken();
      if (token == null) {
        throw Exception('Authentication token not found');
      }

      final response = await http.post(
        Uri.parse('$baseUrl/ratings/food'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
        body: jsonEncode({
          'menuItemId': menuItemId,
          'orderId': orderId,
          'rating': rating,
          'reviewText': reviewText,
        }),
      );

      if (response.statusCode == 201) {
        final data = jsonDecode(response.body);
        return FoodRating.fromJson(data['rating']);
      } else {
        final error = jsonDecode(response.body);
        throw Exception(error['error'] ?? 'Failed to submit food rating');
      }
    } catch (e) {
      throw Exception('Failed to submit food rating: $e');
    }
  }

  // Submit driver rating
  static Future<DriverRating> submitDriverRating({
    required int driverId,
    required int orderId,
    required int rating,
    String? reviewText,
  }) async {
    try {
      final token = await ApiService.getToken();
      if (token == null) {
        throw Exception('Authentication token not found');
      }

      final response = await http.post(
        Uri.parse('$baseUrl/ratings/driver'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
        body: jsonEncode({
          'driverId': driverId,
          'orderId': orderId,
          'rating': rating,
          'reviewText': reviewText,
        }),
      );

      if (response.statusCode == 201) {
        final data = jsonDecode(response.body);
        return DriverRating.fromJson(data['rating']);
      } else {
        final error = jsonDecode(response.body);
        throw Exception(error['error'] ?? 'Failed to submit driver rating');
      }
    } catch (e) {
      throw Exception('Failed to submit driver rating: $e');
    }
  }

  // Get customer's ratings for a specific order
  static Future<RatingsSummary> getOrderRatings(int orderId) async {
    try {
      final token = await ApiService.getToken();
      if (token == null) {
        throw Exception('Authentication token not found');
      }

      final response = await http.get(
        Uri.parse('$baseUrl/ratings/customer/order/$orderId'),
        headers: {'Authorization': 'Bearer $token'},
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return RatingsSummary.fromJson(data);
      } else {
        final error = jsonDecode(response.body);
        throw Exception(error['error'] ?? 'Failed to get order ratings');
      }
    } catch (e) {
      throw Exception('Failed to get order ratings: $e');
    }
  }

  // Get items available for rating from a completed order
  static Future<AvailableRatings> getAvailableRatings(int orderId) async {
    try {
      final token = await ApiService.getToken();
      if (token == null) {
        throw Exception('Authentication token not found');
      }

      final response = await http.get(
        Uri.parse('$baseUrl/ratings/customer/order/$orderId/available'),
        headers: {'Authorization': 'Bearer $token'},
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return AvailableRatings.fromJson(data);
      } else {
        final error = jsonDecode(response.body);
        throw Exception(error['error'] ?? 'Failed to get available ratings');
      }
    } catch (e) {
      throw Exception('Failed to get available ratings: $e');
    }
  }

  // Get food ratings for a menu item
  static Future<MenuItemRatings> getMenuItemRatings(
    int menuItemId, {
    int page = 1,
    int limit = 10,
  }) async {
    try {
      final response = await http.get(
        Uri.parse(
          '$baseUrl/ratings/food/menu-item/$menuItemId?page=$page&limit=$limit',
        ),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return MenuItemRatings.fromJson(data);
      } else {
        final error = jsonDecode(response.body);
        throw Exception(error['error'] ?? 'Failed to get menu item ratings');
      }
    } catch (e) {
      throw Exception('Failed to get menu item ratings: $e');
    }
  }

  // Get driver ratings
  static Future<DriverRatings> getDriverRatings(
    int driverId, {
    int page = 1,
    int limit = 10,
  }) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/ratings/driver/$driverId?page=$page&limit=$limit'),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return DriverRatings.fromJson(data);
      } else {
        final error = jsonDecode(response.body);
        throw Exception(error['error'] ?? 'Failed to get driver ratings');
      }
    } catch (e) {
      throw Exception('Failed to get driver ratings: $e');
    }
  }

  // Submit multiple food ratings for an order
  static Future<List<FoodRating>> submitMultipleFoodRatings(
    List<Map<String, dynamic>> ratings,
  ) async {
    final List<FoodRating> submittedRatings = [];

    for (final ratingData in ratings) {
      try {
        final rating = await submitFoodRating(
          menuItemId: ratingData['menuItemId'],
          orderId: ratingData['orderId'],
          rating: ratingData['rating'],
          reviewText: ratingData['reviewText'],
        );
        submittedRatings.add(rating);
      } catch (e) {
        // Continue with other ratings even if one fails
        print(
          'Failed to submit rating for menu item ${ratingData['menuItemId']}: $e',
        );
      }
    }

    return submittedRatings;
  }
}
