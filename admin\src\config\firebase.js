import { initializeApp } from 'firebase/app';
import { getStorage } from 'firebase/storage';
import { getAuth } from 'firebase/auth';

// Firebase configuration
const firebaseConfig = {
  apiKey: "AIzaSyDdYQ-PNUxbgiyg-6wqnBGxVRJZ1AfTPp4",
  authDomain: "brsima-30138.firebaseapp.com",
  projectId: "brsima-30138",
  storageBucket: "brsima-30138.firebasestorage.app",
  messagingSenderId: "120480442939",
  appId: "1:120480442939:web:1aee7dee09a8f4fd20ca1e" // Using same as Android for now
};

// Initialize Firebase
const app = initializeApp(firebaseConfig, 'brsima-admin');

// Initialize Firebase services
export const storage = getStorage(app);
export const auth = getAuth(app);

export default app;
