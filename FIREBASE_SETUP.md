# Firebase Setup Guide for BRSIMA

This guide will help you set up Firebase for your BRSIMA project with all Flutter apps and the Node.js backend.

## Prerequisites

1. **Firebase CLI**: Install the Firebase CLI globally
   ```bash
   npm install -g firebase-tools
   ```

2. **Firebase Account**: Make sure you have access to your "brsima" Firebase project

## Step 1: Firebase Project Configuration

### 1.1 Login to Firebase CLI
```bash
firebase login
```

### 1.2 Initialize Firebase in your project
```bash
# Run this from the root directory of your project
firebase init
```

**Select the following options:**
- ☑️ Storage: Configure security rules and optional emulator for Cloud Storage
- Choose "Use an existing project" and select your "brsima" project
- Accept default storage rules file (storage.rules)
- Accept default firebase.json configuration

### 1.3 Generate Configuration Files

You'll need to generate configuration files for each platform:

#### For Android Apps:
```bash
# This will create google-services.json files
firebase apps:sdkconfig android
```

#### For iOS Apps:
```bash
# This will create GoogleService-Info.plist files
firebase apps:sdkconfig ios
```

#### For Web/Admin Panel:
```bash
# This will show web configuration
firebase apps:sdkconfig web
```

## Step 2: Manual Configuration Steps

### 2.1 Create Firebase Apps in Console

Go to [Firebase Console](https://console.firebase.google.com) → Your "brsima" project:

1. **Add Android Apps** (create 3 separate apps):
   - Customer App: `com.brsima.customer`
   - Driver App: `com.brsima.driver`  
   - Restaurant App: `com.brsima.restaurant`

2. **Add iOS Apps** (create 3 separate apps):
   - Customer App: `com.brsima.customer`
   - Driver App: `com.brsima.driver`
   - Restaurant App: `com.brsima.restaurant`

3. **Add Web App**:
   - Admin Panel: `brsima-admin`

### 2.2 Download Configuration Files

After creating each app, download:
- `google-services.json` for each Android app
- `GoogleService-Info.plist` for each iOS app
- Web config object for the admin panel

## Step 3: Enable Firebase Services

In Firebase Console, enable these services:

### 3.1 Authentication
- Go to Authentication → Sign-in method
- Enable "Email/Password" provider
- Configure authorized domains if needed

### 3.2 Cloud Storage
- Go to Storage → Get started
- Choose production mode
- Select your preferred location (same as your users)

### 3.3 Cloud Firestore (Optional - for future use)
- Go to Firestore Database → Create database
- Start in production mode
- Choose same location as Storage

## Step 4: Environment Variables

Create these environment files:

### 4.1 Backend Environment (.env)
Add these to your `backend/.env` file:
```env
# Existing variables...

# Firebase Configuration
FIREBASE_PROJECT_ID=brsima
FIREBASE_STORAGE_BUCKET=brsima.appspot.com
FIREBASE_PRIVATE_KEY_ID=your_private_key_id
FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nyour_private_key\n-----END PRIVATE KEY-----\n"
FIREBASE_CLIENT_EMAIL=<EMAIL>
FIREBASE_CLIENT_ID=your_client_id
FIREBASE_AUTH_URI=https://accounts.google.com/o/oauth2/auth
FIREBASE_TOKEN_URI=https://oauth2.googleapis.com/token
```

### 4.2 Admin Panel Environment (.env)
Create `admin/.env`:
```env
REACT_APP_API_URL=http://localhost:3000/api

# Firebase Web Config
REACT_APP_FIREBASE_API_KEY=your_api_key
REACT_APP_FIREBASE_AUTH_DOMAIN=brsima.firebaseapp.com
REACT_APP_FIREBASE_PROJECT_ID=brsima
REACT_APP_FIREBASE_STORAGE_BUCKET=brsima.appspot.com
REACT_APP_FIREBASE_MESSAGING_SENDER_ID=your_sender_id
REACT_APP_FIREBASE_APP_ID=your_app_id
```

## Step 5: Service Account Key

1. Go to Firebase Console → Project Settings → Service Accounts
2. Click "Generate new private key"
3. Save the JSON file as `backend/config/firebase-service-account.json`
4. Add this file to your `.gitignore`

## Next Steps

After completing this setup:

1. ✅ Run the automated integration scripts (provided separately)
2. ✅ Test Firebase connectivity in each app
3. ✅ Deploy storage security rules
4. ✅ Test image upload functionality

## Security Notes

- Never commit service account keys to version control
- Use environment variables for all sensitive configuration
- Test security rules thoroughly before production
- Consider using Firebase App Check for additional security

## Step 6: Testing Firebase Integration

### 6.1 Test Backend Firebase Connection
```bash
cd backend
npm start
```
Check console for "✅ Firebase initialized successfully" message.

### 6.2 Test Flutter Apps
```bash
# Test Customer app
cd customer
flutter run

# Test Driver app
cd driver
flutter run

# Test Restaurant app
cd restaurant
flutter run
```

### 6.3 Test Image Upload
1. Use the image upload widgets in the Flutter apps
2. Check Firebase Storage console for uploaded files
3. Verify database updates in your PostgreSQL database

## Step 7: Deploy Security Rules

```bash
firebase deploy --only storage
```

## Troubleshooting

### Common Issues:

#### 1. Build Errors
- **Android**: Ensure `google-services.json` is in `android/app/` directory
- **iOS**: Ensure `GoogleService-Info.plist` is in `ios/Runner/` directory
- **Flutter**: Run `flutter clean && flutter pub get`

#### 2. Permission Denied
- Check Firebase Storage security rules
- Verify user authentication tokens include correct claims
- Test with Firebase emulator first

#### 3. Network Errors
- Verify Firebase project ID in configuration
- Check internet connectivity
- For Android emulator, use `********` instead of `localhost`

#### 4. Image Upload Failures
- Check file size (max 5MB)
- Verify file type (jpg, png, gif, webp only)
- Check Firebase Storage quotas

#### 5. Backend Integration Issues
- Verify service account key is correctly placed
- Check environment variables are set
- Ensure Firebase Admin SDK is properly initialized

### Getting Help:
- Check Firebase Console for error logs
- Use Firebase emulator for local testing: `firebase emulators:start`
- Check browser developer tools for web-related issues
- Refer to Firebase documentation for platform-specific issues

### Useful Commands:
```bash
# Check Firebase project status
firebase projects:list

# Test security rules
firebase emulators:start --only storage

# View Firebase logs
firebase functions:log

# Deploy only storage rules
firebase deploy --only storage
```
