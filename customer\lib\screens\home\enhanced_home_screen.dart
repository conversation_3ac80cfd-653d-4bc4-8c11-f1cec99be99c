import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/auth_provider.dart';
import '../../providers/restaurant_provider.dart';
import '../../models/restaurant.dart';
import '../../shared/app_colors.dart';
import '../../shared/app_dimensions.dart';
import '../../shared/app_text_styles.dart';
import '../restaurants/restaurant_detail_screen.dart';

class EnhancedHomeScreen extends StatefulWidget {
  const EnhancedHomeScreen({super.key});

  @override
  State<EnhancedHomeScreen> createState() => _EnhancedHomeScreenState();
}

class _EnhancedHomeScreenState extends State<EnhancedHomeScreen> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadRestaurants();
    });
  }

  void _loadRestaurants() {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final restaurantProvider = Provider.of<RestaurantProvider>(
      context,
      listen: false,
    );

    // Only load if not already loaded or if there's an error
    if (!restaurantProvider.hasLoadedRestaurants ||
        restaurantProvider.error != null) {
      final profile = authProvider.profile;
      if (profile != null &&
          profile.latitude != null &&
          profile.longitude != null) {
        restaurantProvider.loadRestaurants(
          latitude: profile.latitude!,
          longitude: profile.longitude!,
          isGuest: authProvider.isGuestMode,
        );
      } else {
        restaurantProvider.loadRestaurants(isGuest: authProvider.isGuestMode);
      }
    }
  }

  void _forceLoadRestaurants() {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final restaurantProvider = Provider.of<RestaurantProvider>(
      context,
      listen: false,
    );

    final profile = authProvider.profile;
    if (profile != null &&
        profile.latitude != null &&
        profile.longitude != null) {
      restaurantProvider.loadRestaurants(
        latitude: profile.latitude!,
        longitude: profile.longitude!,
        isGuest: authProvider.isGuestMode,
        forceReload: true,
      );
    } else {
      restaurantProvider.loadRestaurants(
        isGuest: authProvider.isGuestMode,
        forceReload: true,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: Text('BRSIMA', style: AppTextStyles.appBarTitle),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        elevation: 0,
        automaticallyImplyLeading: false,
        actions: [
          Consumer<AuthProvider>(
            builder: (context, authProvider, child) {
              if (authProvider.isGuestMode) {
                return TextButton(
                  onPressed: () {
                    Navigator.pushNamed(context, '/login');
                  },
                  child: const Text(
                    'Login',
                    style: TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                );
              }
              return IconButton(
                onPressed: () {
                  // TODO: Navigate to notifications
                },
                icon: const Icon(Icons.notifications_outlined),
              );
            },
          ),
        ],
      ),
      body: Consumer2<AuthProvider, RestaurantProvider>(
        builder: (context, authProvider, restaurantProvider, child) {
          final profile = authProvider.profile;

          return RefreshIndicator(
            onRefresh: () async => _loadRestaurants(),
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(AppDimensions.paddingM),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Welcome Section
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(AppDimensions.paddingL),
                    decoration: BoxDecoration(
                      gradient: AppColors.primaryGradient,
                      borderRadius: BorderRadius.circular(
                        AppDimensions.radiusM,
                      ),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          authProvider.isGuestMode
                              ? 'Welcome to BRSIMA!'
                              : 'Hello, ${profile?.fullName?.split(' ').first ?? 'User'}!',
                          style: AppTextStyles.h3.copyWith(color: Colors.white),
                        ),
                        const SizedBox(height: AppDimensions.spaceS),
                        Text(
                          'What would you like to eat today?',
                          style: AppTextStyles.bodyMedium.copyWith(
                            color: Colors.white.withOpacity(0.9),
                          ),
                        ),
                        if (profile?.address != null ||
                            authProvider.isGuestMode) ...[
                          const SizedBox(height: AppDimensions.spaceM),
                          Row(
                            children: [
                              const Icon(
                                Icons.location_on,
                                color: Colors.white,
                                size: 16,
                              ),
                              const SizedBox(width: AppDimensions.spaceS),
                              Expanded(
                                child: Text(
                                  authProvider.isGuestMode
                                      ? 'Login to set your delivery address'
                                      : 'Delivering to: ${profile!.address}',
                                  style: AppTextStyles.bodySmall.copyWith(
                                    color: Colors.white.withOpacity(0.9),
                                  ),
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ],
                    ),
                  ),

                  const SizedBox(height: AppDimensions.spaceL),

                  // Featured Restaurants Section
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text('Featured Restaurants', style: AppTextStyles.h4),
                      TextButton(
                        onPressed: () {
                          // Navigate to restaurants screen
                        },
                        child: const Text('See All'),
                      ),
                    ],
                  ),
                  const SizedBox(height: AppDimensions.spaceM),

                  if (restaurantProvider.isLoading)
                    const Center(child: CircularProgressIndicator())
                  else if (restaurantProvider.error != null)
                    Center(
                      child: Column(
                        children: [
                          Text(
                            'Error loading restaurants',
                            style: AppTextStyles.bodyMedium,
                          ),
                          const SizedBox(height: AppDimensions.spaceS),
                          ElevatedButton(
                            onPressed: _loadRestaurants,
                            child: const Text('Retry'),
                          ),
                        ],
                      ),
                    )
                  else if (restaurantProvider.restaurants.isEmpty)
                    Center(
                      child: Text(
                        'No restaurants available',
                        style: AppTextStyles.bodyMedium,
                      ),
                    )
                  else
                    SizedBox(
                      height: 200,
                      child: ListView.builder(
                        scrollDirection: Axis.horizontal,
                        itemCount: restaurantProvider.restaurants.length > 5
                            ? 5
                            : restaurantProvider.restaurants.length,
                        itemBuilder: (context, index) {
                          final restaurant =
                              restaurantProvider.restaurants[index];
                          return _buildRestaurantCard(
                            restaurant,
                            authProvider.isGuestMode,
                          );
                        },
                      ),
                    ),

                  const SizedBox(height: AppDimensions.spaceL),

                  // Popular Categories Section
                  Text('Popular Categories', style: AppTextStyles.h4),
                  const SizedBox(height: AppDimensions.spaceM),

                  GridView.count(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    crossAxisCount: 2,
                    crossAxisSpacing: AppDimensions.spaceM,
                    mainAxisSpacing: AppDimensions.spaceM,
                    childAspectRatio: 1.5,
                    children: [
                      _buildCategoryCard(
                        'Pizza',
                        Icons.local_pizza,
                        AppColors.primary,
                      ),
                      _buildCategoryCard(
                        'Burgers',
                        Icons.lunch_dining,
                        AppColors.secondary,
                      ),
                      _buildCategoryCard(
                        'Asian',
                        Icons.ramen_dining,
                        AppColors.accent,
                      ),
                      _buildCategoryCard(
                        'Desserts',
                        Icons.cake,
                        AppColors.warning,
                      ),
                    ],
                  ),

                  const SizedBox(height: AppDimensions.spaceXL),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildRestaurantCard(Restaurant restaurant, bool isGuestMode) {
    return Container(
      width: 160,
      margin: const EdgeInsets.only(right: AppDimensions.marginM),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadow,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: InkWell(
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => RestaurantDetailScreen(
                restaurant: restaurant,
                isGuestMode: isGuestMode,
              ),
            ),
          );
        },
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Restaurant Image
            Container(
              height: 100,
              decoration: BoxDecoration(
                color: AppColors.primary.withOpacity(0.1),
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(AppDimensions.radiusM),
                  topRight: Radius.circular(AppDimensions.radiusM),
                ),
              ),
              child: const Center(
                child: Icon(
                  Icons.restaurant,
                  size: 40,
                  color: AppColors.primary,
                ),
              ),
            ),
            // Restaurant Info
            Padding(
              padding: const EdgeInsets.all(AppDimensions.paddingM),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    restaurant.name,
                    style: AppTextStyles.labelLarge,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: AppDimensions.spaceXS),
                  Text(
                    restaurant.cuisineType,
                    style: AppTextStyles.bodySmall,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: AppDimensions.spaceXS),
                  Row(
                    children: [
                      const Icon(
                        Icons.star,
                        size: 16,
                        color: AppColors.warning,
                      ),
                      const SizedBox(width: AppDimensions.spaceXS),
                      Text(
                        restaurant.rating.toStringAsFixed(1),
                        style: AppTextStyles.bodySmall,
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCategoryCard(String title, IconData icon, Color color) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadow,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: InkWell(
        onTap: () {
          // TODO: Navigate to category
        },
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(AppDimensions.paddingM),
              decoration: BoxDecoration(
                color: color.withOpacity(0.1),
                borderRadius: BorderRadius.circular(AppDimensions.radiusS),
              ),
              child: Icon(icon, size: AppDimensions.iconL, color: color),
            ),
            const SizedBox(height: AppDimensions.spaceS),
            Text(title, style: AppTextStyles.labelLarge),
          ],
        ),
      ),
    );
  }
}
