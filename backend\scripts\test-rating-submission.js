const http = require('http');
const jwt = require('jsonwebtoken');

// Create a test JWT token for customer ID 3 (who has order 34)
function createTestToken() {
  const payload = {
    id: 3, // This should match a user_id in the users table
    email: '<EMAIL>',
    userType: 'customer'
  };
  
  // Use the same secret as in your auth middleware
  const secret = process.env.JWT_SECRET || 'your-secret-key';
  return jwt.sign(payload, secret, { expiresIn: '1h' });
}

function testRatingSubmission() {
  console.log('🧪 Testing Rating Submission with Authentication...\n');
  
  const token = createTestToken();
  console.log('Generated test token for customer ID 3');
  
  // Test food rating first
  const foodRatingData = JSON.stringify({
    menuItemId: 26,
    orderId: 34,
    rating: 5,
    reviewText: "Great food!"
  });

  console.log('📝 Testing food rating submission...');
  console.log('Request body:', foodRatingData);
  
  const foodOptions = {
    hostname: 'localhost',
    port: 3000,
    path: '/api/ratings/food',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    }
  };

  const foodReq = http.request(foodOptions, (res) => {
    let data = '';
    res.on('data', (chunk) => { data += chunk; });
    res.on('end', () => {
      console.log(`📡 Food Rating Response Status: ${res.statusCode}`);
      console.log('📄 Response:', data);
      console.log('');
      
      // Test driver rating after food rating
      setTimeout(() => {
        const driverRatingData = JSON.stringify({
          driverId: 1,
          orderId: 34,
          rating: 4,
          reviewText: "Good service!"
        });

        console.log('📝 Testing driver rating submission...');
        console.log('Request body:', driverRatingData);
        
        const driverOptions = {
          hostname: 'localhost',
          port: 3000,
          path: '/api/ratings/driver',
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
          }
        };

        const driverReq = http.request(driverOptions, (res) => {
          let data = '';
          res.on('data', (chunk) => { data += chunk; });
          res.on('end', () => {
            console.log(`📡 Driver Rating Response Status: ${res.statusCode}`);
            console.log('📄 Response:', data);
            
            // Test getting ratings after submission
            setTimeout(() => {
              console.log('\n📋 Testing get order ratings...');
              const getRatingsOptions = {
                hostname: 'localhost',
                port: 3000,
                path: '/api/ratings/customer/order/34',
                method: 'GET',
                headers: {
                  'Authorization': `Bearer ${token}`
                }
              };

              const getRatingsReq = http.request(getRatingsOptions, (res) => {
                let data = '';
                res.on('data', (chunk) => { data += chunk; });
                res.on('end', () => {
                  console.log(`📡 Get Ratings Response Status: ${res.statusCode}`);
                  console.log('📄 Response:', data);
                  process.exit(0);
                });
              });

              getRatingsReq.on('error', (error) => {
                console.error('❌ Get ratings request error:', error);
                process.exit(1);
              });

              getRatingsReq.end();
            }, 1000);
          });
        });

        driverReq.on('error', (error) => {
          console.error('❌ Driver rating request error:', error);
          process.exit(1);
        });

        driverReq.write(driverRatingData);
        driverReq.end();
      }, 1000);
    });
  });

  foodReq.on('error', (error) => {
    console.error('❌ Food rating request error:', error);
    process.exit(1);
  });

  foodReq.write(foodRatingData);
  foodReq.end();
}

// Wait a moment for server to be ready, then test
setTimeout(testRatingSubmission, 1000);
