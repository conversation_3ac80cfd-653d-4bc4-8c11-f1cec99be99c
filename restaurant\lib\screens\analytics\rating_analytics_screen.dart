import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import '../../services/api_service.dart';

class RatingAnalyticsScreen extends StatefulWidget {
  const RatingAnalyticsScreen({Key? key}) : super(key: key);

  @override
  State<RatingAnalyticsScreen> createState() => _RatingAnalyticsScreenState();
}

class _RatingAnalyticsScreenState extends State<RatingAnalyticsScreen> {
  bool _isLoading = true;
  String? _error;
  
  // Analytics data
  double _overallRating = 0.0;
  int _totalRatings = 0;
  List<MenuItemRating> _topRatedItems = [];
  List<MenuItemRating> _lowRatedItems = [];
  Map<int, int> _ratingDistribution = {};

  @override
  void initState() {
    super.initState();
    _loadRatingAnalytics();
  }

  Future<void> _loadRatingAnalytics() async {
    try {
      setState(() {
        _isLoading = true;
        _error = null;
      });

      final token = await ApiService.getToken();
      if (token == null) {
        throw Exception('Authentication token not found');
      }

      // Get restaurant menu items with ratings
      final response = await http.get(
        Uri.parse('${ApiService.baseUrl}/restaurant/menu'),
        headers: {
          'Authorization': 'Bearer $token',
        },
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        final categories = data['categories'] as List;
        
        List<MenuItemRating> allItems = [];
        double totalRatingSum = 0;
        int totalRatingCount = 0;
        Map<int, int> distribution = {1: 0, 2: 0, 3: 0, 4: 0, 5: 0};

        for (final category in categories) {
          final items = category['items'] as List;
          for (final item in items) {
            if (item['totalRatings'] > 0) {
              final menuItem = MenuItemRating.fromJson(item);
              allItems.add(menuItem);
              
              totalRatingSum += menuItem.averageRating * menuItem.totalRatings;
              totalRatingCount += menuItem.totalRatings;
              
              // Approximate distribution (simplified)
              final roundedRating = menuItem.averageRating.round();
              distribution[roundedRating] = (distribution[roundedRating] ?? 0) + menuItem.totalRatings;
            }
          }
        }

        // Sort items by rating
        allItems.sort((a, b) => b.averageRating.compareTo(a.averageRating));

        setState(() {
          _overallRating = totalRatingCount > 0 ? totalRatingSum / totalRatingCount : 0.0;
          _totalRatings = totalRatingCount;
          _topRatedItems = allItems.take(5).toList();
          _lowRatedItems = allItems.reversed.take(5).toList();
          _ratingDistribution = distribution;
          _isLoading = false;
        });
      } else {
        throw Exception('Failed to load rating analytics');
      }
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      appBar: AppBar(
        title: Text(
          'Rating Analytics',
          style: GoogleFonts.poppins(fontWeight: FontWeight.w600),
        ),
        backgroundColor: Colors.orange[600],
        foregroundColor: Colors.white,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _error != null
              ? _buildErrorState()
              : _buildAnalytics(),
    );
  }

  Widget _buildErrorState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.error_outline, size: 64, color: Colors.grey[400]),
          const SizedBox(height: 16),
          Text(
            'Failed to load analytics',
            style: GoogleFonts.poppins(
              fontSize: 18,
              fontWeight: FontWeight.w500,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            _error ?? 'Unknown error occurred',
            textAlign: TextAlign.center,
            style: GoogleFonts.poppins(
              fontSize: 14,
              color: Colors.grey[500],
            ),
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: _loadRatingAnalytics,
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.orange[600],
              foregroundColor: Colors.white,
            ),
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  Widget _buildAnalytics() {
    return RefreshIndicator(
      onRefresh: _loadRatingAnalytics,
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildOverallRating(),
            const SizedBox(height: 20),
            _buildRatingDistribution(),
            const SizedBox(height: 20),
            _buildTopRatedItems(),
            const SizedBox(height: 20),
            if (_lowRatedItems.isNotEmpty) _buildLowRatedItems(),
          ],
        ),
      ),
    );
  }

  Widget _buildOverallRating() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          children: [
            Text(
              'Overall Restaurant Rating',
              style: GoogleFonts.poppins(
                fontSize: 18,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  _overallRating.toStringAsFixed(1),
                  style: GoogleFonts.poppins(
                    fontSize: 48,
                    fontWeight: FontWeight.bold,
                    color: Colors.orange[600],
                  ),
                ),
                const SizedBox(width: 16),
                Column(
                  children: [
                    Row(
                      children: List.generate(5, (index) {
                        return Icon(
                          index < _overallRating.round() ? Icons.star : Icons.star_border,
                          color: Colors.amber,
                          size: 24,
                        );
                      }),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '$_totalRatings total ratings',
                      style: GoogleFonts.poppins(
                        fontSize: 14,
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRatingDistribution() {
    if (_totalRatings == 0) return const SizedBox.shrink();

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Rating Distribution',
              style: GoogleFonts.poppins(
                fontSize: 18,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 16),
            ...List.generate(5, (index) {
              final starCount = 5 - index;
              final count = _ratingDistribution[starCount] ?? 0;
              final percentage = _totalRatings > 0 ? count / _totalRatings : 0.0;

              return Padding(
                padding: const EdgeInsets.symmetric(vertical: 4),
                child: Row(
                  children: [
                    Text(
                      '$starCount',
                      style: GoogleFonts.poppins(fontSize: 14),
                    ),
                    const SizedBox(width: 4),
                    const Icon(Icons.star, size: 16, color: Colors.amber),
                    const SizedBox(width: 12),
                    Expanded(
                      child: LinearProgressIndicator(
                        value: percentage,
                        backgroundColor: Colors.grey[300],
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.orange[600]!),
                      ),
                    ),
                    const SizedBox(width: 12),
                    SizedBox(
                      width: 40,
                      child: Text(
                        count.toString(),
                        style: GoogleFonts.poppins(fontSize: 14),
                        textAlign: TextAlign.end,
                      ),
                    ),
                  ],
                ),
              );
            }),
          ],
        ),
      ),
    );
  }

  Widget _buildTopRatedItems() {
    if (_topRatedItems.isEmpty) return const SizedBox.shrink();

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Top Rated Items',
              style: GoogleFonts.poppins(
                fontSize: 18,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 16),
            ..._topRatedItems.map((item) => _buildItemRatingTile(item, true)),
          ],
        ),
      ),
    );
  }

  Widget _buildLowRatedItems() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Items Needing Improvement',
              style: GoogleFonts.poppins(
                fontSize: 18,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 16),
            ..._lowRatedItems.map((item) => _buildItemRatingTile(item, false)),
          ],
        ),
      ),
    );
  }

  Widget _buildItemRatingTile(MenuItemRating item, bool isTopRated) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: isTopRated ? Colors.green[50] : Colors.red[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: isTopRated ? Colors.green[200]! : Colors.red[200]!,
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  item.name,
                  style: GoogleFonts.poppins(
                    fontWeight: FontWeight.w600,
                    fontSize: 14,
                  ),
                ),
                const SizedBox(height: 4),
                Row(
                  children: [
                    ...List.generate(5, (index) {
                      return Icon(
                        index < item.averageRating.round() ? Icons.star : Icons.star_border,
                        color: Colors.amber,
                        size: 16,
                      );
                    }),
                    const SizedBox(width: 8),
                    Text(
                      '${item.averageRating.toStringAsFixed(1)} (${item.totalRatings})',
                      style: GoogleFonts.poppins(
                        fontSize: 12,
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          Icon(
            isTopRated ? Icons.trending_up : Icons.trending_down,
            color: isTopRated ? Colors.green[600] : Colors.red[600],
          ),
        ],
      ),
    );
  }
}

class MenuItemRating {
  final int id;
  final String name;
  final double averageRating;
  final int totalRatings;

  MenuItemRating({
    required this.id,
    required this.name,
    required this.averageRating,
    required this.totalRatings,
  });

  factory MenuItemRating.fromJson(Map<String, dynamic> json) {
    return MenuItemRating(
      id: json['id'],
      name: json['name'],
      averageRating: (json['averageRating'] ?? 0).toDouble(),
      totalRatings: json['totalRatings'] ?? 0,
    );
  }
}
