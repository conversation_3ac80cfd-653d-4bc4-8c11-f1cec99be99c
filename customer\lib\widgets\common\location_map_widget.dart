import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:provider/provider.dart';
import '../../providers/location_provider.dart';

class LocationMapWidget extends StatefulWidget {
  final double height;
  final LatLng? initialLocation;
  final bool showCurrentLocationButton;
  final bool allowLocationSelection;
  final Function(LatLng)? onLocationSelected;
  final String? markerTitle;

  const LocationMapWidget({
    super.key,
    this.height = 300,
    this.initialLocation,
    this.showCurrentLocationButton = true,
    this.allowLocationSelection = true,
    this.onLocationSelected,
    this.markerTitle,
  });

  @override
  State<LocationMapWidget> createState() => _LocationMapWidgetState();
}

class _LocationMapWidgetState extends State<LocationMapWidget> {
  GoogleMapController? _controller;
  Set<Marker> _markers = {};
  LatLng? _selectedLocation;

  // Default location (you can change this to your city's coordinates)
  static const LatLng _defaultLocation = LatLng(
    37.7749,
    -122.4194,
  ); // San Francisco

  @override
  void initState() {
    super.initState();
    _selectedLocation = widget.initialLocation;
    _updateMarkers();

    // If no initial location provided, try to get current location
    if (widget.initialLocation == null) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _getCurrentLocationIfNeeded();
      });
    }
  }

  @override
  void didUpdateWidget(LocationMapWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.initialLocation != oldWidget.initialLocation) {
      _selectedLocation = widget.initialLocation;
      _updateMarkers();
      if (_controller != null && _selectedLocation != null) {
        _controller!.animateCamera(CameraUpdate.newLatLng(_selectedLocation!));
      }
    }
  }

  void _updateMarkers() {
    _markers.clear();
    if (_selectedLocation != null) {
      _markers.add(
        Marker(
          markerId: const MarkerId('selected_location'),
          position: _selectedLocation!,
          infoWindow: InfoWindow(
            title: widget.markerTitle ?? 'Selected Location',
          ),
          icon: BitmapDescriptor.defaultMarkerWithHue(
            BitmapDescriptor.hueOrange,
          ),
        ),
      );
    }
    if (mounted) {
      setState(() {});
    }
  }

  void _onMapCreated(GoogleMapController controller) {
    _controller = controller;
    final locationProvider = Provider.of<LocationProvider>(
      context,
      listen: false,
    );
    locationProvider.setMapController(controller);

    // Move to initial location if provided
    if (_selectedLocation != null) {
      controller.animateCamera(
        CameraUpdate.newCameraPosition(
          CameraPosition(target: _selectedLocation!, zoom: 16.0),
        ),
      );
    }
  }

  void _onMapTap(LatLng position) {
    if (!widget.allowLocationSelection) return;

    setState(() {
      _selectedLocation = position;
    });
    _updateMarkers();

    // Update location provider
    final locationProvider = Provider.of<LocationProvider>(
      context,
      listen: false,
    );
    locationProvider.setLocationFromMap(position);

    // Notify parent widget
    if (widget.onLocationSelected != null) {
      widget.onLocationSelected!(position);
    }
  }

  Future<void> _getCurrentLocationIfNeeded() async {
    // Only get location automatically if no initial location was provided
    if (widget.initialLocation == null) {
      await _getCurrentLocation();
    }
  }

  Future<void> _getCurrentLocation() async {
    final locationProvider = Provider.of<LocationProvider>(
      context,
      listen: false,
    );

    final success = await locationProvider.getCurrentLocation();
    if (success && locationProvider.currentLatLng != null) {
      setState(() {
        _selectedLocation = locationProvider.currentLatLng;
      });
      _updateMarkers();

      if (_controller != null) {
        await _controller!.animateCamera(
          CameraUpdate.newCameraPosition(
            CameraPosition(target: _selectedLocation!, zoom: 16.0),
          ),
        );
      }

      // Notify parent widget
      if (widget.onLocationSelected != null) {
        widget.onLocationSelected!(_selectedLocation!);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: widget.height,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(12),
        child: Stack(
          children: [
            GoogleMap(
              onMapCreated: _onMapCreated,
              initialCameraPosition: CameraPosition(
                target: _selectedLocation ?? _defaultLocation,
                zoom: 16.0,
              ),
              markers: _markers,
              onTap: _onMapTap,
              myLocationEnabled: true,
              myLocationButtonEnabled: false,
              zoomControlsEnabled: false,
              mapToolbarEnabled: false,
              compassEnabled: true,
              buildingsEnabled: true,
              trafficEnabled: false,
            ),

            // Current location button
            if (widget.showCurrentLocationButton)
              Positioned(
                top: 16,
                right: 16,
                child: Consumer<LocationProvider>(
                  builder: (context, locationProvider, child) {
                    return FloatingActionButton.small(
                      onPressed: locationProvider.isLoading
                          ? null
                          : _getCurrentLocation,
                      backgroundColor: Colors.white,
                      foregroundColor: Colors.orange,
                      elevation: 4,
                      child: locationProvider.isLoading
                          ? const SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor: AlwaysStoppedAnimation<Color>(
                                  Colors.orange,
                                ),
                              ),
                            )
                          : const Icon(Icons.my_location),
                    );
                  },
                ),
              ),

            // Instructions overlay
            if (widget.allowLocationSelection)
              Positioned(
                bottom: 16,
                left: 16,
                right: 16,
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 8,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.black.withValues(alpha: 0.7),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Text(
                    'Tap on the map to select a location',
                    style: TextStyle(color: Colors.white, fontSize: 12),
                    textAlign: TextAlign.center,
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  @override
  void dispose() {
    _controller?.dispose();
    super.dispose();
  }
}
