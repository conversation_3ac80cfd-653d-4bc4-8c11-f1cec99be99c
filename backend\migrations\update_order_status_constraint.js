const { pool } = require('../config/database');

async function updateOrderStatusConstraint() {
  const client = await pool.connect();
  
  try {
    console.log('Updating order status constraint...');
    
    // Drop the existing constraint
    await client.query(`
      ALTER TABLE orders 
      DROP CONSTRAINT IF EXISTS orders_status_check;
    `);
    
    // Add the new constraint with additional statuses
    await client.query(`
      ALTER TABLE orders
      ADD CONSTRAINT orders_status_check
      CHECK (status IN ('pending', 'confirmed', 'preparing', 'ready', 'assigned', 'picked_up', 'delivered', 'cancelled'));
    `);
    
    console.log('Order status constraint updated successfully!');
    console.log('New allowed statuses: pending, confirmed, preparing, ready, assigned, picked_up, delivered, cancelled');
    
  } catch (error) {
    console.error('Error updating order status constraint:', error);
    throw error;
  } finally {
    client.release();
  }
}

// Run the migration if this file is executed directly
if (require.main === module) {
  updateOrderStatusConstraint()
    .then(() => {
      console.log('Migration completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('Migration failed:', error);
      process.exit(1);
    });
}

module.exports = { updateOrderStatusConstraint };
