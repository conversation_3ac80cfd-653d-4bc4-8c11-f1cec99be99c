# BRSIMA Backend

Node.js backend server with Express.js and PostgreSQL integration.

## Setup

1. **Install dependencies:**
   ```bash
   npm install
   ```

2. **Environment configuration:**
   ```bash
   cp .env.example .env
   # Edit .env with your database credentials
   ```

3. **Database setup:**
   - Install PostgreSQL
   - Create database: `brsima_db`
   - Update .env with your credentials

4. **Run the server:**
   ```bash
   # Development mode
   npm run dev
   
   # Production mode
   npm start
   ```

## API Endpoints

- `GET /` - Server status
- `GET /health` - Health check
- `GET /api` - API information

## Project Structure

```
backend/
├── server.js          # Main server file
├── package.json       # Dependencies
├── .env.example       # Environment variables template
└── README.md          # This file
```

## Next Steps

- Implement database models
- Add authentication routes
- Create API endpoints
- Add middleware for validation
- Implement error handling
