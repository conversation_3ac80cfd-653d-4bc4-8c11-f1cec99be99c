const { pool } = require('../config/database');

const ratingMigrations = [
  // Food ratings table
  `
    CREATE TABLE IF NOT EXISTS food_ratings (
      id SERIAL PRIMARY KEY,
      customer_id INTEGER REFERENCES customer_profiles(id) ON DELETE CASCADE,
      menu_item_id INTEGER REFERENCES menu_items(id) ON DELETE CASCADE,
      order_id INTEGER REFERENCES orders(id) ON DELETE CASCADE,
      rating INTEGER NOT NULL CHECK (rating >= 1 AND rating <= 5),
      review_text TEXT,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      UNIQUE(customer_id, menu_item_id, order_id)
    );
  `,

  // Driver ratings table
  `
    CREATE TABLE IF NOT EXISTS driver_ratings (
      id SERIAL PRIMARY KEY,
      customer_id INTEGER REFERENCES customer_profiles(id) ON DELETE CASCADE,
      driver_id INTEGER REFERENCES driver_profiles(id) ON DELETE CASCADE,
      order_id INTEGER REFERENCES orders(id) ON DELETE CASCADE,
      rating INTEGER NOT NULL CHECK (rating >= 1 AND rating <= 5),
      review_text TEXT,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      UNIQUE(customer_id, driver_id, order_id)
    );
  `,

  // Add rating columns to menu_items table
  `
    ALTER TABLE menu_items 
    ADD COLUMN IF NOT EXISTS average_rating DECIMAL(3, 2) DEFAULT 0.00,
    ADD COLUMN IF NOT EXISTS total_ratings INTEGER DEFAULT 0;
  `,

  // Create indexes for rating tables
  `
    CREATE INDEX IF NOT EXISTS idx_food_ratings_customer ON food_ratings(customer_id);
    CREATE INDEX IF NOT EXISTS idx_food_ratings_menu_item ON food_ratings(menu_item_id);
    CREATE INDEX IF NOT EXISTS idx_food_ratings_order ON food_ratings(order_id);
    CREATE INDEX IF NOT EXISTS idx_food_ratings_rating ON food_ratings(rating);
    CREATE INDEX IF NOT EXISTS idx_driver_ratings_customer ON driver_ratings(customer_id);
    CREATE INDEX IF NOT EXISTS idx_driver_ratings_driver ON driver_ratings(driver_id);
    CREATE INDEX IF NOT EXISTS idx_driver_ratings_order ON driver_ratings(order_id);
    CREATE INDEX IF NOT EXISTS idx_driver_ratings_rating ON driver_ratings(rating);
    CREATE INDEX IF NOT EXISTS idx_menu_items_rating ON menu_items(average_rating);
  `,

  // Create function to update menu item ratings
  `
    CREATE OR REPLACE FUNCTION update_menu_item_rating(item_id INTEGER)
    RETURNS VOID AS $$
    BEGIN
      UPDATE menu_items 
      SET 
        average_rating = (
          SELECT COALESCE(AVG(rating), 0) 
          FROM food_ratings 
          WHERE menu_item_id = item_id
        ),
        total_ratings = (
          SELECT COUNT(*) 
          FROM food_ratings 
          WHERE menu_item_id = item_id
        )
      WHERE id = item_id;
    END;
    $$ LANGUAGE plpgsql;
  `,

  // Create function to update driver ratings
  `
    CREATE OR REPLACE FUNCTION update_driver_rating(driver_profile_id INTEGER)
    RETURNS VOID AS $$
    BEGIN
      UPDATE driver_profiles 
      SET 
        rating = (
          SELECT COALESCE(AVG(rating), 0) 
          FROM driver_ratings 
          WHERE driver_id = driver_profile_id
        )
      WHERE id = driver_profile_id;
    END;
    $$ LANGUAGE plpgsql;
  `,

  // Create function to update restaurant rating based on food ratings
  `
    CREATE OR REPLACE FUNCTION update_restaurant_rating(restaurant_profile_id INTEGER)
    RETURNS VOID AS $$
    BEGIN
      UPDATE restaurant_profiles 
      SET 
        rating = (
          SELECT COALESCE(AVG(fr.rating), 0) 
          FROM food_ratings fr
          JOIN menu_items mi ON fr.menu_item_id = mi.id
          WHERE mi.restaurant_id = restaurant_profile_id
        )
      WHERE id = restaurant_profile_id;
    END;
    $$ LANGUAGE plpgsql;
  `,

  // Create triggers to automatically update ratings
  `
    CREATE OR REPLACE FUNCTION trigger_update_menu_item_rating()
    RETURNS TRIGGER AS $$
    BEGIN
      IF TG_OP = 'INSERT' OR TG_OP = 'UPDATE' THEN
        PERFORM update_menu_item_rating(NEW.menu_item_id);
        PERFORM update_restaurant_rating((SELECT restaurant_id FROM menu_items WHERE id = NEW.menu_item_id));
        RETURN NEW;
      ELSIF TG_OP = 'DELETE' THEN
        PERFORM update_menu_item_rating(OLD.menu_item_id);
        PERFORM update_restaurant_rating((SELECT restaurant_id FROM menu_items WHERE id = OLD.menu_item_id));
        RETURN OLD;
      END IF;
      RETURN NULL;
    END;
    $$ LANGUAGE plpgsql;
  `,

  `
    CREATE OR REPLACE FUNCTION trigger_update_driver_rating()
    RETURNS TRIGGER AS $$
    BEGIN
      IF TG_OP = 'INSERT' OR TG_OP = 'UPDATE' THEN
        PERFORM update_driver_rating(NEW.driver_id);
        RETURN NEW;
      ELSIF TG_OP = 'DELETE' THEN
        PERFORM update_driver_rating(OLD.driver_id);
        RETURN OLD;
      END IF;
      RETURN NULL;
    END;
    $$ LANGUAGE plpgsql;
  `,

  // Create the actual triggers
  `
    DROP TRIGGER IF EXISTS food_rating_update_trigger ON food_ratings;
    CREATE TRIGGER food_rating_update_trigger
      AFTER INSERT OR UPDATE OR DELETE ON food_ratings
      FOR EACH ROW EXECUTE FUNCTION trigger_update_menu_item_rating();
  `,

  `
    DROP TRIGGER IF EXISTS driver_rating_update_trigger ON driver_ratings;
    CREATE TRIGGER driver_rating_update_trigger
      AFTER INSERT OR UPDATE OR DELETE ON driver_ratings
      FOR EACH ROW EXECUTE FUNCTION trigger_update_driver_rating();
  `
];

async function runRatingMigrations() {
  const client = await pool.connect();
  
  try {
    console.log('🌟 Starting rating system migrations...');
    
    for (let i = 0; i < ratingMigrations.length; i++) {
      console.log(`📝 Running rating migration ${i + 1}/${ratingMigrations.length}...`);
      await client.query(ratingMigrations[i]);
    }
    
    console.log('✅ Rating system migrations completed successfully!');
  } catch (error) {
    console.error('❌ Rating migration failed:', error);
    throw error;
  } finally {
    client.release();
  }
}

// Run migrations if this file is executed directly
if (require.main === module) {
  runRatingMigrations()
    .then(() => {
      console.log('🎉 Rating system setup complete!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Rating system setup failed:', error);
      process.exit(1);
    });
}

module.exports = { runRatingMigrations };
