const express = require('express');
const router = express.Router();
const { query } = require('../config/database');
const { authenticateToken, requireRole } = require('../middleware/auth');
const { validateRequest } = require('../middleware/validation');
const Joi = require('joi');

// Validation schemas
const schemas = {
  foodRating: Joi.object({
    menuItemId: Joi.number().integer().positive().required(),
    orderId: Joi.number().integer().positive().required(),
    rating: Joi.number().integer().min(1).max(5).required(),
    reviewText: Joi.string().max(500).optional().allow('')
  }),
  
  driverRating: Joi.object({
    driverId: Joi.number().integer().positive().required(),
    orderId: Joi.number().integer().positive().required(),
    rating: Joi.number().integer().min(1).max(5).required(),
    reviewText: Joi.string().max(500).optional().allow('')
  })
};

// ============================================================================
// FOOD RATING ENDPOINTS
// ============================================================================

// Submit food rating
router.post('/food',
  authenticateToken,
  requireRole(['customer']),
  validateRequest(schemas.foodRating),
  async (req, res) => {
    try {
      console.log('Food rating request received:', req.body);
      console.log('User:', req.user);

      const { menuItemId, orderId, rating, reviewText } = req.body;
      const userId = req.user.id;

      // Get customer profile
      const customerResult = await query(
        'SELECT id FROM customer_profiles WHERE user_id = $1',
        [userId]
      );

      if (customerResult.rows.length === 0) {
        return res.status(404).json({ error: 'Customer profile not found' });
      }

      const customerId = customerResult.rows[0].id;

      // Verify order belongs to customer and contains the menu item
      const orderVerification = await query(`
        SELECT o.id, oi.menu_item_id 
        FROM orders o
        JOIN order_items oi ON o.id = oi.order_id
        WHERE o.id = $1 AND o.customer_id = $2 AND oi.menu_item_id = $3 AND o.status = 'delivered'
      `, [orderId, customerId, menuItemId]);

      if (orderVerification.rows.length === 0) {
        return res.status(400).json({ 
          error: 'Invalid order or menu item, or order not yet delivered' 
        });
      }

      // Check if rating already exists
      const existingRating = await query(
        'SELECT id FROM food_ratings WHERE customer_id = $1 AND menu_item_id = $2 AND order_id = $3',
        [customerId, menuItemId, orderId]
      );

      let result;
      if (existingRating.rows.length > 0) {
        // Update existing rating
        result = await query(`
          UPDATE food_ratings 
          SET rating = $1, review_text = $2, updated_at = CURRENT_TIMESTAMP
          WHERE customer_id = $3 AND menu_item_id = $4 AND order_id = $5
          RETURNING *
        `, [rating, reviewText || null, customerId, menuItemId, orderId]);
      } else {
        // Create new rating
        result = await query(`
          INSERT INTO food_ratings (customer_id, menu_item_id, order_id, rating, review_text)
          VALUES ($1, $2, $3, $4, $5)
          RETURNING *
        `, [customerId, menuItemId, orderId, rating, reviewText || null]);
      }

      res.status(201).json({
        message: 'Food rating submitted successfully',
        rating: {
          id: result.rows[0].id,
          rating: result.rows[0].rating,
          reviewText: result.rows[0].review_text,
          createdAt: result.rows[0].created_at
        }
      });

    } catch (error) {
      console.error('Submit food rating error:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }
);

// Get food ratings for a menu item
router.get('/food/menu-item/:menuItemId', async (req, res) => {
  try {
    const { menuItemId } = req.params;
    const { page = 1, limit = 10 } = req.query;
    const offset = (page - 1) * limit;

    const ratingsResult = await query(`
      SELECT 
        fr.id, fr.rating, fr.review_text, fr.created_at,
        cp.full_name as customer_name
      FROM food_ratings fr
      JOIN customer_profiles cp ON fr.customer_id = cp.id
      WHERE fr.menu_item_id = $1
      ORDER BY fr.created_at DESC
      LIMIT $2 OFFSET $3
    `, [menuItemId, limit, offset]);

    const countResult = await query(
      'SELECT COUNT(*) as total FROM food_ratings WHERE menu_item_id = $1',
      [menuItemId]
    );

    const averageResult = await query(
      'SELECT AVG(rating) as average_rating FROM food_ratings WHERE menu_item_id = $1',
      [menuItemId]
    );

    res.json({
      ratings: ratingsResult.rows.map(rating => ({
        id: rating.id,
        rating: rating.rating,
        reviewText: rating.review_text,
        customerName: rating.customer_name,
        createdAt: rating.created_at
      })),
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: parseInt(countResult.rows[0].total),
        totalPages: Math.ceil(countResult.rows[0].total / limit)
      },
      averageRating: parseFloat(averageResult.rows[0].average_rating) || 0
    });

  } catch (error) {
    console.error('Get food ratings error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// ============================================================================
// DRIVER RATING ENDPOINTS
// ============================================================================

// Submit driver rating
router.post('/driver',
  authenticateToken,
  requireRole(['customer']),
  validateRequest(schemas.driverRating),
  async (req, res) => {
    try {
      console.log('Driver rating request received:', req.body);
      console.log('User:', req.user);

      const { driverId, orderId, rating, reviewText } = req.body;
      const userId = req.user.id;

      // Get customer profile
      const customerResult = await query(
        'SELECT id FROM customer_profiles WHERE user_id = $1',
        [userId]
      );

      if (customerResult.rows.length === 0) {
        return res.status(404).json({ error: 'Customer profile not found' });
      }

      const customerId = customerResult.rows[0].id;

      // Verify order belongs to customer and was delivered by the driver
      const orderVerification = await query(`
        SELECT id FROM orders 
        WHERE id = $1 AND customer_id = $2 AND driver_id = $3 AND status = 'delivered'
      `, [orderId, customerId, driverId]);

      if (orderVerification.rows.length === 0) {
        return res.status(400).json({ 
          error: 'Invalid order or driver, or order not yet delivered' 
        });
      }

      // Check if rating already exists
      const existingRating = await query(
        'SELECT id FROM driver_ratings WHERE customer_id = $1 AND driver_id = $2 AND order_id = $3',
        [customerId, driverId, orderId]
      );

      let result;
      if (existingRating.rows.length > 0) {
        // Update existing rating
        result = await query(`
          UPDATE driver_ratings 
          SET rating = $1, review_text = $2, updated_at = CURRENT_TIMESTAMP
          WHERE customer_id = $3 AND driver_id = $4 AND order_id = $5
          RETURNING *
        `, [rating, reviewText || null, customerId, driverId, orderId]);
      } else {
        // Create new rating
        result = await query(`
          INSERT INTO driver_ratings (customer_id, driver_id, order_id, rating, review_text)
          VALUES ($1, $2, $3, $4, $5)
          RETURNING *
        `, [customerId, driverId, orderId, rating, reviewText || null]);
      }

      res.status(201).json({
        message: 'Driver rating submitted successfully',
        rating: {
          id: result.rows[0].id,
          rating: result.rows[0].rating,
          reviewText: result.rows[0].review_text,
          createdAt: result.rows[0].created_at
        }
      });

    } catch (error) {
      console.error('Submit driver rating error:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }
);

// Get driver ratings
router.get('/driver/:driverId', async (req, res) => {
  try {
    const { driverId } = req.params;
    const { page = 1, limit = 10 } = req.query;
    const offset = (page - 1) * limit;

    const ratingsResult = await query(`
      SELECT 
        dr.id, dr.rating, dr.review_text, dr.created_at,
        cp.full_name as customer_name
      FROM driver_ratings dr
      JOIN customer_profiles cp ON dr.customer_id = cp.id
      WHERE dr.driver_id = $1
      ORDER BY dr.created_at DESC
      LIMIT $2 OFFSET $3
    `, [driverId, limit, offset]);

    const countResult = await query(
      'SELECT COUNT(*) as total FROM driver_ratings WHERE driver_id = $1',
      [driverId]
    );

    const averageResult = await query(
      'SELECT AVG(rating) as average_rating FROM driver_ratings WHERE driver_id = $1',
      [driverId]
    );

    res.json({
      ratings: ratingsResult.rows.map(rating => ({
        id: rating.id,
        rating: rating.rating,
        reviewText: rating.review_text,
        customerName: rating.customer_name,
        createdAt: rating.created_at
      })),
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: parseInt(countResult.rows[0].total),
        totalPages: Math.ceil(countResult.rows[0].total / limit)
      },
      averageRating: parseFloat(averageResult.rows[0].average_rating) || 0
    });

  } catch (error) {
    console.error('Get driver ratings error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// ============================================================================
// CUSTOMER RATING ENDPOINTS
// ============================================================================

// Get customer's ratings for a specific order
router.get('/customer/order/:orderId',
  authenticateToken,
  requireRole(['customer']),
  async (req, res) => {
    try {
      const { orderId } = req.params;
      const userId = req.user.id;

      // Get customer profile
      const customerResult = await query(
        'SELECT id FROM customer_profiles WHERE user_id = $1',
        [userId]
      );

      if (customerResult.rows.length === 0) {
        return res.status(404).json({ error: 'Customer profile not found' });
      }

      const customerId = customerResult.rows[0].id;

      // Verify order belongs to customer
      const orderResult = await query(
        'SELECT id, driver_id FROM orders WHERE id = $1 AND customer_id = $2',
        [orderId, customerId]
      );

      if (orderResult.rows.length === 0) {
        return res.status(404).json({ error: 'Order not found' });
      }

      const order = orderResult.rows[0];

      // Get food ratings for this order
      const foodRatingsResult = await query(`
        SELECT
          fr.id, fr.rating, fr.review_text, fr.created_at,
          mi.id as menu_item_id, mi.name as menu_item_name
        FROM food_ratings fr
        JOIN menu_items mi ON fr.menu_item_id = mi.id
        WHERE fr.order_id = $1 AND fr.customer_id = $2
      `, [orderId, customerId]);

      // Get driver rating for this order (if driver exists)
      let driverRating = null;
      if (order.driver_id) {
        const driverRatingResult = await query(`
          SELECT
            dr.id, dr.rating, dr.review_text, dr.created_at,
            dp.full_name as driver_name
          FROM driver_ratings dr
          JOIN driver_profiles dp ON dr.driver_id = dp.id
          WHERE dr.order_id = $1 AND dr.customer_id = $2
        `, [orderId, customerId]);

        if (driverRatingResult.rows.length > 0) {
          const rating = driverRatingResult.rows[0];
          driverRating = {
            id: rating.id,
            rating: rating.rating,
            reviewText: rating.review_text,
            driverName: rating.driver_name,
            createdAt: rating.created_at
          };
        }
      }

      res.json({
        orderId: parseInt(orderId),
        foodRatings: foodRatingsResult.rows.map(rating => ({
          id: rating.id,
          rating: rating.rating,
          reviewText: rating.review_text,
          menuItemId: rating.menu_item_id,
          menuItemName: rating.menu_item_name,
          createdAt: rating.created_at
        })),
        driverRating
      });

    } catch (error) {
      console.error('Get customer order ratings error:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }
);

// Get items available for rating from a completed order
router.get('/customer/order/:orderId/available',
  authenticateToken,
  requireRole(['customer']),
  async (req, res) => {
    try {
      const { orderId } = req.params;
      const userId = req.user.id;

      // Get customer profile
      const customerResult = await query(
        'SELECT id FROM customer_profiles WHERE user_id = $1',
        [userId]
      );

      if (customerResult.rows.length === 0) {
        return res.status(404).json({ error: 'Customer profile not found' });
      }

      const customerId = customerResult.rows[0].id;

      // Get order details and verify it's delivered
      const orderResult = await query(`
        SELECT o.id, o.driver_id, o.status,
               dp.id as driver_profile_id, dp.full_name as driver_name
        FROM orders o
        LEFT JOIN driver_profiles dp ON o.driver_id = dp.id
        WHERE o.id = $1 AND o.customer_id = $2 AND o.status = 'delivered'
      `, [orderId, customerId]);

      if (orderResult.rows.length === 0) {
        return res.status(404).json({ error: 'Order not found or not yet delivered' });
      }

      const order = orderResult.rows[0];

      // Get menu items from this order that haven't been rated yet
      const availableItemsResult = await query(`
        SELECT
          oi.menu_item_id, mi.name, mi.description, mi.image_url,
          oi.quantity, oi.unit_price,
          CASE WHEN fr.id IS NULL THEN false ELSE true END as already_rated
        FROM order_items oi
        JOIN menu_items mi ON oi.menu_item_id = mi.id
        LEFT JOIN food_ratings fr ON (fr.menu_item_id = oi.menu_item_id AND fr.order_id = oi.order_id AND fr.customer_id = $2)
        WHERE oi.order_id = $1
        ORDER BY mi.name
      `, [orderId, customerId]);

      // Check if driver has been rated
      let driverRatingStatus = null;
      if (order.driver_profile_id) {
        const driverRatingResult = await query(
          'SELECT id FROM driver_ratings WHERE order_id = $1 AND customer_id = $2 AND driver_id = $3',
          [orderId, customerId, order.driver_profile_id]
        );

        driverRatingStatus = {
          driverId: order.driver_profile_id,
          driverName: order.driver_name,
          alreadyRated: driverRatingResult.rows.length > 0
        };
      }

      res.json({
        orderId: parseInt(orderId),
        menuItems: availableItemsResult.rows.map(item => ({
          menuItemId: item.menu_item_id,
          name: item.name,
          description: item.description,
          imageUrl: item.image_url,
          quantity: item.quantity,
          unitPrice: parseFloat(item.unit_price),
          alreadyRated: item.already_rated
        })),
        driver: driverRatingStatus
      });

    } catch (error) {
      console.error('Get available ratings error:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }
);

module.exports = router;
