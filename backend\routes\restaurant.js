const express = require('express');
const { query } = require('../config/database');
const { authenticateToken, requireRole } = require('../middleware/auth');
const { validateRequest, schemas } = require('../middleware/validation');
const imageService = require('../services/imageService');

const router = express.Router();

// Complete restaurant profile
router.post('/profile', 
  authenticateToken, 
  requireRole(['restaurant']), 
  validateRequest(schemas.restaurantProfile), 
  async (req, res) => {
    try {
      const { restaurantName, description, phone, address, latitude, longitude, cuisineType } = req.body;
      const userId = req.user.id;

      // Check if profile already exists
      const existingProfile = await query(
        'SELECT id FROM restaurant_profiles WHERE user_id = $1',
        [userId]
      );

      let result;
      if (existingProfile.rows.length > 0) {
        // Update existing profile
        result = await query(
          `UPDATE restaurant_profiles 
           SET restaurant_name = $1, description = $2, phone = $3, address = $4, 
               latitude = $5, longitude = $6, cuisine_type = $7, updated_at = CURRENT_TIMESTAMP
           WHERE user_id = $8 
           RETURNING *`,
          [restaurantName, description, phone, address, latitude, longitude, cuisineType, userId]
        );
      } else {
        // Create new profile
        result = await query(
          `INSERT INTO restaurant_profiles 
           (user_id, restaurant_name, description, phone, address, latitude, longitude, cuisine_type) 
           VALUES ($1, $2, $3, $4, $5, $6, $7, $8) 
           RETURNING *`,
          [userId, restaurantName, description, phone, address, latitude, longitude, cuisineType]
        );
      }

      const profile = result.rows[0];

      res.json({
        message: 'Restaurant profile completed successfully',
        profile: {
          id: profile.id,
          restaurantName: profile.restaurant_name,
          description: profile.description,
          phone: profile.phone,
          address: profile.address,
          latitude: profile.latitude,
          longitude: profile.longitude,
          cuisineType: profile.cuisine_type,
          isOpen: profile.is_open,
          rating: profile.rating,
          totalOrders: profile.total_orders
        }
      });

    } catch (error) {
      console.error('Restaurant profile completion error:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }
);

// Get restaurant profile
router.get('/profile', authenticateToken, requireRole(['restaurant']), async (req, res) => {
  try {
    const userId = req.user.id;

    const result = await query(
      'SELECT * FROM restaurant_profiles WHERE user_id = $1',
      [userId]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Restaurant profile not found' });
    }

    const profile = result.rows[0];

    res.json({
      profile: {
        id: profile.id,
        restaurantName: profile.restaurant_name,
        description: profile.description,
        phone: profile.phone,
        address: profile.address,
        latitude: profile.latitude,
        longitude: profile.longitude,
        cuisineType: profile.cuisine_type,
        isOpen: profile.is_open,
        rating: profile.rating,
        totalOrders: profile.total_orders,
        createdAt: profile.created_at,
        updatedAt: profile.updated_at
      }
    });

  } catch (error) {
    console.error('Get restaurant profile error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Toggle restaurant open/closed status
router.patch('/status', authenticateToken, requireRole(['restaurant']), async (req, res) => {
  try {
    const { isOpen } = req.body;
    const userId = req.user.id;

    if (typeof isOpen !== 'boolean') {
      return res.status(400).json({ error: 'isOpen must be a boolean value' });
    }

    const result = await query(
      `UPDATE restaurant_profiles 
       SET is_open = $1, updated_at = CURRENT_TIMESTAMP
       WHERE user_id = $2 
       RETURNING is_open`,
      [isOpen, userId]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Restaurant profile not found' });
    }

    res.json({
      message: `Restaurant ${isOpen ? 'opened' : 'closed'} successfully`,
      isOpen: result.rows[0].is_open
    });

  } catch (error) {
    console.error('Update restaurant status error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Get restaurant orders
router.get('/orders', authenticateToken, requireRole(['restaurant']), async (req, res) => {
  try {
    const userId = req.user.id;
    const { status } = req.query;

    // Get restaurant profile first
    const restaurantResult = await query(
      'SELECT id FROM restaurant_profiles WHERE user_id = $1',
      [userId]
    );

    if (restaurantResult.rows.length === 0) {
      return res.status(404).json({ error: 'Restaurant profile not found' });
    }

    const restaurantId = restaurantResult.rows[0].id;

    let ordersQuery = `
      SELECT o.id, o.order_number, o.status, o.total_amount, o.delivery_fee,
             o.delivery_address, o.delivery_latitude, o.delivery_longitude,
             o.special_instructions, o.estimated_delivery_time,
             o.created_at, o.updated_at,
             cp.full_name as customer_name, cp.phone as customer_phone
      FROM orders o
      JOIN customer_profiles cp ON o.customer_id = cp.id
      WHERE o.restaurant_id = $1
    `;
    let queryParams = [restaurantId];

    if (status) {
      ordersQuery += ' AND o.status = $2';
      queryParams.push(status);
    }

    ordersQuery += ' ORDER BY o.created_at DESC';

    const result = await query(ordersQuery, queryParams);

    res.json({
      orders: result.rows.map(order => ({
        id: order.id,
        orderNumber: order.order_number,
        status: order.status,
        totalAmount: parseFloat(order.total_amount),
        deliveryFee: parseFloat(order.delivery_fee),
        deliveryAddress: order.delivery_address,
        deliveryLatitude: order.delivery_latitude ? parseFloat(order.delivery_latitude) : null,
        deliveryLongitude: order.delivery_longitude ? parseFloat(order.delivery_longitude) : null,
        specialInstructions: order.special_instructions,
        estimatedDeliveryTime: order.estimated_delivery_time,
        customer: {
          name: order.customer_name,
          phone: order.customer_phone
        },
        createdAt: order.created_at,
        updatedAt: order.updated_at
      }))
    });

  } catch (error) {
    console.error('Get restaurant orders error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Get specific order details with items
router.get('/orders/:orderId', authenticateToken, requireRole(['restaurant']), async (req, res) => {
  try {
    const { orderId } = req.params;
    const userId = req.user.id;

    // Get restaurant profile first
    const restaurantResult = await query(
      'SELECT id FROM restaurant_profiles WHERE user_id = $1',
      [userId]
    );

    if (restaurantResult.rows.length === 0) {
      return res.status(404).json({ error: 'Restaurant profile not found' });
    }

    const restaurantId = restaurantResult.rows[0].id;

    // Get order details
    const orderResult = await query(
      `SELECT
         o.id, o.order_number, o.status, o.total_amount, o.delivery_fee,
         o.delivery_address, o.delivery_latitude, o.delivery_longitude,
         o.special_instructions, o.estimated_delivery_time,
         o.created_at, o.updated_at,
         cp.full_name as customer_name, cp.phone as customer_phone,
         dp.full_name as driver_name, dp.phone as driver_phone
       FROM orders o
       JOIN customer_profiles cp ON o.customer_id = cp.id
       LEFT JOIN driver_profiles dp ON o.driver_id = dp.id
       WHERE o.id = $1 AND o.restaurant_id = $2`,
      [orderId, restaurantId]
    );

    if (orderResult.rows.length === 0) {
      return res.status(404).json({ error: 'Order not found' });
    }

    const order = orderResult.rows[0];

    // Get order items
    const itemsResult = await query(
      `SELECT
         oi.quantity, oi.unit_price, oi.total_price, oi.special_instructions,
         mi.name, mi.description, mi.image_url
       FROM order_items oi
       JOIN menu_items mi ON oi.menu_item_id = mi.id
       WHERE oi.order_id = $1`,
      [orderId]
    );

    const orderDetails = {
      id: order.id,
      orderNumber: order.order_number,
      status: order.status,
      totalAmount: parseFloat(order.total_amount),
      deliveryFee: parseFloat(order.delivery_fee),
      deliveryAddress: order.delivery_address,
      deliveryLatitude: order.delivery_latitude ? parseFloat(order.delivery_latitude) : null,
      deliveryLongitude: order.delivery_longitude ? parseFloat(order.delivery_longitude) : null,
      deliveryLocation: {
        latitude: order.delivery_latitude ? parseFloat(order.delivery_latitude) : null,
        longitude: order.delivery_longitude ? parseFloat(order.delivery_longitude) : null
      },
      specialInstructions: order.special_instructions,
      estimatedDeliveryTime: order.estimated_delivery_time,
      createdAt: order.created_at,
      updatedAt: order.updated_at,
      customer: {
        name: order.customer_name,
        phone: order.customer_phone
      },
      driver: order.driver_name ? {
        name: order.driver_name,
        phone: order.driver_phone
      } : null,
      items: itemsResult.rows.map(item => ({
        name: item.name,
        description: item.description,
        imageUrl: item.image_url,
        quantity: item.quantity,
        unitPrice: parseFloat(item.unit_price),
        totalPrice: parseFloat(item.total_price),
        specialInstructions: item.special_instructions
      }))
    };

    res.json({ order: orderDetails });

  } catch (error) {
    console.error('Get restaurant order details error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Cancel order (restaurant decline)
router.patch('/orders/:orderId/cancel', authenticateToken, requireRole(['restaurant']), async (req, res) => {
  try {
    const { orderId } = req.params;
    const { reason } = req.body;
    const userId = req.user.id;

    // Get restaurant profile first
    const restaurantResult = await query(
      'SELECT id FROM restaurant_profiles WHERE user_id = $1',
      [userId]
    );

    if (restaurantResult.rows.length === 0) {
      return res.status(404).json({ error: 'Restaurant profile not found' });
    }

    const restaurantId = restaurantResult.rows[0].id;

    // Check if order exists and belongs to this restaurant
    const orderCheck = await query(
      'SELECT id, status FROM orders WHERE id = $1 AND restaurant_id = $2',
      [orderId, restaurantId]
    );

    if (orderCheck.rows.length === 0) {
      return res.status(404).json({ error: 'Order not found' });
    }

    const currentStatus = orderCheck.rows[0].status;

    // Only allow cancellation for pending or confirmed orders
    if (!['pending', 'confirmed'].includes(currentStatus)) {
      return res.status(400).json({
        error: 'Order cannot be cancelled at this stage'
      });
    }

    // Update order status to cancelled
    const result = await query(
      `UPDATE orders
       SET status = 'cancelled', updated_at = CURRENT_TIMESTAMP
       WHERE id = $1 AND restaurant_id = $2
       RETURNING *`,
      [orderId, restaurantId]
    );

    // Record the cancellation
    await query(
      `INSERT INTO order_cancellations (order_id, cancelled_by_type, cancelled_by_id, cancellation_reason)
       VALUES ($1, 'restaurant', $2, $3)`,
      [orderId, restaurantId, reason || 'Restaurant cancelled the order']
    );

    res.json({
      message: 'Order cancelled successfully',
      order: result.rows[0]
    });

  } catch (error) {
    console.error('Cancel order error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Update order status
router.patch('/orders/:orderId/status', authenticateToken, requireRole(['restaurant']), async (req, res) => {
  try {
    const { orderId } = req.params;
    const { status } = req.body;
    const userId = req.user.id;

    const validStatuses = ['pending', 'confirmed', 'preparing', 'ready', 'picked_up', 'delivered', 'cancelled'];
    if (!validStatuses.includes(status)) {
      return res.status(400).json({ error: 'Invalid status' });
    }

    // Get restaurant profile first
    const restaurantResult = await query(
      'SELECT id FROM restaurant_profiles WHERE user_id = $1',
      [userId]
    );

    if (restaurantResult.rows.length === 0) {
      return res.status(404).json({ error: 'Restaurant profile not found' });
    }

    const restaurantId = restaurantResult.rows[0].id;

    // Update order status
    const result = await query(
      `UPDATE orders 
       SET status = $1, updated_at = CURRENT_TIMESTAMP
       WHERE id = $2 AND restaurant_id = $3
       RETURNING *`,
      [status, orderId, restaurantId]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Order not found' });
    }

    res.json({
      message: 'Order status updated successfully',
      order: {
        id: result.rows[0].id,
        status: result.rows[0].status,
        updatedAt: result.rows[0].updated_at
      }
    });

  } catch (error) {
    console.error('Update order status error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Update restaurant image
router.patch('/:restaurantId/image',
  authenticateToken,
  requireRole(['restaurant']),
  async (req, res) => {
    try {
      const { restaurantId } = req.params;
      const { imageUrl, imageType = 'profile' } = req.body;

      if (!imageUrl) {
        return res.status(400).json({ error: 'Image URL is required' });
      }

      // Check if user owns this restaurant
      const restaurantCheck = await query(
        'SELECT id FROM restaurant_profiles WHERE id = $1 AND user_id = $2',
        [restaurantId, req.user.id]
      );

      if (restaurantCheck.rows.length === 0) {
        return res.status(403).json({ error: 'Access denied to this restaurant' });
      }

      // Update restaurant image
      const updatedRestaurant = await imageService.updateRestaurantImage(
        restaurantId,
        imageUrl,
        imageType
      );

      res.json({
        success: true,
        restaurant: updatedRestaurant,
        message: 'Restaurant image updated successfully'
      });

    } catch (error) {
      console.error('Restaurant image update error:', error);
      res.status(500).json({
        error: 'Failed to update restaurant image',
        message: error.message
      });
    }
  }
);

// Get restaurant images
router.get('/:restaurantId/images',
  authenticateToken,
  requireRole(['restaurant']),
  async (req, res) => {
    try {
      const { restaurantId } = req.params;

      // Check if user owns this restaurant
      const restaurantCheck = await query(
        'SELECT id FROM restaurant_profiles WHERE id = $1 AND user_id = $2',
        [restaurantId, req.user.id]
      );

      if (restaurantCheck.rows.length === 0) {
        return res.status(403).json({ error: 'Access denied to this restaurant' });
      }

      // Get all images for this restaurant
      const images = await imageService.getRestaurantImages(restaurantId);

      res.json({
        success: true,
        images
      });

    } catch (error) {
      console.error('Get restaurant images error:', error);
      res.status(500).json({
        error: 'Failed to get restaurant images',
        message: error.message
      });
    }
  }
);

module.exports = router;
