import 'package:flutter/foundation.dart';
import '../models/order.dart';
import '../services/api_service.dart';

class OrdersProvider with ChangeNotifier {
  List<Order> _orders = [];
  bool _isLoading = false;
  String? _error;
  bool _hasMore = true;
  int _currentOffset = 0;
  static const int _limit = 20;

  List<Order> get orders => _orders;
  bool get isLoading => _isLoading;
  String? get error => _error;
  bool get hasMore => _hasMore;

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String? error) {
    _error = error;
    notifyListeners();
  }

  // Load orders (with pagination)
  Future<void> loadOrders({bool refresh = false, String? status}) async {
    if (_isLoading) return;

    try {
      _setLoading(true);
      _setError(null);

      if (refresh) {
        _currentOffset = 0;
        _hasMore = true;
        _orders.clear();
      }

      final ordersData = await ApiService.getOrders(
        status: status,
        limit: _limit,
        offset: _currentOffset,
      );

      final newOrders = ordersData.map((orderJson) => Order.fromJson(orderJson)).toList();

      if (refresh) {
        _orders = newOrders;
      } else {
        _orders.addAll(newOrders);
      }

      // Check if there are more orders to load
      _hasMore = newOrders.length == _limit;
      _currentOffset += newOrders.length;

      notifyListeners();
    } catch (e) {
      _setError('Failed to load orders: $e');
    } finally {
      _setLoading(false);
    }
  }

  // Load more orders (pagination)
  Future<void> loadMoreOrders({String? status}) async {
    if (!_hasMore || _isLoading) return;
    await loadOrders(refresh: false, status: status);
  }

  // Refresh orders
  Future<void> refreshOrders({String? status}) async {
    await loadOrders(refresh: true, status: status);
  }

  // Get order details
  Future<OrderDetail> getOrderDetails(int orderId) async {
    try {
      _setError(null);
      final orderData = await ApiService.getOrderDetails(orderId);
      return OrderDetail.fromJson(orderData);
    } catch (e) {
      _setError('Failed to load order details: $e');
      rethrow;
    }
  }

  // Cancel order
  Future<void> cancelOrder(int orderId) async {
    try {
      _setError(null);
      await ApiService.cancelOrder(orderId);

      // Update the order status in the local list
      final orderIndex = _orders.indexWhere((order) => order.id == orderId);
      if (orderIndex != -1) {
        final updatedOrder = Order(
          id: _orders[orderIndex].id,
          orderNumber: _orders[orderIndex].orderNumber,
          status: 'cancelled',
          totalAmount: _orders[orderIndex].totalAmount,
          deliveryFee: _orders[orderIndex].deliveryFee,
          deliveryAddress: _orders[orderIndex].deliveryAddress,
          specialInstructions: _orders[orderIndex].specialInstructions,
          estimatedDeliveryTime: _orders[orderIndex].estimatedDeliveryTime,
          createdAt: _orders[orderIndex].createdAt,
          updatedAt: DateTime.now(),
          restaurant: _orders[orderIndex].restaurant,
          driver: _orders[orderIndex].driver,
          items: _orders[orderIndex].items,
        );

        _orders[orderIndex] = updatedOrder;
        notifyListeners();
      }
    } catch (e) {
      _setError('Failed to cancel order: $e');
      rethrow;
    }
  }

  // Get orders by status
  List<Order> getOrdersByStatus(String status) {
    return _orders.where((order) => order.status == status).toList();
  }

  // Get active orders (not delivered or cancelled)
  List<Order> get activeOrders {
    return _orders.where((order) => 
      !['delivered', 'cancelled'].contains(order.status)
    ).toList();
  }

  // Get completed orders (delivered or cancelled)
  List<Order> get completedOrders {
    return _orders.where((order) => 
      ['delivered', 'cancelled'].contains(order.status)
    ).toList();
  }

  // Find order by ID
  Order? findOrderById(int orderId) {
    try {
      return _orders.firstWhere((order) => order.id == orderId);
    } catch (e) {
      return null;
    }
  }

  // Clear orders
  void clearOrders() {
    _orders.clear();
    _currentOffset = 0;
    _hasMore = true;
    _setError(null);
    notifyListeners();
  }

  // Get order statistics
  Map<String, dynamic> getOrderStatistics() {
    final totalOrders = _orders.length;
    final activeOrdersCount = activeOrders.length;
    final completedOrdersCount = completedOrders.length;
    final cancelledOrdersCount = getOrdersByStatus('cancelled').length;
    
    final totalSpent = _orders
        .where((order) => order.status == 'delivered')
        .fold(0.0, (sum, order) => sum + order.totalAmount);

    return {
      'totalOrders': totalOrders,
      'activeOrders': activeOrdersCount,
      'completedOrders': completedOrdersCount,
      'cancelledOrders': cancelledOrdersCount,
      'totalSpent': totalSpent,
    };
  }

  // Update order status (for real-time updates)
  void updateOrderStatus(int orderId, String newStatus) {
    final orderIndex = _orders.indexWhere((order) => order.id == orderId);
    if (orderIndex != -1) {
      final order = _orders[orderIndex];
      final updatedOrder = Order(
        id: order.id,
        orderNumber: order.orderNumber,
        status: newStatus,
        totalAmount: order.totalAmount,
        deliveryFee: order.deliveryFee,
        deliveryAddress: order.deliveryAddress,
        specialInstructions: order.specialInstructions,
        estimatedDeliveryTime: order.estimatedDeliveryTime,
        createdAt: order.createdAt,
        updatedAt: DateTime.now(),
        restaurant: order.restaurant,
        driver: order.driver,
        items: order.items,
      );

      _orders[orderIndex] = updatedOrder;
      notifyListeners();
    }
  }

  // Add new order to the beginning of the list
  void addOrder(Order order) {
    _orders.insert(0, order);
    notifyListeners();
  }
}
