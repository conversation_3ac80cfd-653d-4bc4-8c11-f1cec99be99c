import 'package:flutter/foundation.dart';
import '../models/cart.dart';
import '../models/restaurant.dart';
import '../models/order.dart';
import '../services/api_service.dart';

class CartProvider with ChangeNotifier {
  Cart? _cart;
  bool _isLoading = false;
  String? _error;

  Cart? get cart => _cart;
  bool get isLoading => _isLoading;
  String? get error => _error;
  bool get hasItems => _cart != null && _cart!.isNotEmpty;
  int get itemCount => _cart?.itemCount ?? 0;
  double get total => _cart?.total ?? 0.0;

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String? error) {
    _error = error;
    notifyListeners();
  }

  // Add item to cart
  void addToCart(MenuItem menuItem, int restaurantId, String restaurantName, {String? specialInstructions}) {
    try {
      _setError(null);

      // If cart is empty or from different restaurant, create new cart
      if (_cart == null || _cart!.restaurantId != restaurantId) {
        _cart = Cart(
          restaurantId: restaurantId,
          restaurantName: restaurantName,
          items: [],
          deliveryFee: 5.0, // Default delivery fee
        );
      }

      // Check if item already exists in cart
      final existingItemIndex = _cart!.items.indexWhere(
        (item) => item.menuItemId == menuItem.id && item.specialInstructions == specialInstructions,
      );

      if (existingItemIndex != -1) {
        // Update quantity of existing item
        final existingItem = _cart!.items[existingItemIndex];
        _cart!.items[existingItemIndex] = existingItem.copyWith(
          quantity: existingItem.quantity + 1,
        );
      } else {
        // Add new item to cart
        final cartItem = CartItem.fromMenuItem(
          menuItem,
          quantity: 1,
          specialInstructions: specialInstructions,
        );
        _cart!.items.add(cartItem);
      }

      _cart = _cart!.copyWith(items: List.from(_cart!.items));
      notifyListeners();
    } catch (e) {
      _setError('Failed to add item to cart: $e');
    }
  }

  // Remove item from cart
  void removeFromCart(int menuItemId, {String? specialInstructions}) {
    try {
      _setError(null);

      if (_cart == null) return;

      final itemIndex = _cart!.items.indexWhere(
        (item) => item.menuItemId == menuItemId && item.specialInstructions == specialInstructions,
      );

      if (itemIndex != -1) {
        final item = _cart!.items[itemIndex];
        if (item.quantity > 1) {
          // Decrease quantity
          _cart!.items[itemIndex] = item.copyWith(quantity: item.quantity - 1);
        } else {
          // Remove item completely
          _cart!.items.removeAt(itemIndex);
        }

        // If cart is empty, clear it
        if (_cart!.items.isEmpty) {
          _cart = null;
        } else {
          _cart = _cart!.copyWith(items: List.from(_cart!.items));
        }

        notifyListeners();
      }
    } catch (e) {
      _setError('Failed to remove item from cart: $e');
    }
  }

  // Update item quantity
  void updateItemQuantity(int menuItemId, int newQuantity, {String? specialInstructions}) {
    try {
      _setError(null);

      if (_cart == null || newQuantity < 0) return;

      final itemIndex = _cart!.items.indexWhere(
        (item) => item.menuItemId == menuItemId && item.specialInstructions == specialInstructions,
      );

      if (itemIndex != -1) {
        if (newQuantity == 0) {
          // Remove item
          _cart!.items.removeAt(itemIndex);
          
          // If cart is empty, clear it
          if (_cart!.items.isEmpty) {
            _cart = null;
          } else {
            _cart = _cart!.copyWith(items: List.from(_cart!.items));
          }
        } else {
          // Update quantity
          final item = _cart!.items[itemIndex];
          _cart!.items[itemIndex] = item.copyWith(quantity: newQuantity);
          _cart = _cart!.copyWith(items: List.from(_cart!.items));
        }

        notifyListeners();
      }
    } catch (e) {
      _setError('Failed to update item quantity: $e');
    }
  }

  // Update item special instructions
  void updateItemInstructions(int menuItemId, String? oldInstructions, String? newInstructions) {
    try {
      _setError(null);

      if (_cart == null) return;

      final itemIndex = _cart!.items.indexWhere(
        (item) => item.menuItemId == menuItemId && item.specialInstructions == oldInstructions,
      );

      if (itemIndex != -1) {
        final item = _cart!.items[itemIndex];
        _cart!.items[itemIndex] = item.copyWith(specialInstructions: newInstructions);
        _cart = _cart!.copyWith(items: List.from(_cart!.items));
        notifyListeners();
      }
    } catch (e) {
      _setError('Failed to update item instructions: $e');
    }
  }

  // Get item quantity in cart
  int getItemQuantity(int menuItemId, {String? specialInstructions}) {
    if (_cart == null) return 0;

    final item = _cart!.items.firstWhere(
      (item) => item.menuItemId == menuItemId && item.specialInstructions == specialInstructions,
      orElse: () => CartItem(
        menuItemId: -1,
        name: '',
        description: '',
        price: 0,
        preparationTime: 0,
        quantity: 0,
      ),
    );

    return item.menuItemId == -1 ? 0 : item.quantity;
  }

  // Clear cart
  void clearCart() {
    _cart = null;
    _setError(null);
    notifyListeners();
  }

  // Update delivery fee
  void updateDeliveryFee(double fee) {
    if (_cart != null) {
      _cart = _cart!.copyWith(deliveryFee: fee);
      notifyListeners();
    }
  }

  // Create order from cart
  Future<Map<String, dynamic>> createOrder({
    required String deliveryAddress,
    required double deliveryLatitude,
    required double deliveryLongitude,
    String? specialInstructions,
  }) async {
    if (_cart == null || _cart!.isEmpty) {
      throw Exception('Cart is empty');
    }

    try {
      _setLoading(true);
      _setError(null);

      final orderData = await ApiService.createOrder(
        restaurantId: _cart!.restaurantId,
        items: _cart!.items.map((item) => item.toJson()).toList(),
        deliveryAddress: deliveryAddress,
        deliveryLatitude: deliveryLatitude,
        deliveryLongitude: deliveryLongitude,
        specialInstructions: specialInstructions,
        deliveryFee: _cart!.deliveryFee,
      );

      // Clear cart after successful order
      clearCart();

      return orderData;
    } catch (e) {
      _setError('Failed to create order: $e');
      rethrow;
    } finally {
      _setLoading(false);
    }
  }

  // Check if cart is from different restaurant
  bool isFromDifferentRestaurant(int restaurantId) {
    return _cart != null && _cart!.restaurantId != restaurantId;
  }

  // Get cart summary
  Map<String, dynamic> getCartSummary() {
    if (_cart == null) {
      return {
        'itemCount': 0,
        'subtotal': 0.0,
        'deliveryFee': 0.0,
        'total': 0.0,
      };
    }

    return {
      'itemCount': _cart!.itemCount,
      'subtotal': _cart!.subtotal,
      'deliveryFee': _cart!.deliveryFee,
      'total': _cart!.total,
    };
  }
}
