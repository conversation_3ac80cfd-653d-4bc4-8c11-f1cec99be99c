const express = require('express');
const bcrypt = require('bcryptjs');
const { query } = require('../config/database');
const { authenticateToken, requireRole } = require('../middleware/auth');
const { validateRequest, schemas } = require('../middleware/validation');

const router = express.Router();

// Create new restaurant (Admin only)
router.post('/restaurants', authenticateToken, requireRole(['admin']), async (req, res) => {
  try {
    const { 
      email, 
      password, 
      restaurantName, 
      description, 
      phone, 
      address, 
      latitude, 
      longitude, 
      cuisineType 
    } = req.body;

    // Validate required fields
    if (!email || !password || !restaurantName || !phone || !address || !cuisineType) {
      return res.status(400).json({ error: 'Missing required fields' });
    }

    // Check if user already exists
    const existingUser = await query('SELECT id FROM users WHERE email = $1', [email]);
    
    if (existingUser.rows.length > 0) {
      return res.status(400).json({ error: 'User already exists with this email' });
    }

    // Hash password
    const saltRounds = 12;
    const passwordHash = await bcrypt.hash(password, saltRounds);

    // Create user account
    const userResult = await query(
      'INSERT INTO users (email, password_hash, user_type, is_verified) VALUES ($1, $2, $3, $4) RETURNING id, email, user_type, created_at',
      [email, passwordHash, 'restaurant', true]
    );

    const user = userResult.rows[0];

    // Create restaurant profile
    const profileResult = await query(
      `INSERT INTO restaurant_profiles 
       (user_id, restaurant_name, description, phone, address, latitude, longitude, cuisine_type) 
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8) 
       RETURNING *`,
      [user.id, restaurantName, description, phone, address, latitude, longitude, cuisineType]
    );

    const profile = profileResult.rows[0];

    res.status(201).json({
      message: 'Restaurant created successfully',
      restaurant: {
        user: {
          id: user.id,
          email: user.email,
          userType: user.user_type,
          createdAt: user.created_at
        },
        profile: {
          id: profile.id,
          restaurantName: profile.restaurant_name,
          description: profile.description,
          phone: profile.phone,
          address: profile.address,
          latitude: profile.latitude,
          longitude: profile.longitude,
          cuisineType: profile.cuisine_type,
          isOpen: profile.is_open,
          rating: profile.rating,
          totalOrders: profile.total_orders,
          createdAt: profile.created_at
        }
      }
    });

  } catch (error) {
    console.error('Create restaurant error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Get all restaurants (Admin only)
router.get('/restaurants', authenticateToken, requireRole(['admin']), async (req, res) => {
  try {
    const result = await query(`
      SELECT 
        u.id as user_id, u.email, u.is_active, u.created_at as user_created_at,
        rp.id as profile_id, rp.restaurant_name, rp.description, rp.phone, 
        rp.address, rp.latitude, rp.longitude, rp.cuisine_type, rp.is_open,
        rp.rating, rp.total_orders, rp.created_at as profile_created_at
      FROM users u
      JOIN restaurant_profiles rp ON u.id = rp.user_id
      WHERE u.user_type = 'restaurant'
      ORDER BY rp.created_at DESC
    `);

    const restaurants = result.rows.map(row => ({
      user: {
        id: row.user_id,
        email: row.email,
        isActive: row.is_active,
        createdAt: row.user_created_at
      },
      profile: {
        id: row.profile_id,
        restaurantName: row.restaurant_name,
        description: row.description,
        phone: row.phone,
        address: row.address,
        latitude: row.latitude,
        longitude: row.longitude,
        cuisineType: row.cuisine_type,
        isOpen: row.is_open,
        rating: parseFloat(row.rating),
        totalOrders: row.total_orders,
        createdAt: row.profile_created_at
      }
    }));

    res.json({ restaurants });

  } catch (error) {
    console.error('Get restaurants error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Create new driver (Admin only)
router.post('/drivers', authenticateToken, requireRole(['admin']), async (req, res) => {
  try {
    const { 
      email, 
      password, 
      fullName, 
      phone, 
      licenseNumber, 
      vehicleType, 
      vehiclePlate,
      vehicleColor 
    } = req.body;

    // Validate required fields
    if (!email || !password || !fullName || !phone || !licenseNumber || !vehicleType || !vehiclePlate) {
      return res.status(400).json({ error: 'Missing required fields' });
    }

    // Check if user already exists
    const existingUser = await query('SELECT id FROM users WHERE email = $1', [email]);
    
    if (existingUser.rows.length > 0) {
      return res.status(400).json({ error: 'User already exists with this email' });
    }

    // Hash password
    const saltRounds = 12;
    const passwordHash = await bcrypt.hash(password, saltRounds);

    // Create user account
    const userResult = await query(
      'INSERT INTO users (email, password_hash, user_type, is_verified) VALUES ($1, $2, $3, $4) RETURNING id, email, user_type, created_at',
      [email, passwordHash, 'driver', true]
    );

    const user = userResult.rows[0];

    // Create driver profile
    const profileResult = await query(
      `INSERT INTO driver_profiles 
       (user_id, full_name, phone, license_number, vehicle_type, vehicle_plate) 
       VALUES ($1, $2, $3, $4, $5, $6) 
       RETURNING *`,
      [user.id, fullName, phone, licenseNumber, vehicleType, vehiclePlate]
    );

    const profile = profileResult.rows[0];

    res.status(201).json({
      message: 'Driver created successfully',
      driver: {
        user: {
          id: user.id,
          email: user.email,
          userType: user.user_type,
          createdAt: user.created_at
        },
        profile: {
          id: profile.id,
          fullName: profile.full_name,
          phone: profile.phone,
          licenseNumber: profile.license_number,
          vehicleType: profile.vehicle_type,
          vehiclePlate: profile.vehicle_plate,
          vehicleColor: null, // Column doesn't exist in current schema
          isAvailable: profile.is_available,
          currentLatitude: profile.current_latitude,
          currentLongitude: profile.current_longitude,
          rating: profile.rating || 0,
          totalDeliveries: profile.total_deliveries || 0,
          totalEarnings: 0, // Column doesn't exist in current schema
          createdAt: profile.created_at
        }
      }
    });

  } catch (error) {
    console.error('Create driver error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Get all drivers (Admin only)
router.get('/drivers', authenticateToken, requireRole(['admin']), async (req, res) => {
  try {
    const result = await query(`
      SELECT 
        u.id as user_id, u.email, u.is_active, u.created_at as user_created_at,
        dp.id as profile_id, dp.full_name, dp.phone, dp.license_number,
        dp.vehicle_type, dp.vehicle_plate, dp.is_available,
        dp.current_latitude, dp.current_longitude, dp.rating, dp.total_deliveries,
        dp.created_at as profile_created_at
      FROM users u
      JOIN driver_profiles dp ON u.id = dp.user_id
      WHERE u.user_type = 'driver'
      ORDER BY dp.created_at DESC
    `);

    const drivers = result.rows.map(row => ({
      user: {
        id: row.user_id,
        email: row.email,
        isActive: row.is_active,
        createdAt: row.user_created_at
      },
      profile: {
        id: row.profile_id,
        fullName: row.full_name,
        phone: row.phone,
        licenseNumber: row.license_number,
        vehicleType: row.vehicle_type,
        vehiclePlate: row.vehicle_plate,
        vehicleColor: null, // Column doesn't exist in current schema
        isAvailable: row.is_available,
        currentLatitude: row.current_latitude,
        currentLongitude: row.current_longitude,
        rating: parseFloat(row.rating || 0),
        totalDeliveries: row.total_deliveries || 0,
        totalEarnings: 0, // Column doesn't exist in current schema
        createdAt: row.profile_created_at
      }
    }));

    res.json({ drivers });

  } catch (error) {
    console.error('Get drivers error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Get all customers (Admin only)
router.get('/customers', authenticateToken, requireRole(['admin']), async (req, res) => {
  try {
    const result = await query(`
      SELECT 
        u.id as user_id, u.email, u.is_active, u.created_at as user_created_at,
        cp.id as profile_id, cp.full_name, cp.phone, cp.address, 
        cp.latitude, cp.longitude, cp.profile_completed, cp.created_at as profile_created_at
      FROM users u
      LEFT JOIN customer_profiles cp ON u.id = cp.user_id
      WHERE u.user_type = 'customer'
      ORDER BY u.created_at DESC
    `);

    const customers = result.rows.map(row => ({
      user: {
        id: row.user_id,
        email: row.email,
        isActive: row.is_active,
        createdAt: row.user_created_at
      },
      profile: row.profile_id ? {
        id: row.profile_id,
        fullName: row.full_name,
        phone: row.phone,
        address: row.address,
        latitude: row.latitude,
        longitude: row.longitude,
        profileCompleted: row.profile_completed,
        createdAt: row.profile_created_at
      } : null
    }));

    res.json({ customers });

  } catch (error) {
    console.error('Get customers error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Get system statistics (Admin only)
router.get('/stats', authenticateToken, requireRole(['admin']), async (req, res) => {
  try {
    // Get counts
    const customerCount = await query('SELECT COUNT(*) FROM users WHERE user_type = $1', ['customer']);
    const restaurantCount = await query('SELECT COUNT(*) FROM users WHERE user_type = $1', ['restaurant']);
    const driverCount = await query('SELECT COUNT(*) FROM users WHERE user_type = $1', ['driver']);
    
    // Get order count (handle if orders table doesn't exist or is empty)
    let orderCount;
    try {
      orderCount = await query('SELECT COUNT(*) FROM orders');
    } catch (orderError) {
      console.log('Orders table not found or error:', orderError.message);
      orderCount = { rows: [{ count: '0' }] };
    }
    
    // Get recent orders (handle if no orders exist)
    let recentOrders;
    try {
      recentOrders = await query(`
        SELECT o.id, o.order_number, o.status, o.total_amount, o.created_at,
               rp.restaurant_name, cp.full_name as customer_name
        FROM orders o
        LEFT JOIN restaurant_profiles rp ON o.restaurant_id = rp.id
        LEFT JOIN customer_profiles cp ON o.customer_id = cp.id
        ORDER BY o.created_at DESC
        LIMIT 10
      `);
    } catch (ordersError) {
      console.log('Error fetching recent orders:', ordersError.message);
      recentOrders = { rows: [] };
    }

    res.json({
      stats: {
        totalCustomers: parseInt(customerCount.rows[0].count),
        totalRestaurants: parseInt(restaurantCount.rows[0].count),
        totalDrivers: parseInt(driverCount.rows[0].count),
        totalOrders: parseInt(orderCount.rows[0].count)
      },
      recentOrders: recentOrders.rows.map(order => ({
        id: order.id,
        orderNumber: order.order_number,
        status: order.status,
        totalAmount: parseFloat(order.total_amount || 0),
        restaurantName: order.restaurant_name || 'Unknown Restaurant',
        customerName: order.customer_name || 'Unknown Customer',
        createdAt: order.created_at
      }))
    });

  } catch (error) {
    console.error('Get stats error:', error);
    res.status(500).json({ error: 'Internal server error', details: error.message });
  }
});

// Get all menu items for a restaurant (Admin only)
router.get('/restaurants/:restaurantId/menu', authenticateToken, requireRole(['admin']), async (req, res) => {
  try {
    const { restaurantId } = req.params;

    // Get categories and menu items
    const categoriesResult = await query(
      'SELECT * FROM categories WHERE restaurant_id = $1 ORDER BY name',
      [restaurantId]
    );

    const menuItemsResult = await query(
      'SELECT * FROM menu_items WHERE restaurant_id = $1 ORDER BY category_id, name',
      [restaurantId]
    );

    const categories = categoriesResult.rows;
    const menuItems = menuItemsResult.rows;

    // Group menu items by category
    const categoriesWithItems = categories.map(category => ({
      ...category,
      items: menuItems.filter(item => item.category_id === category.id)
    }));

    // Add uncategorized items
    const uncategorizedItems = menuItems.filter(item => !item.category_id);
    if (uncategorizedItems.length > 0) {
      categoriesWithItems.push({
        id: null,
        name: 'Uncategorized',
        description: 'Items without a category',
        is_active: true,
        items: uncategorizedItems
      });
    }

    res.json({ categories: categoriesWithItems });

  } catch (error) {
    console.error('Get menu error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Create category for restaurant (Admin only)
router.post('/restaurants/:restaurantId/categories', authenticateToken, requireRole(['admin']), async (req, res) => {
  try {
    const { restaurantId } = req.params;
    const { name, description } = req.body;

    if (!name) {
      return res.status(400).json({ error: 'Category name is required' });
    }

    const result = await query(
      'INSERT INTO categories (restaurant_id, name, description) VALUES ($1, $2, $3) RETURNING *',
      [restaurantId, name, description]
    );

    res.status(201).json({ category: result.rows[0] });

  } catch (error) {
    console.error('Create category error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Create menu item for restaurant (Admin only)
router.post('/restaurants/:restaurantId/menu-items', authenticateToken, requireRole(['admin']), async (req, res) => {
  try {
    const { restaurantId } = req.params;
    const { name, description, price, categoryId, imageUrl, preparationTime, isAvailable } = req.body;

    if (!name || !price) {
      return res.status(400).json({ error: 'Name and price are required' });
    }

    const result = await query(
      `INSERT INTO menu_items 
       (restaurant_id, category_id, name, description, price, image_url, preparation_time, is_available) 
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8) RETURNING *`,
      [restaurantId, categoryId || null, name, description, parseFloat(price), imageUrl, preparationTime || 15, isAvailable !== false]
    );

    res.status(201).json({ menuItem: result.rows[0] });

  } catch (error) {
    console.error('Create menu item error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Update menu item (Admin only)
router.patch('/menu-items/:itemId', authenticateToken, requireRole(['admin']), async (req, res) => {
  try {
    const { itemId } = req.params;
    const { name, description, price, categoryId, imageUrl, preparationTime, isAvailable } = req.body;

    const updates = [];
    const values = [];
    let paramCount = 1;

    if (name !== undefined) {
      updates.push(`name = $${paramCount++}`);
      values.push(name);
    }
    if (description !== undefined) {
      updates.push(`description = $${paramCount++}`);
      values.push(description);
    }
    if (price !== undefined) {
      updates.push(`price = $${paramCount++}`);
      values.push(parseFloat(price));
    }
    if (categoryId !== undefined) {
      updates.push(`category_id = $${paramCount++}`);
      values.push(categoryId || null);
    }
    if (imageUrl !== undefined) {
      updates.push(`image_url = $${paramCount++}`);
      values.push(imageUrl);
    }
    if (preparationTime !== undefined) {
      updates.push(`preparation_time = $${paramCount++}`);
      values.push(preparationTime);
    }
    if (isAvailable !== undefined) {
      updates.push(`is_available = $${paramCount++}`);
      values.push(isAvailable);
    }

    if (updates.length === 0) {
      return res.status(400).json({ error: 'No fields to update' });
    }

    updates.push(`updated_at = CURRENT_TIMESTAMP`);
    values.push(itemId);

    const result = await query(
      `UPDATE menu_items SET ${updates.join(', ')} WHERE id = $${paramCount} RETURNING *`,
      values
    );

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Menu item not found' });
    }

    res.json({ menuItem: result.rows[0] });

  } catch (error) {
    console.error('Update menu item error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Delete menu item (Admin only)
router.delete('/menu-items/:itemId', authenticateToken, requireRole(['admin']), async (req, res) => {
  try {
    const { itemId } = req.params;

    const result = await query(
      'DELETE FROM menu_items WHERE id = $1 RETURNING *',
      [itemId]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Menu item not found' });
    }

    res.json({ message: 'Menu item deleted successfully' });

  } catch (error) {
    console.error('Delete menu item error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Toggle user active status (Admin only)
router.patch('/users/:userId/status', authenticateToken, requireRole(['admin']), async (req, res) => {
  try {
    const { userId } = req.params;
    const { isActive } = req.body;

    if (typeof isActive !== 'boolean') {
      return res.status(400).json({ error: 'isActive must be a boolean value' });
    }

    const result = await query(
      'UPDATE users SET is_active = $1 WHERE id = $2 RETURNING id, email, user_type, is_active',
      [isActive, userId]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'User not found' });
    }

    res.json({
      message: `User ${isActive ? 'activated' : 'deactivated'} successfully`,
      user: result.rows[0]
    });

  } catch (error) {
    console.error('Update user status error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Get all orders with filtering and search (Admin only)
router.get('/orders', authenticateToken, requireRole(['admin']), async (req, res) => {
  try {
    const { status, search, page = 1, limit = 50 } = req.query;
    const offset = (page - 1) * limit;

    let whereClause = '';
    let queryParams = [];
    let paramCount = 0;

    if (status && status !== 'undefined') {
      paramCount++;
      whereClause += `WHERE o.status = $${paramCount}`;
      queryParams.push(status);
    }

    if (search) {
      paramCount++;
      const searchCondition = status ? ' AND ' : 'WHERE ';
      whereClause += `${searchCondition}(cp.full_name ILIKE $${paramCount} OR rp.restaurant_name ILIKE $${paramCount + 1} OR o.id::text ILIKE $${paramCount + 2})`;
      queryParams.push(`%${search}%`, `%${search}%`, `%${search}%`);
      paramCount += 2;
    }

    const ordersQuery = `
      SELECT 
        o.id,
        o.status,
        o.total_amount,
        o.delivery_address,
        o.special_instructions,
        o.estimated_delivery_time,
        o.created_at,
        cp.full_name as customer_name,
        cu.email as customer_email,
        rp.restaurant_name,
        COUNT(*) OVER() as total_count
      FROM orders o
      JOIN customer_profiles cp ON o.customer_id = cp.id
      JOIN users cu ON cp.user_id = cu.id
      JOIN restaurant_profiles rp ON o.restaurant_id = rp.id
      ${whereClause}
      ORDER BY o.created_at DESC
      LIMIT $${paramCount + 1} OFFSET $${paramCount + 2}
    `;

    queryParams.push(limit, offset);
    const result = await query(ordersQuery, queryParams);

    const orders = result.rows;
    const totalCount = orders.length > 0 ? parseInt(orders[0].total_count) : 0;

    // Get order items for each order
    for (let order of orders) {
      const itemsResult = await query(`
        SELECT 
          oi.quantity,
          oi.unit_price AS price,
          oi.special_instructions,
          mi.name
        FROM order_items oi
        JOIN menu_items mi ON oi.menu_item_id = mi.id
        WHERE oi.order_id = $1
      `, [order.id]);
      
      order.items = itemsResult.rows;
    }

    res.json({
      orders,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: totalCount,
        pages: Math.ceil(totalCount / limit)
      }
    });
  } catch (error) {
    console.error('Get orders error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Get order details (Admin only)
router.get('/orders/:orderId', authenticateToken, requireRole(['admin']), async (req, res) => {
  try {
    const { orderId } = req.params;

    const orderResult = await query(`
      SELECT 
        o.*,
        cp.full_name as customer_name,
        cp.phone as customer_phone,
        cu.email as customer_email,
        rp.restaurant_name,
        rp.phone as restaurant_phone,
        dp.full_name as driver_name,
        dp.phone as driver_phone
      FROM orders o
      JOIN customer_profiles cp ON o.customer_id = cp.id
      JOIN users cu ON cp.user_id = cu.id
      JOIN restaurant_profiles rp ON o.restaurant_id = rp.id
      LEFT JOIN driver_profiles dp ON o.driver_id = dp.user_id
      WHERE o.id = $1
    `, [orderId]);

    if (orderResult.rows.length === 0) {
      return res.status(404).json({ error: 'Order not found' });
    }

    const order = orderResult.rows[0];

    // Get order items
    const itemsResult = await query(`
      SELECT 
        oi.quantity,
        oi.unit_price AS price,
        oi.special_instructions,
        mi.name,
        mi.description
      FROM order_items oi
      JOIN menu_items mi ON oi.menu_item_id = mi.id
      WHERE oi.order_id = $1
    `, [orderId]);

    order.items = itemsResult.rows;

    res.json(order);
  } catch (error) {
    console.error('Get order details error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Update order status (Admin only)
router.patch('/orders/:orderId/status', authenticateToken, requireRole(['admin']), async (req, res) => {
  try {
    const { orderId } = req.params;
    const { status } = req.body;

    const validStatuses = ['pending', 'confirmed', 'preparing', 'ready', 'picked_up', 'delivered', 'cancelled'];
    
    if (!status || !validStatuses.includes(status)) {
      return res.status(400).json({ error: 'Invalid status' });
    }

    // Check if order exists
    const orderCheck = await query('SELECT id, status FROM orders WHERE id = $1', [orderId]);
    
    if (orderCheck.rows.length === 0) {
      return res.status(404).json({ error: 'Order not found' });
    }

    // Update order status
    const result = await query(
      'UPDATE orders SET status = $1, updated_at = CURRENT_TIMESTAMP WHERE id = $2 RETURNING *',
      [status, orderId]
    );

    res.json({
      message: 'Order status updated successfully',
      order: result.rows[0]
    });
  } catch (error) {
    console.error('Update order status error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Get order analytics/stats (Admin only)
router.get('/analytics/orders', authenticateToken, requireRole(['admin']), async (req, res) => {
  try {
    const { period = '7d' } = req.query;
    
    let dateFilter = '';
    if (period === '7d') {
      dateFilter = "WHERE o.created_at >= CURRENT_DATE - INTERVAL '7 days'";
    } else if (period === '30d') {
      dateFilter = "WHERE o.created_at >= CURRENT_DATE - INTERVAL '30 days'";
    } else if (period === '90d') {
      dateFilter = "WHERE o.created_at >= CURRENT_DATE - INTERVAL '90 days'";
    }

    // Total orders and revenue
    const totalStats = await query(`
      SELECT 
        COUNT(*) as total_orders,
        COALESCE(SUM(total_amount), 0) as total_revenue,
        COUNT(CASE WHEN status = 'delivered' THEN 1 END) as completed_orders,
        COUNT(CASE WHEN status = 'cancelled' THEN 1 END) as cancelled_orders
      FROM orders o
      ${dateFilter}
    `);

    // Orders by status
    const statusStats = await query(`
      SELECT 
        status,
        COUNT(*) as count
      FROM orders o
      ${dateFilter}
      GROUP BY status
      ORDER BY count DESC
    `);

    // Daily order trends
    const trendStats = await query(`
      SELECT 
        DATE(created_at) as date,
        COUNT(*) as orders,
        SUM(total_amount) as revenue
      FROM orders o
      ${dateFilter}
      GROUP BY DATE(created_at)
      ORDER BY date DESC
      LIMIT 30
    `);

    // Top restaurants by orders
    const topRestaurants = await query(`
      SELECT 
        rp.restaurant_name,
        COUNT(*) as order_count,
        SUM(o.total_amount) as revenue
      FROM orders o
      JOIN restaurant_profiles rp ON o.restaurant_id = rp.id
      ${dateFilter}
      GROUP BY rp.restaurant_name, rp.user_id
      ORDER BY order_count DESC
      LIMIT 10
    `);

    res.json({
      totalStats: totalStats.rows[0],
      statusStats: statusStats.rows,
      trendStats: trendStats.rows,
      topRestaurants: topRestaurants.rows
    });
  } catch (error) {
    console.error('Get order analytics error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Get rating analytics (Admin only)
router.get('/analytics/ratings', authenticateToken, requireRole(['admin']), async (req, res) => {
  try {
    // Get overall food rating statistics
    const foodRatingStats = await query(`
      SELECT
        AVG(fr.rating) as average_rating,
        COUNT(fr.id) as total_ratings
      FROM food_ratings fr
    `);

    // Get overall driver rating statistics
    const driverRatingStats = await query(`
      SELECT
        AVG(dr.rating) as average_rating,
        COUNT(dr.id) as total_ratings
      FROM driver_ratings dr
    `);

    // Get top rated food items
    const topRatedItems = await query(`
      SELECT
        mi.id, mi.name, mi.average_rating, mi.total_ratings,
        rp.restaurant_name
      FROM menu_items mi
      JOIN restaurant_profiles rp ON mi.restaurant_id = rp.id
      WHERE mi.total_ratings >= 3
      ORDER BY mi.average_rating DESC, mi.total_ratings DESC
      LIMIT 10
    `);

    // Get low rated food items (items that need improvement)
    const lowRatedItems = await query(`
      SELECT
        mi.id, mi.name, mi.average_rating, mi.total_ratings,
        rp.restaurant_name
      FROM menu_items mi
      JOIN restaurant_profiles rp ON mi.restaurant_id = rp.id
      WHERE mi.total_ratings >= 3 AND mi.average_rating < 3.5
      ORDER BY mi.average_rating ASC, mi.total_ratings DESC
      LIMIT 10
    `);

    // Get top rated drivers
    const topRatedDrivers = await query(`
      SELECT
        dp.id, dp.full_name, dp.rating,
        COUNT(dr.id) as total_ratings
      FROM driver_profiles dp
      LEFT JOIN driver_ratings dr ON dp.id = dr.driver_id
      WHERE dp.rating IS NOT NULL
      GROUP BY dp.id, dp.full_name, dp.rating
      HAVING COUNT(dr.id) >= 3
      ORDER BY dp.rating DESC, COUNT(dr.id) DESC
      LIMIT 10
    `);

    // Calculate overall rating (combination of food and driver ratings)
    const foodAvg = parseFloat(foodRatingStats.rows[0]?.average_rating) || 0;
    const driverAvg = parseFloat(driverRatingStats.rows[0]?.average_rating) || 0;
    const overallRating = (foodAvg + driverAvg) / 2;

    res.json({
      averageFoodRating: foodAvg,
      totalFoodRatings: parseInt(foodRatingStats.rows[0]?.total_ratings) || 0,
      averageDriverRating: driverAvg,
      totalDriverRatings: parseInt(driverRatingStats.rows[0]?.total_ratings) || 0,
      overallRating: overallRating,
      topRatedItems: topRatedItems.rows.map(item => ({
        id: item.id,
        name: item.name,
        average_rating: parseFloat(item.average_rating),
        total_ratings: item.total_ratings,
        restaurant_name: item.restaurant_name
      })),
      lowRatedItems: lowRatedItems.rows.map(item => ({
        id: item.id,
        name: item.name,
        average_rating: parseFloat(item.average_rating),
        total_ratings: item.total_ratings,
        restaurant_name: item.restaurant_name
      })),
      topRatedDrivers: topRatedDrivers.rows.map(driver => ({
        id: driver.id,
        full_name: driver.full_name,
        rating: parseFloat(driver.rating),
        total_ratings: parseInt(driver.total_ratings)
      }))
    });

  } catch (error) {
    console.error('Get rating analytics error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Get admin settings
router.get('/settings', authenticateToken, requireRole(['admin']), async (req, res) => {
  try {
    const settings = await query('SELECT * FROM admin_settings ORDER BY setting_key');

    const settingsObj = {};
    settings.rows.forEach(setting => {
      settingsObj[setting.setting_key] = {
        value: setting.setting_value,
        description: setting.description,
        updatedAt: setting.updated_at
      };
    });

    res.json({ settings: settingsObj });
  } catch (error) {
    console.error('Get admin settings error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Update admin setting
router.put('/settings/:key', authenticateToken, requireRole(['admin']), async (req, res) => {
  try {
    const { key } = req.params;
    const { value } = req.body;

    if (!value) {
      return res.status(400).json({ error: 'Setting value is required' });
    }

    const result = await query(
      `UPDATE admin_settings
       SET setting_value = $1, updated_at = CURRENT_TIMESTAMP
       WHERE setting_key = $2
       RETURNING *`,
      [value, key]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Setting not found' });
    }

    res.json({
      message: 'Setting updated successfully',
      setting: {
        key: result.rows[0].setting_key,
        value: result.rows[0].setting_value,
        description: result.rows[0].description,
        updatedAt: result.rows[0].updated_at
      }
    });
  } catch (error) {
    console.error('Update admin setting error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

module.exports = router;
