const http = require('http');

function testRatingEndpoints() {
  console.log('🧪 Testing Rating API endpoints...\n');

  // Test food rating endpoint
  const foodRatingData = JSON.stringify({
    menuItemId: 1,
    orderId: 1,
    rating: 5,
    reviewText: "Great food!"
  });

  const driverRatingData = JSON.stringify({
    driverId: 1,
    orderId: 1,
    rating: 4,
    reviewText: "Good service!"
  });

  // Test food rating
  console.log('📝 Testing food rating endpoint...');
  console.log('Request body:', foodRatingData);
  
  const foodOptions = {
    hostname: 'localhost',
    port: 3000,
    path: '/api/ratings/food',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer fake-token-for-testing'
    }
  };

  const foodReq = http.request(foodOptions, (res) => {
    let data = '';
    res.on('data', (chunk) => { data += chunk; });
    res.on('end', () => {
      console.log(`📡 Food Rating Response Status: ${res.statusCode}`);
      console.log('📄 Response:', data);
      console.log('');
      
      // Test driver rating
      console.log('📝 Testing driver rating endpoint...');
      console.log('Request body:', driverRatingData);
      
      const driverOptions = {
        hostname: 'localhost',
        port: 3000,
        path: '/api/ratings/driver',
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer fake-token-for-testing'
        }
      };

      const driverReq = http.request(driverOptions, (res) => {
        let data = '';
        res.on('data', (chunk) => { data += chunk; });
        res.on('end', () => {
          console.log(`📡 Driver Rating Response Status: ${res.statusCode}`);
          console.log('📄 Response:', data);
        });
      });

      driverReq.on('error', (error) => {
        console.error('❌ Driver rating request error:', error);
      });

      driverReq.write(driverRatingData);
      driverReq.end();
    });
  });

  foodReq.on('error', (error) => {
    console.error('❌ Food rating request error:', error);
  });

  foodReq.write(foodRatingData);
  foodReq.end();
}

// Wait a moment for server to be ready, then test
setTimeout(testRatingEndpoints, 1000);
