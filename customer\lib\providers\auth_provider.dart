import 'package:flutter/widgets.dart';
import '../models/user.dart';
import '../services/api_service.dart';

class AuthProvider with ChangeNotifier {
  User? _user;
  CustomerProfile? _profile;
  bool _isLoading = false;
  String? _error;
  bool _isGuestMode = false;
  String? _pendingAction; // Store action to perform after login

  User? get user => _user;
  CustomerProfile? get profile => _profile;
  bool get isLoading => _isLoading;
  String? get error => _error;
  bool get isAuthenticated => _user != null;
  bool get isGuestMode => _isGuestMode;
  String? get pendingAction => _pendingAction;
  // Profile is considered completed if we have any profile object.
  // This avoids relying on a backend flag that might be missing.
  bool get isProfileCompleted => _profile != null;

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String? error) {
    _error = error;
    notifyListeners();
  }

  Future<bool> register({
    required String email,
    required String password,
  }) async {
    try {
      _setLoading(true);
      _setError(null);

      final response = await ApiService.register(
        email: email,
        password: password,
      );

      _user = User.fromJson(response['user']);
      _profile = null; // Profile not completed yet

      notifyListeners();
      return true;
    } catch (e) {
      _setError(e.toString().replaceFirst('Exception: ', ''));
      return false;
    } finally {
      _setLoading(false);
    }
  }

  Future<bool> login({required String email, required String password}) async {
    try {
      _setLoading(true);
      _setError(null);

      final response = await ApiService.login(email: email, password: password);

      _user = User.fromJson(response['user']);

      if (response['profile'] != null) {
        _profile = CustomerProfile.fromJson(response['profile']);
      } else if (response['profileCompleted'] == true) {
        // Server indicates profile is already complete.
        _profile = CustomerProfile(profileCompleted: true);
      }

      notifyListeners();
      return true;
    } catch (e) {
      _setError(e.toString().replaceFirst('Exception: ', ''));
      return false;
    } finally {
      _setLoading(false);
    }
  }

  Future<bool> completeProfile({
    required String fullName,
    required String phone,
    required String address,
    required double latitude,
    required double longitude,
  }) async {
    try {
      _setLoading(true);
      _setError(null);

      final response = await ApiService.completeProfile(
        fullName: fullName,
        phone: phone,
        address: address,
        latitude: latitude,
        longitude: longitude,
      );

      _profile = CustomerProfile.fromJson(response['profile']);

      notifyListeners();
      return true;
    } catch (e) {
      _setError(e.toString().replaceFirst('Exception: ', ''));
      return false;
    } finally {
      _setLoading(false);
    }
  }

  Future<bool> updateLocation({
    required double latitude,
    required double longitude,
    String? address,
  }) async {
    try {
      _setLoading(true);
      _setError(null);

      await ApiService.updateLocation(
        latitude: latitude,
        longitude: longitude,
        address: address,
      );

      // Update local profile
      if (_profile != null) {
        _profile = CustomerProfile(
          id: _profile!.id,
          fullName: _profile!.fullName,
          phone: _profile!.phone,
          address: address ?? _profile!.address,
          latitude: latitude,
          longitude: longitude,
          profileCompleted: _profile!.profileCompleted,
        );
      }

      notifyListeners();
      return true;
    } catch (e) {
      _setError(e.toString().replaceFirst('Exception: ', ''));
      return false;
    } finally {
      _setLoading(false);
    }
  }

  Future<void> loadCurrentUser() async {
    try {
      _setLoading(true);
      _setError(null);

      final response = await ApiService.getCurrentUser();

      _user = User.fromJson(response['user']);

      if (response['profile'] != null) {
        _profile = CustomerProfile.fromJson(response['profile']);
      } else if (response['profileCompleted'] == true) {
        // Server indicates profile is already complete.
        _profile = CustomerProfile(profileCompleted: true);
      }

      notifyListeners();
    } catch (e) {
      _setError(e.toString().replaceFirst('Exception: ', ''));
      // If token is invalid, logout
      await logout();
    } finally {
      _setLoading(false);
    }
  }

  Future<void> logout() async {
    await ApiService.logout();
    _user = null;
    _profile = null;
    _error = null;
    notifyListeners();
  }

  void clearError() {
    _setError(null);
  }

  // Guest mode methods
  void enableGuestMode() {
    if (!_isGuestMode) {
      _isGuestMode = true;
      _user = null;
      _profile = null;
      // Use post frame callback to avoid setState during build
      WidgetsBinding.instance.addPostFrameCallback((_) {
        notifyListeners();
      });
    }
  }

  void disableGuestMode() {
    _isGuestMode = false;
    notifyListeners();
  }

  void setPendingAction(String action) {
    _pendingAction = action;
    notifyListeners();
  }

  void clearPendingAction() {
    _pendingAction = null;
    notifyListeners();
  }

  // Check if user needs to login for a specific action
  bool requiresAuthentication(String action) {
    const authenticatedActions = [
      'add_to_cart',
      'checkout',
      'view_orders',
      'manage_addresses',
      'view_profile',
    ];
    return authenticatedActions.contains(action);
  }

  // Handle action that requires authentication
  void handleAuthenticatedAction(String action) {
    if (isAuthenticated) {
      // User is already authenticated, perform action
      _performAction(action);
    } else {
      // Store action and redirect to login
      setPendingAction(action);
    }
  }

  // Perform pending action after successful login
  void performPendingAction() {
    if (_pendingAction != null) {
      final action = _pendingAction!;
      clearPendingAction();
      _performAction(action);
    }
  }

  void _performAction(String action) {
    // This method will be called by the UI to handle specific actions
    // The actual implementation will be in the respective screens
    notifyListeners();
  }

  // Force refresh authentication state
  void refreshAuthState() {
    notifyListeners();
  }

  // Check current authentication status
  void checkCurrentAuthStatus() {
    // This method can be used for debugging if needed
  }
}
