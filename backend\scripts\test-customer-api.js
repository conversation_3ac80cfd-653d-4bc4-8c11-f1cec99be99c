const http = require('http');

function testCustomerAPI() {
  console.log('🧪 Testing Customer API endpoints...\n');

  // Test public restaurants endpoint (guest access)
  const options = {
    hostname: 'localhost',
    port: 3000,
    path: '/api/customer/public/restaurants',
    method: 'GET',
    headers: {
      'Content-Type': 'application/json'
    }
  };

  const req = http.request(options, (res) => {
    let data = '';

    res.on('data', (chunk) => {
      data += chunk;
    });

    res.on('end', () => {
      try {
        const response = JSON.parse(data);
        console.log(`📡 API Response Status: ${res.statusCode}`);
        console.log(`📊 Restaurants returned: ${response.restaurants?.length || 0}`);
        
        if (response.restaurants) {
          console.log('\n🏪 Restaurants from API:');
          response.restaurants.forEach((r, index) => {
            console.log(`  ${index + 1}. ${r.name} (ID: ${r.id}) - ${r.isOpen ? '🟢 OPEN' : '🔴 CLOSED'}`);
          });
        } else {
          console.log('❌ No restaurants array in response');
          console.log('Response:', JSON.stringify(response, null, 2));
        }
      } catch (error) {
        console.error('❌ Error parsing response:', error);
        console.log('Raw response:', data);
      }
    });
  });

  req.on('error', (error) => {
    console.error('❌ Request error:', error);
  });

  req.end();
}

// Wait a moment for server to be ready, then test
setTimeout(testCustomerAPI, 1000);
