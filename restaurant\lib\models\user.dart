class User {
  final int id;
  final String email;
  final String userType;
  final bool isActive;
  final DateTime createdAt;

  User({
    required this.id,
    required this.email,
    required this.userType,
    required this.isActive,
    required this.createdAt,
  });

  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      id: json['id'] ?? 0,
      email: json['email'] ?? '',
      userType: json['userType'] ?? '',
      isActive: json['isActive'] ?? true,
      createdAt: json['createdAt'] != null
          ? DateTime.parse(json['createdAt'])
          : DateTime.now(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'email': email,
      'userType': userType,
      'isActive': isActive,
      'createdAt': createdAt.toIso8601String(),
    };
  }
}

class RestaurantProfile {
  final int? id;
  final String restaurantName;
  final String description;
  final String phone;
  final String address;
  final double? latitude;
  final double? longitude;
  final String cuisineType;
  final bool isOpen;
  final double rating;
  final int totalOrders;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  RestaurantProfile({
    this.id,
    required this.restaurantName,
    required this.description,
    required this.phone,
    required this.address,
    this.latitude,
    this.longitude,
    required this.cuisineType,
    this.isOpen = false,
    this.rating = 0.0,
    this.totalOrders = 0,
    this.createdAt,
    this.updatedAt,
  });

  factory RestaurantProfile.fromJson(Map<String, dynamic> json) {
    return RestaurantProfile(
      id: json['id'],
      restaurantName: json['restaurantName'] ?? '',
      description: json['description'] ?? '',
      phone: json['phone'] ?? '',
      address: json['address'] ?? '',
      latitude: _parseDouble(json['latitude']),
      longitude: _parseDouble(json['longitude']),
      cuisineType: json['cuisineType'] ?? '',
      isOpen: json['isOpen'] ?? false,
      rating: _parseDouble(json['rating']) ?? 0.0,
      totalOrders: json['totalOrders'] ?? 0,
      createdAt: json['createdAt'] != null
          ? DateTime.parse(json['createdAt'])
          : null,
      updatedAt: json['updatedAt'] != null
          ? DateTime.parse(json['updatedAt'])
          : null,
    );
  }

  static double? _parseDouble(dynamic value) {
    if (value == null) return null;
    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) {
      return double.tryParse(value);
    }
    return null;
  }

  Map<String, dynamic> toJson() {
    return {
      'restaurantName': restaurantName,
      'description': description,
      'phone': phone,
      'address': address,
      'latitude': latitude,
      'longitude': longitude,
      'cuisineType': cuisineType,
    };
  }

  RestaurantProfile copyWith({
    int? id,
    String? restaurantName,
    String? description,
    String? phone,
    String? address,
    double? latitude,
    double? longitude,
    String? cuisineType,
    bool? isOpen,
    double? rating,
    int? totalOrders,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return RestaurantProfile(
      id: id ?? this.id,
      restaurantName: restaurantName ?? this.restaurantName,
      description: description ?? this.description,
      phone: phone ?? this.phone,
      address: address ?? this.address,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      cuisineType: cuisineType ?? this.cuisineType,
      isOpen: isOpen ?? this.isOpen,
      rating: rating ?? this.rating,
      totalOrders: totalOrders ?? this.totalOrders,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}

class OrderItem {
  final String name;
  final String description;
  final String? imageUrl;
  final int quantity;
  final double unitPrice;
  final double totalPrice;
  final String? specialInstructions;

  OrderItem({
    required this.name,
    required this.description,
    this.imageUrl,
    required this.quantity,
    required this.unitPrice,
    required this.totalPrice,
    this.specialInstructions,
  });

  factory OrderItem.fromJson(Map<String, dynamic> json) {
    return OrderItem(
      name: json['name'],
      description: json['description'],
      imageUrl: json['imageUrl'],
      quantity: json['quantity'],
      unitPrice: (json['unitPrice'] as num).toDouble(),
      totalPrice: (json['totalPrice'] as num).toDouble(),
      specialInstructions: json['specialInstructions'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'description': description,
      'imageUrl': imageUrl,
      'quantity': quantity,
      'unitPrice': unitPrice,
      'totalPrice': totalPrice,
      'specialInstructions': specialInstructions,
    };
  }
}

class OrderDriver {
  final String name;
  final String phone;

  OrderDriver({required this.name, required this.phone});

  factory OrderDriver.fromJson(Map<String, dynamic> json) {
    return OrderDriver(name: json['name'], phone: json['phone']);
  }

  Map<String, dynamic> toJson() {
    return {'name': name, 'phone': phone};
  }
}

class Order {
  final int id;
  final String orderNumber;
  final String status;
  final double totalAmount;
  final double deliveryFee;
  final String deliveryAddress;
  final double? deliveryLatitude;
  final double? deliveryLongitude;
  final String? specialInstructions;
  final DateTime? estimatedDeliveryTime;
  final Customer customer;
  final OrderDriver? driver;
  final List<OrderItem> items;
  final DateTime createdAt;
  final DateTime updatedAt;

  Order({
    required this.id,
    required this.orderNumber,
    required this.status,
    required this.totalAmount,
    required this.deliveryFee,
    required this.deliveryAddress,
    this.deliveryLatitude,
    this.deliveryLongitude,
    this.specialInstructions,
    this.estimatedDeliveryTime,
    required this.customer,
    this.driver,
    this.items = const [],
    required this.createdAt,
    required this.updatedAt,
  });

  factory Order.fromJson(Map<String, dynamic> json) {
    return Order(
      id: json['id'],
      orderNumber: json['orderNumber'],
      status: json['status'],
      totalAmount: (json['totalAmount'] as num).toDouble(),
      deliveryFee: (json['deliveryFee'] as num).toDouble(),
      deliveryAddress: json['deliveryAddress'],
      deliveryLatitude: json['deliveryLatitude'] != null
          ? (json['deliveryLatitude'] as num).toDouble()
          : null,
      deliveryLongitude: json['deliveryLongitude'] != null
          ? (json['deliveryLongitude'] as num).toDouble()
          : null,
      specialInstructions: json['specialInstructions'],
      estimatedDeliveryTime: json['estimatedDeliveryTime'] != null
          ? DateTime.parse(json['estimatedDeliveryTime'])
          : null,
      customer: Customer.fromJson(json['customer']),
      driver: json['driver'] != null
          ? OrderDriver.fromJson(json['driver'])
          : null,
      items: json['items'] != null
          ? (json['items'] as List)
                .map((item) => OrderItem.fromJson(item))
                .toList()
          : [],
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: DateTime.parse(json['updatedAt']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'orderNumber': orderNumber,
      'status': status,
      'totalAmount': totalAmount,
      'deliveryFee': deliveryFee,
      'deliveryAddress': deliveryAddress,
      'deliveryLatitude': deliveryLatitude,
      'deliveryLongitude': deliveryLongitude,
      'specialInstructions': specialInstructions,
      'estimatedDeliveryTime': estimatedDeliveryTime?.toIso8601String(),
      'customer': customer.toJson(),
      'driver': driver?.toJson(),
      'items': items.map((item) => item.toJson()).toList(),
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }
}

class Customer {
  final String name;
  final String phone;

  Customer({required this.name, required this.phone});

  factory Customer.fromJson(Map<String, dynamic> json) {
    return Customer(name: json['name'], phone: json['phone']);
  }

  Map<String, dynamic> toJson() {
    return {'name': name, 'phone': phone};
  }
}
